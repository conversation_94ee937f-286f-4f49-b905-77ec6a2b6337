{% extends 'base.html' %}

{% block title %}菜单计划管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">菜单计划列表</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('menu_plan.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 新建菜单计划
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('menu_plan.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
                                        <option value="已发布" {% if status == '已发布' %}selected{% endif %}>已发布</option>
                                        <option value="已执行" {% if status == '已执行' %}selected{% endif %}>已执行</option>
                                        <option value="已取消" {% if status == '已取消' %}selected{% endif %}>已取消</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>餐次</label>
                                    <select name="meal_type" class="form-control">
                                        <option value="">全部</option>
                                        <option value="早餐" {% if meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>开始日期</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>结束日期</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary form-control">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>区域</th>
                                    <th>计划日期</th>
                                    <th>餐次</th>
                                    <th>计划人数</th>
                                    <th>实际人数</th>
                                    <th>状态</th>
                                    <th>创建人</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for menu_plan in menu_plans %}
                                <tr>
                                    <td>{{ menu_plan.id }}</td>
                                    <td>{{ menu_plan.area.name }}</td>
                                    <td>{{ menu_plan.plan_date }}</td>
                                    <td>{{ menu_plan.meal_type }}</td>
                                    <td>{{ menu_plan.expected_diners }}</td>
                                    <td>{{ menu_plan.actual_diners or '-' }}</td>
                                    <td>
                                        {% if menu_plan.status == '计划中' %}
                                        <span class="badge badge-warning">计划中</span>
                                        {% elif menu_plan.status == '已发布' %}
                                        <span class="badge badge-info">已发布</span>
                                        {% elif menu_plan.status == '已执行' %}
                                        <span class="badge badge-success">已执行</span>
                                        {% elif menu_plan.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ menu_plan.creator.real_name or menu_plan.creator.username }}</td>
                                    <td>{{ menu_plan.created_at }}</td>
                                    <td>
                                        <a href="{{ url_for('menu_plan.view', id=menu_plan.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        {% if menu_plan.status == '计划中' %}
                                        <a href="{{ url_for('menu_plan.edit', id=menu_plan.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        {% endif %}
                                        <a href="{{ url_for('menu_plan.print_menu', id=menu_plan.id) }}" class="btn btn-secondary btn-sm" target="_blank">
                                            <i class="fas fa-print"></i> 打印
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('menu_plan.index', page=pagination.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                    上一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">上一页</span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('menu_plan.index', page=page, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('menu_plan.index', page=pagination.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, meal_type=meal_type) }}">
                                    下一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">下一页</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

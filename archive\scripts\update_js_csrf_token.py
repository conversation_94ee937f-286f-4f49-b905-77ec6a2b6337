#!/usr/bin/env python
"""
更新JavaScript中的CSRF令牌处理
"""

import os
import re

def update_js_csrf_token(directory):
    """
    查找并替换所有HTML文件中的JavaScript CSRF令牌处理
    """
    # 查找meta标签中的CSRF令牌
    meta_pattern = re.compile(r'meta\[name=[\'"](.*?)-csrf-token[\'"]\]')
    meta_replacement = r'meta[name="csrf-token"]'
    
    # 查找表单中的CSRF令牌字段
    form_pattern = re.compile(r'name=[\'"](_csrf_token)[\'"]')
    form_replacement = r'name="csrf_token"'
    
    modified_files = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                
                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含需要替换的模式
                if meta_pattern.search(content) or form_pattern.search(content):
                    # 替换内容
                    new_content = meta_pattern.sub(meta_replacement, content)
                    new_content = form_pattern.sub(form_replacement, new_content)
                    
                    # 写回文件
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    
                    modified_files.append(file_path)
                    print(f"已修改: {file_path}")
    
    return modified_files

if __name__ == "__main__":
    templates_dir = "app/templates"
    modified = update_js_csrf_token(templates_dir)
    
    print(f"\n总共修改了 {len(modified)} 个文件")
    if modified:
        print("修改的文件列表:")
        for file in modified:
            print(f"- {file}")

{% extends 'base.html' %}

{% block title %}通知中心 - {{ super() }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>通知中心</h2>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group">
                <a href="{{ url_for('notification.mark_all_read') }}" class="btn btn-secondary">
                    <i class="fas fa-check-double"></i> 全部标为已读
                </a>
                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteAllModal">
                    <i class="fas fa-trash"></i> 清空通知
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">通知筛选</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('notification.index', type='all') }}" class="list-group-item list-group-item-action {% if filter_type == 'all' %}active{% endif %}">
                        全部通知
                        <span class="badge badge-pill badge-primary float-right">{{  notifications|length  }}</span>
                    </a>
                    <a href="{{ url_for('notification.index', type='unread') }}" class="list-group-item list-group-item-action {% if filter_type == 'unread' %}active{% endif %}">
                        未读通知
                        <span class="badge badge-pill badge-danger float-right">{{ unread_count }}</span>
                    </a>
                    <div class="list-group-item list-group-item-secondary">通知类型</div>
                    {% for type, count in type_counts.items() %}
                        <a href="{{ url_for('notification.index', type=type) }}" class="list-group-item list-group-item-action {% if filter_type == type %}active{% endif %}">
                            {{ get_notification_type_name(type) }}
                            <span class="badge badge-pill badge-primary float-right">{{ count }}</span>
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        {% if filter_type == 'all' %}
                            全部通知
                        {% elif filter_type == 'unread' %}
                            未读通知
                        {% else %}
                            {{ get_notification_type_name(filter_type) }}
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if notifications %}
                        <div class="list-group list-group-flush">
                            {% for notification in notifications %}
                                <div class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-light{% endif %}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">
                                            {% if notification.level == 2 %}
                                                <span class="badge badge-danger">紧急</span>
                                            {% elif notification.level == 1 %}
                                                <span class="badge badge-warning">重要</span>
                                            {% endif %}
                                            {{ notification.title }}
                                        </h5>
                                        <small>{{ notification.formatted_created_time }}</small>
                                    </div>
                                    <p class="mb-1">{{ notification.content }}</p>
                                    <div class="d-flex justify-content-end mt-2">
                                        <a href="{{ url_for('notification.view', id=notification.id) }}" class="btn btn-sm btn-primary mr-2">
                                            查看详情
                                        </a>
                                        {% if not notification.is_read %}
                                            <a href="{{ url_for('notification.mark_read', id=notification.id) }}" class="btn btn-sm btn-secondary mr-2">
                                                标为已读
                                            </a>
                                        {% endif %}
                                        <button type="button" class="btn btn-sm btn-danger" data-toggle="modal" data-target="#deleteModal{{ notification.id }}">
                                            删除
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ notification.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ notification.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ notification.id }}">确认删除</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <p>确定要删除这条通知吗？</p>
                                                <p><strong>{{ notification.title }}</strong></p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                <form action="{{ url_for('notification.delete', id=notification.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>
                                                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- 分页 -->
                        {% if pagination.pages > 1 %}
                            <div class="d-flex justify-content-center mt-4 mb-4">
                                <nav aria-label="通知分页">
                                    <ul class="pagination">
                                        {% if pagination.has_prev %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('notification.index', page=pagination.prev_num, type=filter_type) }}">上一页</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">上一页</span>
                                            </li>
                                        {% endif %}
                                        
                                        {% for p in pagination.iter_pages() %}
                                            {% if p %}
                                                {% if p == pagination.page %}
                                                    <li class="page-item active">
                                                        <span class="page-link">{{ p }}</span>
                                                    </li>
                                                {% else %}
                                                    <li class="page-item">
                                                        <a class="page-link" href="{{ url_for('notification.index', page=p, type=filter_type) }}">{{ p }}</a>
                                                    </li>
                                                {% endif %}
                                            {% else %}
                                                <li class="page-item disabled">
                                                    <span class="page-link">...</span>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                        
                                        {% if pagination.has_next %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('notification.index', page=pagination.next_num, type=filter_type) }}">下一页</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">下一页</span>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </nav>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="alert alert-info m-3">
                            <p class="mb-0">暂无通知</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除所有通知确认模态框 -->
<div class="modal fade" id="deleteAllModal" tabindex="-1" role="dialog" aria-labelledby="deleteAllModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteAllModalLabel">确认清空通知</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <p><i class="fas fa-exclamation-triangle"></i> 警告：</p>
                    <p>此操作将删除所有通知，且无法恢复。确定要继续吗？</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <form action="{{ url_for('notification.delete_all') }}" method="POST"><button type="submit" class="btn btn-danger">确认清空</button>
                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 辅助函数
    function get_notification_type_name(type_code) {
        const type_names = {
            'system': '系统通知',
            'health_cert': '健康证提醒',
            'menu': '食谱通知',
            'purchase': '采购通知',
            'inspection': '检查通知',
            'task': '任务通知'
        };
        return type_names[type_code] || type_code;
    }
</script>
{% endblock %}

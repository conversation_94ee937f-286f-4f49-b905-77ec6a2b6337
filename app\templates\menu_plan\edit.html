{% extends 'base.html' %}

{% block title %}编辑菜单计划{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑菜单计划</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('menu_plan.view', id=menu_plan.id) }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('menu_plan.edit', id=menu_plan.id) }}"><div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="area_id">区域</label>
                                    <input type="text" class="form-control" value="{{ menu_plan.area.name }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="plan_date">计划日期 <span class="text-danger">*</span></label>
                                    <input type="date" name="plan_date" id="plan_date" class="form-control" value="{{ menu_plan.plan_date }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="meal_type">餐次 <span class="text-danger">*</span></label>
                                    <select name="meal_type" id="meal_type" class="form-control" required>
                                        <option value="">请选择餐次</option>
                                        <option value="早餐" {% if menu_plan.meal_type == '早餐' %}selected{% endif %}>早餐</option>
                                        <option value="午餐" {% if menu_plan.meal_type == '午餐' %}selected{% endif %}>午餐</option>
                                        <option value="晚餐" {% if menu_plan.meal_type == '晚餐' %}selected{% endif %}>晚餐</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expected_diners">预计用餐人数</label>
                                    <input type="number" name="expected_diners" id="expected_diners" class="form-control" min="1" value="{{ menu_plan.expected_diners or '' }}">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3">{{ menu_plan.notes or '' }}</textarea>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">选择食谱</h4>
                                <div class="card-tools">
                                    <button type="button" class="btn btn-primary btn-sm" id="addRecipeBtn">
                                        <i class="fas fa-plus"></i> 添加食谱
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="recipeContainer">
                                    <!-- 现有食谱项 -->
                                    {% for menu_recipe in menu_recipes %}
                                    <div class="recipe-item card mb-3">
                                        <div class="card-header">
                                            <div class="row">
                                                <div class="col-10">
                                                    <h5 class="recipe-title">{{ menu_recipe.recipe.name }}</h5>
                                                </div>
                                                <div class="col-2 text-right">
                                                    <button type="button" class="btn btn-danger btn-sm remove-recipe-btn">
                                                        <i class="fas fa-trash"></i> 移除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group">
                                                        <label>食谱 <span class="text-danger">*</span></label>
                                                        <select name="recipe_ids[]" class="form-control recipe-select" required>
                                                            <option value="">请选择食谱</option>
                                                            {% for recipe in recipes %}
                                                            <option value="{{ recipe.id }}" data-name="{{ recipe.name }}" {% if recipe.id == menu_recipe.recipe_id %}selected{% endif %}>{{ recipe.name }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>计划数量 <span class="text-danger">*</span></label>
                                                        <input type="number" name="quantities[]" class="form-control" min="1" step="0.1" value="{{ menu_recipe.planned_quantity }}" required>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <a href="{{ url_for('menu_plan.view', id=menu_plan.id) }}" class="btn btn-default">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 食谱项模板 -->
<template id="recipeItemTemplate">
    <div class="recipe-item card mb-3">
        <div class="card-header">
            <div class="row">
                <div class="col-10">
                    <h5 class="recipe-title">食谱项</h5>
                </div>
                <div class="col-2 text-right">
                    <button type="button" class="btn btn-danger btn-sm remove-recipe-btn">
                        <i class="fas fa-trash"></i> 移除
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label>食谱 <span class="text-danger">*</span></label>
                        <select name="recipe_ids[]" class="form-control recipe-select" required>
                            <option value="">请选择食谱</option>
                            {% for recipe in recipes %}
                            <option value="{{ recipe.id }}" data-name="{{ recipe.name }}">{{ recipe.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>计划数量 <span class="text-danger">*</span></label>
                        <input type="number" name="quantities[]" class="form-control" min="1" step="0.1" required>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script>
    $(document).ready(function() {
        // 初始化Select2
        $('.select2').select2({
            theme: 'bootstrap4'
        });

        // 初始化已有的Select2
        $('.recipe-select').select2({
            theme: 'bootstrap4'
        });

        // 添加食谱按钮点击事件
        $('#addRecipeBtn').click(function() {
            addRecipeItem();
        });

        // 如果没有食谱项，添加一个空的食谱项
        if ($('.recipe-item').length === 0) {
            addRecipeItem();
        }

        // 动态绑定移除食谱按钮点击事件
        $(document).on('click', '.remove-recipe-btn', function() {
            // 如果只有一个食谱项，不允许删除
            if ($('.recipe-item').length > 1) {
                $(this).closest('.recipe-item').remove();
            } else {
                alert('至少需要一个食谱项');
            }
        });

        // 动态绑定食谱选择变化事件
        $(document).on('change', '.recipe-select', function() {
            var selectedOption = $(this).find('option:selected');
            var recipeName = selectedOption.data('name');
            $(this).closest('.recipe-item').find('.recipe-title').text(recipeName || '食谱项');
        });

        // 添加食谱项函数
        function addRecipeItem() {
            var template = document.getElementById('recipeItemTemplate');
            var clone = document.importNode(template.content, true);
            $('#recipeContainer').append(clone);

            // 初始化新添加的Select2
            $('.recipe-select').last().select2({
                theme: 'bootstrap4'
            });
        }
    });
</script>
{% endblock %}

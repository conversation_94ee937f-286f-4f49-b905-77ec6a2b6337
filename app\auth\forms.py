from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User, AdministrativeArea

class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(message='请输入用户名')])
    password = PasswordField('密码', validators=[DataRequired(message='请输入密码')])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class RegisterForm(FlaskForm):
    school_name = StringField('学校名称', validators=[
        DataRequired(message='请输入学校名称'),
        Length(min=2, max=50, message='学校名称长度必须在2-50个字符之间')
    ])
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度必须在3-20个字符之间')
    ])
    email = StringField('电子邮箱', validators=[
        DataRequired(message='请输入电子邮箱'),
        Email(message='请输入有效的电子邮箱地址')
    ])
    real_name = StringField('真实姓名', validators=[
        DataRequired(message='请输入真实姓名'),
        Length(min=2, max=20, message='姓名长度必须在2-20个字符之间')
    ])
    phone = StringField('手机号码', validators=[
        DataRequired(message='请输入手机号码'),
        Length(min=11, max=11, message='请输入11位手机号码')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码'),
        Length(min=6, message='密码长度不能少于6个字符')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(message='请再次输入密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('创建学校并注册')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('该用户名已被使用，请更换一个')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('该邮箱已被注册，请更换一个')

    def validate_school_name(self, school_name):
        school = AdministrativeArea.query.filter_by(name=school_name.data, level=3).first()
        if school is not None:
            raise ValidationError('该学校名称已被使用，请更换一个')

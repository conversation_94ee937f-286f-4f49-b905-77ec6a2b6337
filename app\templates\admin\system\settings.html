{% extends 'base.html' %}

{% block title %}系统设置 - {{ super() }}{% endblock %}

{% block styles %}
<style>
    .settings-card {
        margin-bottom: 20px;
    }
    .settings-card .card-header {
        font-weight: bold;
    }
    .form-group label {
        font-weight: 500;
    }
    .setting-description {
        font-size: 0.85rem;
        color: #6c757d;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>系统设置</h2>
        <p class="text-muted">配置系统的基本参数和行为</p>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('system.backups') }}" class="btn btn-info">
            <i class="fas fa-database"></i> 数据库备份
        </a>
        <a href="{{ url_for('system.monitor') }}" class="btn btn-primary">
            <i class="fas fa-chart-line"></i> 系统监控
        </a>
        <a href="{{ url_for('system.users') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回
        </a>
    </div>
</div>

<form method="post" action="{{ url_for('system.update_settings') }}">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

    <div class="row">
        <div class="col-md-3">
            <div class="list-group" id="settings-tab" role="tablist">
                {% for category in settings.keys() %}
                <a class="list-group-item list-group-item-action {% if loop.first %}active{% endif %}"
                   id="{{ category|lower|replace(' ', '-') }}-tab"
                   data-toggle="list"
                   href="#{{ category|lower|replace(' ', '-') }}"
                   role="tab">
                    {{ category }}
                </a>
                {% endfor %}
            </div>
        </div>

        <div class="col-md-9">
            <div class="tab-content">
                {% for category, category_settings in settings.items() %}
                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                     id="{{ category|lower|replace(' ', '-') }}"
                     role="tabpanel">

                    <div class="card settings-card">
                        <div class="card-header bg-primary text-white">
                            {{ category }}
                        </div>
                        <div class="card-body">
                            {% for setting in category_settings %}
                            <div class="form-group">
                                <label for="setting_{{ setting.key }}">{{ setting.description }}</label>

                                {% if setting.key == 'project_name' %}
                                <input type="text" class="form-control" id="setting_{{ setting.key }}"
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'theme_color' %}
                                <select class="form-control theme-color-selector" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}" onchange="previewTheme(this.value)">
                                    <option value="primary" {% if setting.value == 'primary' %}selected{% endif %}>
                                        <span class="theme-preview primary"></span>蓝色 (Primary)
                                    </option>
                                    <option value="secondary" {% if setting.value == 'secondary' %}selected{% endif %}>
                                        <span class="theme-preview secondary"></span>灰色 (Secondary)
                                    </option>
                                    <option value="success" {% if setting.value == 'success' %}selected{% endif %}>
                                        <span class="theme-preview success"></span>绿色 (Success)
                                    </option>
                                    <option value="danger" {% if setting.value == 'danger' %}selected{% endif %}>
                                        <span class="theme-preview danger"></span>红色 (Danger)
                                    </option>
                                    <option value="warning" {% if setting.value == 'warning' %}selected{% endif %}>
                                        <span class="theme-preview warning"></span>黄色 (Warning)
                                    </option>
                                    <option value="info" {% if setting.value == 'info' %}selected{% endif %}>
                                        <span class="theme-preview info"></span>浅蓝色 (Info)
                                    </option>
                                    <option value="dark" {% if setting.value == 'dark' %}selected{% endif %}>
                                        <span class="theme-preview dark"></span>深色 (Dark)
                                    </option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        选择主题颜色后会实时预览效果，保存后全站生效
                                    </small>
                                </div>

                                {% elif setting.key == 'show_welcome_message' or setting.key.startswith('enable_') %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="1" {% if setting.value == '1' %}selected{% endif %}>是</option>
                                    <option value="0" {% if setting.value == '0' %}selected{% endif %}>否</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'items_per_page' %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="10" {% if setting.value == '10' %}selected{% endif %}>10</option>
                                    <option value="20" {% if setting.value == '20' %}selected{% endif %}>20</option>
                                    <option value="50" {% if setting.value == '50' %}selected{% endif %}>50</option>
                                    <option value="100" {% if setting.value == '100' %}selected{% endif %}>100</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% elif setting.key == 'session_timeout' %}
                                <select class="form-control" id="setting_{{ setting.key }}"
                                        name="setting_{{ setting.key }}">
                                    <option value="1" {% if setting.value == '1' %}selected{% endif %}>1天</option>
                                    <option value="3" {% if setting.value == '3' %}selected{% endif %}>3天</option>
                                    <option value="7" {% if setting.value == '7' %}selected{% endif %}>7天</option>
                                    <option value="14" {% if setting.value == '14' %}selected{% endif %}>14天</option>
                                    <option value="30" {% if setting.value == '30' %}selected{% endif %}>30天</option>
                                </select>
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">

                                {% else %}
                                <input type="text" class="form-control" id="setting_{{ setting.key }}"
                                       name="setting_{{ setting.key }}" value="{{ setting.value }}">
                                <input type="hidden" name="value_type_{{ setting.key }}" value="{{ setting.value_type }}">
                                {% endif %}

                                <div class="setting-description">{{ setting.description }}</div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-12 text-right">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> 保存设置
            </button>
        </div>
    </div>
</form>
{% endblock %}

{% block scripts %}
<script>
// 主题预览功能
function previewTheme(themeName) {
    if (window.themeSwitcher) {
        // 使用主题切换器的预览功能
        window.themeSwitcher.previewTheme(themeName);

        // 显示预览提示
        showThemePreviewToast(themeName);
    } else {
        // 如果主题切换器还没加载，直接应用主题
        document.documentElement.setAttribute('data-theme', themeName);
    }
}

// 显示主题预览提示
function showThemePreviewToast(themeName) {
    const themes = {
        'primary': '蓝色',
        'secondary': '灰色',
        'success': '绿色',
        'danger': '红色',
        'warning': '黄色',
        'info': '浅蓝色',
        'dark': '深色'
    };

    const themeName_zh = themes[themeName] || themeName;

    // 使用toastr显示提示
    if (typeof toastr !== 'undefined') {
        toastr.info(`正在预览 ${themeName_zh} 主题，3秒后恢复原主题`, '主题预览', {
            timeOut: 3000,
            progressBar: true
        });
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待主题切换器加载完成
    setTimeout(function() {
        // 如果主题切换器已加载，同步当前选择的主题
        if (window.themeSwitcher) {
            const themeSelector = document.getElementById('setting_theme_color');
            if (themeSelector && themeSelector.value) {
                window.themeSwitcher.applyTheme(themeSelector.value);
            }
        }
    }, 500);

    // 监听主题选择器变化
    const themeSelector = document.getElementById('setting_theme_color');
    if (themeSelector) {
        themeSelector.addEventListener('change', function(e) {
            const selectedTheme = e.target.value;

            // 立即应用主题（不是预览）
            if (window.themeSwitcher) {
                window.themeSwitcher.applyTheme(selectedTheme);
            } else {
                document.documentElement.setAttribute('data-theme', selectedTheme);
            }

            // 显示应用提示
            if (typeof toastr !== 'undefined') {
                const themes = {
                    'primary': '蓝色',
                    'secondary': '灰色',
                    'success': '绿色',
                    'danger': '红色',
                    'warning': '黄色',
                    'info': '浅蓝色',
                    'dark': '深色'
                };
                const themeName_zh = themes[selectedTheme] || selectedTheme;
                toastr.success(`已切换到 ${themeName_zh} 主题`, '主题切换');
            }
        });
    }
});

// 表单提交时的处理
document.addEventListener('DOMContentLoaded', function() {
    const settingsForm = document.querySelector('form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            // 表单提交时显示保存提示
            if (typeof toastr !== 'undefined') {
                toastr.info('正在保存设置...', '系统设置');
            }
        });
    }
});
</script>
{% endblock %}
{"version": 3, "file": "scrollspy.js", "sources": ["../src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_ITEMS = '.dropdown-item'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS},` +\n                          `${this._config.target} ${SELECTOR_LIST_ITEMS},` +\n                          `${this._config.target} ${SELECTOR_DROPDOWN_ITEMS}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    $(this._scrollElement).on(EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET : METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map(element => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n\n        return null\n      })\n      .filter(Boolean)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && Util.isElement(config.target)) {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset + scrollHeight - this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(CLASS_NAME_DROPDOWN_ITEM)) {\n      $link.closest(SELECTOR_DROPDOWN)\n        .find(SELECTOR_DROPDOWN_TOGGLE)\n        .addClass(CLASS_NAME_ACTIVE)\n      $link.addClass(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(CLASS_NAME_ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(`${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n        .addClass(CLASS_NAME_ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(SELECTOR_NAV_LIST_GROUP)\n        .prev(SELECTOR_NAV_ITEMS)\n        .children(SELECTOR_NAV_LINKS)\n        .addClass(CLASS_NAME_ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(SELECTOR_DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "EVENT_ACTIVATE", "EVENT_SCROLL", "EVENT_LOAD_DATA_API", "METHOD_OFFSET", "METHOD_POSITION", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_ITEMS", "SELECTOR_DROPDOWN_TOGGLE", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "ScrollSpy", "element", "config", "_element", "_scrollElement", "tagName", "window", "_config", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "on", "event", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "slice", "call", "document", "querySelectorAll", "map", "targetSelector", "<PERSON><PERSON>", "getSelectorFromElement", "querySelector", "targetBCR", "getBoundingClientRect", "width", "height", "top", "filter", "Boolean", "sort", "a", "b", "for<PERSON>ach", "item", "push", "dispose", "removeData", "off", "isElement", "id", "attr", "getUID", "typeCheckConfig", "pageYOffset", "scrollTop", "scrollHeight", "Math", "max", "body", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "length", "_activate", "_clear", "i", "isActiveTarget", "queries", "split", "selector", "$link", "join", "hasClass", "closest", "find", "addClass", "parents", "prev", "children", "trigger", "relatedTarget", "node", "classList", "contains", "remove", "_jQueryInterface", "each", "data", "TypeError", "scrollSpys", "scrollSpysLength", "$spy", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,WAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,cAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EAEA,IAAMQ,wBAAwB,GAAG,eAAjC,CAAA;EACA,IAAMC,iBAAiB,GAAG,QAA1B,CAAA;EAEA,IAAMC,cAAc,gBAAcP,SAAlC,CAAA;EACA,IAAMQ,YAAY,cAAYR,SAA9B,CAAA;EACA,IAAMS,mBAAmB,GAAA,MAAA,GAAUT,SAAV,GAAsBC,YAA/C,CAAA;EAEA,IAAMS,aAAa,GAAG,QAAtB,CAAA;EACA,IAAMC,eAAe,GAAG,UAAxB,CAAA;EAEA,IAAMC,iBAAiB,GAAG,qBAA1B,CAAA;EACA,IAAMC,uBAAuB,GAAG,mBAAhC,CAAA;EACA,IAAMC,kBAAkB,GAAG,WAA3B,CAAA;EACA,IAAMC,kBAAkB,GAAG,WAA3B,CAAA;EACA,IAAMC,mBAAmB,GAAG,kBAA5B,CAAA;EACA,IAAMC,iBAAiB,GAAG,WAA1B,CAAA;EACA,IAAMC,uBAAuB,GAAG,gBAAhC,CAAA;EACA,IAAMC,wBAAwB,GAAG,kBAAjC,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,EADM;EAEdC,EAAAA,MAAM,EAAE,MAFM;EAGdC,EAAAA,MAAM,EAAE,EAAA;EAHM,CAAhB,CAAA;EAMA,IAAMC,WAAW,GAAG;EAClBH,EAAAA,MAAM,EAAE,QADU;EAElBC,EAAAA,MAAM,EAAE,QAFU;EAGlBC,EAAAA,MAAM,EAAE,kBAAA;EAHU,CAApB,CAAA;EAMA;EACA;EACA;;MAEME;IACJ,SAAYC,SAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;MAC3B,IAAKC,CAAAA,QAAL,GAAgBF,OAAhB,CAAA;MACA,IAAKG,CAAAA,cAAL,GAAsBH,OAAO,CAACI,OAAR,KAAoB,MAApB,GAA6BC,MAA7B,GAAsCL,OAA5D,CAAA;EACA,IAAA,IAAA,CAAKM,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBN,MAAhB,CAAf,CAAA;MACA,IAAKO,CAAAA,SAAL,GAAoB,IAAKF,CAAAA,OAAL,CAAaT,MAAhB,GAAA,GAAA,GAA0BT,kBAA1B,GAAA,GAAA,IACQ,IAAKkB,CAAAA,OAAL,CAAaT,MADrB,GAAA,GAAA,GAC+BP,mBAD/B,GAEQ,GAAA,CAAA,IAAA,IAAA,CAAKgB,OAAL,CAAaT,MAFrB,GAE+BL,GAAAA,GAAAA,uBAF/B,CAAjB,CAAA;MAGA,IAAKiB,CAAAA,QAAL,GAAgB,EAAhB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,EAAhB,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqB,IAArB,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqB,CAArB,CAAA;MAEAnC,qBAAC,CAAC,IAAK0B,CAAAA,cAAN,CAAD,CAAuBU,EAAvB,CAA0B/B,YAA1B,EAAwC,UAAAgC,KAAK,EAAA;EAAA,MAAA,OAAI,KAAI,CAACC,QAAL,CAAcD,KAAd,CAAJ,CAAA;OAA7C,CAAA,CAAA;EAEA,IAAA,IAAA,CAAKE,OAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKD,QAAL,EAAA,CAAA;EACD;;;;;EAWD;EACAC,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACR,IAAA,IAAMC,UAAU,GAAG,IAAKd,CAAAA,cAAL,KAAwB,IAAA,CAAKA,cAAL,CAAoBE,MAA5C,GACjBrB,aADiB,GACDC,eADlB,CAAA;EAGA,IAAA,IAAMiC,YAAY,GAAG,IAAKZ,CAAAA,OAAL,CAAaV,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GACN,IAAKX,CAAAA,OAAL,CAAaV,MAD5B,CAAA;MAGA,IAAMuB,UAAU,GAAGD,YAAY,KAAKjC,eAAjB,GACjB,IAAKmC,CAAAA,aAAL,EADiB,GACM,CADzB,CAAA;MAGA,IAAKX,CAAAA,QAAL,GAAgB,EAAhB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,EAAhB,CAAA;EAEA,IAAA,IAAA,CAAKE,aAAL,GAAqB,IAAKS,CAAAA,gBAAL,EAArB,CAAA;EAEA,IAAA,IAAMC,OAAO,GAAG,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,IAAKlB,CAAAA,SAA/B,CAAd,CAAhB,CAAA;EAEAc,IAAAA,OAAO,CACJK,GADH,CACO,UAAA3B,OAAO,EAAI;EACd,MAAA,IAAIH,MAAJ,CAAA;EACA,MAAA,IAAM+B,cAAc,GAAGC,wBAAI,CAACC,sBAAL,CAA4B9B,OAA5B,CAAvB,CAAA;;EAEA,MAAA,IAAI4B,cAAJ,EAAoB;EAClB/B,QAAAA,MAAM,GAAG4B,QAAQ,CAACM,aAAT,CAAuBH,cAAvB,CAAT,CAAA;EACD,OAAA;;EAED,MAAA,IAAI/B,MAAJ,EAAY;EACV,QAAA,IAAMmC,SAAS,GAAGnC,MAAM,CAACoC,qBAAP,EAAlB,CAAA;;EACA,QAAA,IAAID,SAAS,CAACE,KAAV,IAAmBF,SAAS,CAACG,MAAjC,EAAyC;EACvC;EACA,UAAA,OAAO,CACL1D,qBAAC,CAACoB,MAAD,CAAD,CAAUqB,YAAV,CAAA,EAAA,CAA0BkB,GAA1B,GAAgCjB,UAD3B,EAELS,cAFK,CAAP,CAAA;EAID,SAAA;EACF,OAAA;;EAED,MAAA,OAAO,IAAP,CAAA;OApBJ,CAAA,CAsBGS,MAtBH,CAsBUC,OAtBV,CAAA,CAuBGC,IAvBH,CAuBQ,UAACC,CAAD,EAAIC,CAAJ,EAAA;QAAA,OAAUD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAAlB,CAAA;EAAA,KAvBR,CAwBGC,CAAAA,OAxBH,CAwBW,UAAAC,IAAI,EAAI;QACf,MAAI,CAAClC,QAAL,CAAcmC,IAAd,CAAmBD,IAAI,CAAC,CAAD,CAAvB,CAAA,CAAA;;QACA,MAAI,CAACjC,QAAL,CAAckC,IAAd,CAAmBD,IAAI,CAAC,CAAD,CAAvB,CAAA,CAAA;OA1BJ,CAAA,CAAA;;;EA8BFE,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACRpE,IAAAA,qBAAC,CAACqE,UAAF,CAAa,IAAK5C,CAAAA,QAAlB,EAA4B7B,QAA5B,CAAA,CAAA;EACAI,IAAAA,qBAAC,CAAC,IAAK0B,CAAAA,cAAN,CAAD,CAAuB4C,GAAvB,CAA2BzE,SAA3B,CAAA,CAAA;MAEA,IAAK4B,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,IAAtB,CAAA;MACA,IAAKG,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKE,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqB,IAArB,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqB,IAArB,CAAA;EACD;;;WAGDL,aAAA,SAAWN,UAAAA,CAAAA,MAAX,EAAmB;EACjBA,IAAAA,MAAM,GACDP,QAAAA,CAAAA,EAAAA,EAAAA,OADC,EAEA,OAAOO,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN,CAAA;;EAKA,IAAA,IAAI,OAAOA,MAAM,CAACJ,MAAd,KAAyB,QAAzB,IAAqCgC,wBAAI,CAACmB,SAAL,CAAe/C,MAAM,CAACJ,MAAtB,CAAzC,EAAwE;EACtE,MAAA,IAAIoD,EAAE,GAAGxE,qBAAC,CAACwB,MAAM,CAACJ,MAAR,CAAD,CAAiBqD,IAAjB,CAAsB,IAAtB,CAAT,CAAA;;QACA,IAAI,CAACD,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAGpB,wBAAI,CAACsB,MAAL,CAAYhF,IAAZ,CAAL,CAAA;UACAM,qBAAC,CAACwB,MAAM,CAACJ,MAAR,CAAD,CAAiBqD,IAAjB,CAAsB,IAAtB,EAA4BD,EAA5B,CAAA,CAAA;EACD,OAAA;;QAEDhD,MAAM,CAACJ,MAAP,GAAA,GAAA,GAAoBoD,EAApB,CAAA;EACD,KAAA;;EAEDpB,IAAAA,wBAAI,CAACuB,eAAL,CAAqBjF,IAArB,EAA2B8B,MAA3B,EAAmCH,WAAnC,CAAA,CAAA;EAEA,IAAA,OAAOG,MAAP,CAAA;;;EAGFmB,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,OAAO,IAAKjB,CAAAA,cAAL,KAAwBE,MAAxB,GACL,IAAA,CAAKF,cAAL,CAAoBkD,WADf,GAC6B,IAAKlD,CAAAA,cAAL,CAAoBmD,SADxD,CAAA;;;EAIFjC,EAAAA,MAAAA,CAAAA,mBAAA,SAAmB,gBAAA,GAAA;MACjB,OAAO,IAAA,CAAKlB,cAAL,CAAoBoD,YAApB,IAAoCC,IAAI,CAACC,GAAL,CACzChC,QAAQ,CAACiC,IAAT,CAAcH,YAD2B,EAEzC9B,QAAQ,CAACkC,eAAT,CAAyBJ,YAFgB,CAA3C,CAAA;;;EAMFK,EAAAA,MAAAA,CAAAA,mBAAA,SAAmB,gBAAA,GAAA;EACjB,IAAA,OAAO,IAAKzD,CAAAA,cAAL,KAAwBE,MAAxB,GACLA,MAAM,CAACwD,WADF,GACgB,IAAK1D,CAAAA,cAAL,CAAoB8B,qBAApB,GAA4CE,MADnE,CAAA;;;EAIFpB,EAAAA,MAAAA,CAAAA,WAAA,SAAW,QAAA,GAAA;MACT,IAAMuC,SAAS,GAAG,IAAKlC,CAAAA,aAAL,KAAuB,IAAKd,CAAAA,OAAL,CAAaX,MAAtD,CAAA;;EACA,IAAA,IAAM4D,YAAY,GAAG,IAAKlC,CAAAA,gBAAL,EAArB,CAAA;;MACA,IAAMyC,SAAS,GAAG,IAAA,CAAKxD,OAAL,CAAaX,MAAb,GAAsB4D,YAAtB,GAAqC,IAAKK,CAAAA,gBAAL,EAAvD,CAAA;;EAEA,IAAA,IAAI,IAAKhD,CAAAA,aAAL,KAAuB2C,YAA3B,EAAyC;EACvC,MAAA,IAAA,CAAKvC,OAAL,EAAA,CAAA;EACD,KAAA;;MAED,IAAIsC,SAAS,IAAIQ,SAAjB,EAA4B;QAC1B,IAAMjE,MAAM,GAAG,IAAA,CAAKa,QAAL,CAAc,IAAKA,CAAAA,QAAL,CAAcqD,MAAd,GAAuB,CAArC,CAAf,CAAA;;EAEA,MAAA,IAAI,IAAKpD,CAAAA,aAAL,KAAuBd,MAA3B,EAAmC;UACjC,IAAKmE,CAAAA,SAAL,CAAenE,MAAf,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,KAAKc,aAAL,IAAsB2C,SAAS,GAAG,KAAK7C,QAAL,CAAc,CAAd,CAAlC,IAAsD,IAAKA,CAAAA,QAAL,CAAc,CAAd,CAAA,GAAmB,CAA7E,EAAgF;QAC9E,IAAKE,CAAAA,aAAL,GAAqB,IAArB,CAAA;;EACA,MAAA,IAAA,CAAKsD,MAAL,EAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;MAED,KAAK,IAAIC,CAAC,GAAG,IAAKzD,CAAAA,QAAL,CAAcsD,MAA3B,EAAmCG,CAAC,EAApC,GAAyC;EACvC,MAAA,IAAMC,cAAc,GAAG,IAAA,CAAKxD,aAAL,KAAuB,KAAKD,QAAL,CAAcwD,CAAd,CAAvB,IACnBZ,SAAS,IAAI,KAAK7C,QAAL,CAAcyD,CAAd,CADM,KAElB,OAAO,IAAA,CAAKzD,QAAL,CAAcyD,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACGZ,SAAS,GAAG,KAAK7C,QAAL,CAAcyD,CAAC,GAAG,CAAlB,CAHG,CAAvB,CAAA;;EAKA,MAAA,IAAIC,cAAJ,EAAoB;EAClB,QAAA,IAAA,CAAKH,SAAL,CAAe,IAAA,CAAKtD,QAAL,CAAcwD,CAAd,CAAf,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;;WAGHF,YAAA,SAAUnE,SAAAA,CAAAA,MAAV,EAAkB;MAChB,IAAKc,CAAAA,aAAL,GAAqBd,MAArB,CAAA;;EAEA,IAAA,IAAA,CAAKoE,MAAL,EAAA,CAAA;;EAEA,IAAA,IAAMG,OAAO,GAAG,IAAK5D,CAAAA,SAAL,CACb6D,KADa,CACP,GADO,CAEb1C,CAAAA,GAFa,CAET,UAAA2C,QAAQ,EAAA;EAAA,MAAA,OAAOA,QAAP,GAAgCzE,iBAAAA,GAAAA,MAAhC,GAA4CyE,MAAAA,GAAAA,QAA5C,gBAA8DzE,MAA9D,GAAA,KAAA,CAAA;EAAA,KAFC,CAAhB,CAAA;;MAIA,IAAM0E,KAAK,GAAG9F,qBAAC,CAAC,GAAG8C,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B0C,OAAO,CAACI,IAAR,CAAa,GAAb,CAA1B,CAAd,CAAD,CAAf,CAAA;;EAEA,IAAA,IAAID,KAAK,CAACE,QAAN,CAAe9F,wBAAf,CAAJ,EAA8C;QAC5C4F,KAAK,CAACG,OAAN,CAAcnF,iBAAd,CAAA,CACGoF,IADH,CACQlF,wBADR,CAAA,CAEGmF,QAFH,CAEYhG,iBAFZ,CAAA,CAAA;QAGA2F,KAAK,CAACK,QAAN,CAAehG,iBAAf,CAAA,CAAA;EACD,KALD,MAKO;EACL;EACA2F,MAAAA,KAAK,CAACK,QAAN,CAAehG,iBAAf,EAFK;EAIL;;EACA2F,MAAAA,KAAK,CAACM,OAAN,CAAc1F,uBAAd,EACG2F,IADH,CACW1F,kBADX,GAAA,IAAA,GACkCE,mBADlC,CAEGsF,CAAAA,QAFH,CAEYhG,iBAFZ,EALK;;EASL2F,MAAAA,KAAK,CAACM,OAAN,CAAc1F,uBAAd,EACG2F,IADH,CACQzF,kBADR,CAAA,CAEG0F,QAFH,CAEY3F,kBAFZ,CAGGwF,CAAAA,QAHH,CAGYhG,iBAHZ,CAAA,CAAA;EAID,KAAA;;MAEDH,qBAAC,CAAC,KAAK0B,cAAN,CAAD,CAAuB6E,OAAvB,CAA+BnG,cAA/B,EAA+C;EAC7CoG,MAAAA,aAAa,EAAEpF,MAAAA;OADjB,CAAA,CAAA;;;EAKFoE,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;EACP,IAAA,EAAA,CAAG1C,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,IAAA,CAAKlB,SAA/B,CAAd,CAAA,CACG6B,MADH,CACU,UAAA6C,IAAI,EAAA;EAAA,MAAA,OAAIA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBxG,iBAAxB,CAAJ,CAAA;EAAA,KADd,CAEG8D,CAAAA,OAFH,CAEW,UAAAwC,IAAI,EAAA;EAAA,MAAA,OAAIA,IAAI,CAACC,SAAL,CAAeE,MAAf,CAAsBzG,iBAAtB,CAAJ,CAAA;OAFf,CAAA,CAAA;EAGD;;;cAGM0G,mBAAP,SAAwBrF,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKsF,IAAL,CAAU,YAAY;QAC3B,IAAIC,IAAI,GAAG/G,qBAAC,CAAC,IAAD,CAAD,CAAQ+G,IAAR,CAAanH,QAAb,CAAX,CAAA;;EACA,MAAA,IAAMiC,OAAO,GAAG,OAAOL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C,CAAA;;QAEA,IAAI,CAACuF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIzF,SAAJ,CAAc,IAAd,EAAoBO,OAApB,CAAP,CAAA;UACA7B,qBAAC,CAAC,IAAD,CAAD,CAAQ+G,IAAR,CAAanH,QAAb,EAAuBmH,IAAvB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOvF,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOuF,IAAI,CAACvF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIwF,SAAJ,CAAkCxF,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDuF,IAAI,CAACvF,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAhBM,CAAP,CAAA;;;;;WA9LF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAO7B,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOsB,OAAP,CAAA;EACD,KAAA;;;;;EA4MH;EACA;EACA;;;AAEAjB,uBAAC,CAAC4B,MAAD,CAAD,CAAUQ,EAAV,CAAa9B,mBAAb,EAAkC,YAAM;EACtC,EAAA,IAAM2G,UAAU,GAAG,EAAGnE,CAAAA,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BxC,iBAA1B,CAAd,CAAnB,CAAA;EACA,EAAA,IAAMyG,gBAAgB,GAAGD,UAAU,CAAC3B,MAApC,CAAA;;EAEA,EAAA,KAAK,IAAIG,CAAC,GAAGyB,gBAAb,EAA+BzB,CAAC,EAAhC,GAAqC;MACnC,IAAM0B,IAAI,GAAGnH,qBAAC,CAACiH,UAAU,CAACxB,CAAD,CAAX,CAAd,CAAA;;MACAnE,SAAS,CAACuF,gBAAV,CAA2B9D,IAA3B,CAAgCoE,IAAhC,EAAsCA,IAAI,CAACJ,IAAL,EAAtC,CAAA,CAAA;EACD,GAAA;EACF,CARD,CAAA,CAAA;EAUA;EACA;EACA;;AAEA/G,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAa4B,GAAAA,SAAS,CAACuF,gBAAvB,CAAA;AACA7G,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW0H,CAAAA,WAAX,GAAyB9F,SAAzB,CAAA;;AACAtB,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW2H,CAAAA,UAAX,GAAwB,YAAM;EAC5BrH,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOuB,SAAS,CAACuF,gBAAjB,CAAA;EACD,CAHD;;;;;;;;"}
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="../../../dist/css/bootstrap.min.css">
    <title>Button</title>
  </head>
  <body>
    <div class="container">
      <h1>Button <small>Bootstrap Visual Test</small></h1>

      <button type="button" class="btn btn-primary" data-toggle="button" aria-pressed="false">
        Single toggle
      </button>

      <p>For checkboxes and radio buttons, ensure that keyboard behavior is functioning correctly.</p>
      <p>Navigate to the checkboxes with the keyboard (generally, using <kbd>TAB</kbd> / <kbd>SHIFT + TAB</kbd>), and ensure that <kbd>SPACE</kbd> toggles the currently focused checkbox. Click on one of the checkboxes using the mouse, ensure that focus was correctly set on the actual checkbox, and that <kbd>SPACE</kbd> toggles the checkbox again.</p>

      <div class="btn-group" data-toggle="buttons">
        <label class="btn btn-primary active">
          <input type="checkbox" checked> Checkbox 1 (pre-checked)
        </label>
        <label class="btn btn-primary">
          <input type="checkbox"> Checkbox 2
        </label>
        <label class="btn btn-primary">
          <input type="checkbox"> Checkbox 3
        </label>
      </div>

      <p>Navigate to the radio button group with the keyboard (generally, using <kbd>TAB</kbd> / <kbd>SHIFT + TAB</kbd>). If no radio button was initially set to be selected, the first/last radio button should receive focus (depending on whether you navigated "forward" to the group with <kbd>TAB</kbd> or "backwards" using <kbd>SHIFT + TAB</kbd>). If a radio button was already selected, navigating with the keyboard should set focus to that particular radio button. Only one radio button in a group should receive focus at any given time.  Ensure that the selected radio button can be changed by using the <kbd>←</kbd> and <kbd>→</kbd> arrow keys. Click on one of the radio buttons with the mouse,  ensure that focus was correctly set on the actual radio button, and that <kbd>←</kbd> and <kbd>→</kbd> change the selected radio button again.</p>

      <div class="btn-group" data-toggle="buttons">
        <label class="btn btn-primary active">
          <input type="radio" name="options" id="option1" checked> Radio 1 (preselected)
        </label>
        <label class="btn btn-primary">
          <input type="radio" name="options" id="option2"> Radio 2
        </label>
        <label class="btn btn-primary">
          <input type="radio" name="options" id="option3"> Radio 3
        </label>
      </div>
    </div>

    <script src="../../../node_modules/jquery/dist/jquery.slim.min.js"></script>
    <script src="../../dist/util.js"></script>
    <script src="../../dist/button.js"></script>
  </body>
</html>

{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2>{{ title }}</h2>
        <p class="text-muted">为员工 {{ employee.name }} 添加体检记录</p>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" novalidate>
            {{ form.hidden_tag() }}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.exam_date.label }}
                        {{ form.exam_date(class="form-control", type="date") }}
                        {% for error in form.exam_date.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.exam_hospital.label }}
                        {{ form.exam_hospital(class="form-control") }}
                        {% for error in form.exam_hospital.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.result.label }}
                        {{ form.result(class="form-control") }}
                        {% for error in form.result.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.report_img.label }}
                        {{ form.report_img(class="form-control-file") }}
                        {% for error in form.report_img.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=3) }}
                {% for error in form.notes.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>
            
            <div class="form-group text-center">
                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

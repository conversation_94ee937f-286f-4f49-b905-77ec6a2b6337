<!doctype html>
<html lang="en">
  <head>
    {{ partial "header" . }}
  </head>
  <body>
    {{ partial "skippy" . }}

    {{ partial "docs-navbar" . }}

    <header class="d-flex flex-column flex-md-row align-items-md-center p-5 bg-light">
      <div class="pt-md-3 pb-md-4">
        <h1 class="bd-title mt-0">{{ .Title | markdownify }}</h1>
        <p class="bd-lead">{{ .Page.Params.Description | markdownify }}</p>
        {{ if eq .Title "Examples" }}
        <div class="d-flex flex-column flex-sm-row">
          <a href="{{ .Site.Params.download.dist_examples }}" class="btn btn-bd-primary py-2 px-3" onclick="ga('send', 'event', 'Examples', 'Hero', 'Download Examples');">Download examples</a>
          <a href="{{ .Site.Params.download.source }}" class="btn btn-outline-secondary py-2 px-3 mt-3 mt-sm-0 ml-sm-3" onclick="ga('send', 'event', 'Examples', 'Hero', 'Download');">Download source code</a>
        </div>
        {{- end -}}
      </div>
      {{ partial "ads" . }}
    </header>

    <main class="bd-content p-5" id="content" role="main">
      {{ .Content }}

      {{ if eq .Title "Examples" }}
        <hr class="my-5">
        <div class="container">
          <div class="text-center">
            <div class="masthead-followup-icon d-inline-block mb-2 text-white bg-danger">
              {{ partial "icons/droplet-fill.svg" (dict "width" "32" "height" "32") }}
            </div>
            <h2>Go further with Bootstrap Themes</h2>
            <p class="col-md-10 mx-auto lead font-weight-normal">
              Need something more than these examples? Take Bootstrap to the next level with premium themes from the <a href="{{ $.Site.Params.themes }}">official Bootstrap Themes marketplace</a>. They’re built as their own extended frameworks, rich with new components and plugins, documentation, and powerful build tools.
            </p>
            <a href="{{ .Site.Params.themes }}" class="btn btn-lg btn-outline-primary mb-3">Browse themes</a>
          </div>
          <img class="d-block img-fluid mt-3 mx-auto" srcset="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes-collage.png,
                                                              /docs/{{ .Site.Params.docs_version }}/assets/img/<EMAIL> 2x"
                                                      src="/docs/{{ .Site.Params.docs_version }}/assets/img/bootstrap-themes-collage.png"
                                                      alt="Bootstrap Themes" width="1151" height="320" loading="lazy">
        </div>
      {{ end }}
    </main>

    {{ partial "footer" . }}
    {{ partial "scripts" . }}
  </body>
</html>

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="../../../dist/css/bootstrap.min.css">
    <title>Tooltip</title>
    <style>
      #target {
        border: 1px solid;
        width: 100px;
        height: 50px;
        border: 1px solid;
        margin-left: 50px;
        -webkit-transform: rotate(270deg);
        -ms-transform: rotate(270deg);
        transform: rotate(270deg);
        margin-top: 100px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Tooltip <small>Bootstrap Visual Test</small></h1>

      <p class="text-muted">Tight pants next level keffiyeh <a href="#" data-toggle="tooltip" title="Default tooltip">you probably</a> haven't heard of them. Photo booth beard raw denim letterpress vegan messenger bag stumptown. Farm-to-table seitan, mcsweeney's fixie sustainable quinoa 8-bit american apparel <a href="#" data-toggle="tooltip" title="Another tooltip">have a</a> terry richardson vinyl chambray. Beard stumptown, cardigans banh mi lomo thundercats. Tofu biodiesel williamsburg marfa, four loko mcsweeney's cleanse vegan chambray. A really ironic artisan <a href="#" data-toggle="tooltip" title="Another one here too">whatever keytar</a>, scenester farm-to-table banksy Austin <a href="#" data-toggle="tooltip" title="The last tip!">twitter handle</a> freegan cred raw denim single-origin coffee viral.</p>

      <hr>

      <div class="row">
        <p>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="auto" title="Tooltip on auto">
            Tooltip on auto
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="top" title="Tooltip on top">
            Tooltip on top
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="right" title="Tooltip on right">
            Tooltip on right
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom">
            Tooltip on bottom
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="left" title="Tooltip on left">
            Tooltip on left
          </button>
        </p>
      </div>
      <div class="row">
        <p>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="left" title="Tooltip with XSS" data-container="<img src=1 onerror=alert(123) />">
            Tooltip with XSS
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="left" title="Tooltip with container (selector)" data-container="#customContainer">
            Tooltip with container (selector)
          </button>
          <button id="tooltipElement" type="button" class="btn btn-secondary" data-placement="left" title="Tooltip with container (element)">
            Tooltip with container (element)
          </button>
          <button type="button" class="btn btn-secondary" data-toggle="tooltip" data-html="true" title="<em>Tooltip</em> <u>with</u> <b>HTML</b>">
            Tooltip with HTML
          </button>
        </p>
      </div>
      <div class="row">
        <div class="col-sm-3">
          <div id="target" title="Test tooltip on transformed element"></div>
        </div>
        <div id="shadow" class="pt-5"></div>
      </div>
      <div id="customContainer"></div>
    </div>

    <script src="../../../node_modules/jquery/dist/jquery.slim.min.js"></script>
    <script src="../../../node_modules/popper.js/dist/umd/popper.min.js"></script>
    <script src="../../dist/util.js"></script>
    <script src="../../dist/tooltip.js"></script>
    <script>
      $(function () {
        if (typeof document.body.attachShadow === 'function') {
          var shadowRoot = $('#shadow')[0].attachShadow({ mode: 'open' })
          shadowRoot.innerHTML =
            '<button type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="top" title="Tooltip on top in a shadow dom">' +
            '  Tooltip on top in a shadow dom' +
            '</button>' +
            '<button id="secondTooltip" type="button" class="btn btn-secondary" data-toggle="tooltip" data-placement="top" title="Tooltip on top in a shadow dom with container option">' +
            '  Tooltip on top in a shadow dom' +
            '</button>'

          $(shadowRoot.firstChild).tooltip()
          $(shadowRoot.getElementById('secondTooltip')).tooltip({
            container: shadowRoot
          })
        }
        $('[data-toggle="tooltip"]').tooltip()
        $('#tooltipElement').tooltip({
          container: $('#customContainer')[0]
        })
        $('#target').tooltip({
          placement : 'top',
          trigger : 'manual'
        }).tooltip('show')
      })
    </script>
  </body>
</html>

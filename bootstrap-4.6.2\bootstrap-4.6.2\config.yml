languageCode:           "en"
title:                  "Bootstrap"
baseURL:                "https://getbootstrap.com"
enableInlineShortcodes: true

security:
  enableInlineShortcodes: true
  funcs:
    getenv:
      - ^HUGO_
      - NETLIFY

markup:
  goldmark:
    renderer:
      unsafe:           true
  highlight:
    noClasses:          false
  tableOfContents:
    startLevel:         2
    endLevel:           6

buildDrafts:            true
buildFuture:            true

enableRobotsTXT:        true
metaDataFormat:         "yaml"
disableKinds:           ["404", "taxonomy", "term", "RSS"]

publishDir:             "_site"

module:
  mounts:
    - source:           dist
      target:           static/docs/4.6/dist
    - source:           site/assets
      target:           assets
    - source:           site/content
      target:           content
    - source:           site/data
      target:           data
    - source:           site/layouts
      target:           layouts
    - source:           site/static
      target:           static
    - source:           site/static/docs/4.6/assets/img/favicons/apple-touch-icon.png
      target:           static/apple-touch-icon.png
    - source:           site/static/docs/4.6/assets/img/favicons/favicon.ico
      target:           static/favicon.ico

params:
  description:          "The most popular HTML, CSS, and JS library in the world."
  authors:              "<PERSON>, <PERSON>, and Bootstrap contributors"
  social_image_path:    /docs/4.6/assets/brand/bootstrap-social.png
  social_logo_path:     /docs/4.6/assets/brand/bootstrap-social-logo.png

  current_version:      "4.6.2"
  current_ruby_version: "4.6.2"
  docs_version:         "4.6"
  rfs_version:          "v8.1.0"
  github_org:           "https://github.com/twbs"
  repo:                 "https://github.com/twbs/bootstrap"
  twitter:              "getbootstrap"
  slack:                "https://bootstrap-slack.herokuapp.com/"
  opencollective:       "https://opencollective.com/bootstrap"
  blog:                 "https://blog.getbootstrap.com/"
  expo:                 "https://expo.getbootstrap.com/"
  themes:               "https://themes.getbootstrap.com/"
  icons:                "https://icons.getbootstrap.com/"

  download:
    source:             "https://github.com/twbs/bootstrap/archive/v4.6.2.zip"
    dist:               "https://github.com/twbs/bootstrap/releases/download/v4.6.2/bootstrap-4.6.2-dist.zip"
    dist_examples:      "https://github.com/twbs/bootstrap/releases/download/v4.6.2/bootstrap-4.6.2-examples.zip"

  cdn:
    # See https://www.srihash.org for info on how to generate the hashes
    css:              "https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
    css_hash:         "sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N"
    js:               "https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js"
    js_hash:          "sha384-+sLIOodYLS7CIrQpBjl+C7nPvqq+FbNUBDunl/OZv93DB7Ln/533i8e/mZXLi/P+"
    js_bundle:        "https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"
    js_bundle_hash:   "sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct"
    jquery:           "https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"
    jquery_hash:      "sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj"
    popper:           "https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"
    popper_hash:      "sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN"

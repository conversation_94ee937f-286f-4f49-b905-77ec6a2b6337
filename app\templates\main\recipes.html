{% extends 'base.html' %}

{% block title %}食谱管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>食谱管理</h2>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('recipe.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加食谱
        </a>
    </div>
</div>

<div class="row">
    {% for recipe in recipes.items %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            {% if recipe.main_image %}
            <img src="{{ url_for('static', filename=recipe.main_image) }}" class="card-img-top" alt="{{ recipe.name }}" style="height: 200px; object-fit: cover;">
            {% else %}
            <div class="card-img-top bg-light text-center py-5">
                <i class="fas fa-utensils fa-4x text-muted"></i>
            </div>
            {% endif %}
            <div class="card-body">
                <h5 class="card-title">{{ recipe.name }}</h5>
                <p class="card-text">
                    <span class="badge badge-primary">{{ recipe.category }}</span>
                    {% if recipe.calories %}
                    <span class="badge badge-info">{{ recipe.calories }} 卡路里</span>
                    {% endif %}
                </p>
                <p class="card-text text-muted">
                    <small>创建于: {{  recipe.created_at|default('', true)|string  }}</small>
                </p>
            </div>
            <div class="card-footer bg-white">
                <div class="btn-group btn-group-sm w-100">
                    <a href="{{ url_for('recipe.view', id=recipe.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> 查看
                    </a>
                    <a href="{{ url_for('recipe.edit', id=recipe.id) }}" class="btn btn-outline-success">
                        <i class="fas fa-edit"></i> 编辑
                    </a>
                    <a href="{{ url_for('recipe.delete', id=recipe.id) }}" class="btn btn-outline-danger" onclick="return confirm('确定要删除这个食谱吗？');">
                        <i class="fas fa-trash"></i> 删除
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="col-12">
        <div class="alert alert-info">
            暂无食谱数据，请点击"添加食谱"按钮创建新食谱。
        </div>
    </div>
    {% endfor %}
</div>

{% if recipes.pages > 1 %}
<div class="row mt-4">
    <div class="col-12">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item {% if not recipes.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.recipes', page=recipes.prev_num) if recipes.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in recipes.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == recipes.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.recipes', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not recipes.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.recipes', page=recipes.next_num) if recipes.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
</div>
{% endif %}
{% endblock %}

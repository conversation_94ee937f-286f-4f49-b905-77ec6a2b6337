{"version": 3, "file": "popover.js", "sources": ["../src/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const $tip = $(this.getTipElement())\n\n    // We use append for html objects to maintain js events\n    this.setElementContent($tip.find(SELECTOR_TITLE), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent($tip.find(SELECTOR_CONTENT), content)\n\n    $tip.removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  // Private\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Popover._jQueryInterface\n$.fn[NAME].Constructor = Popover\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Popover._jQueryInterface\n}\n\nexport default Popover\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "RegExp", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "SELECTOR_TITLE", "SELECTOR_CONTENT", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "placement", "trigger", "content", "template", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "Popover", "isWithContent", "getTitle", "_getContent", "addAttachmentClass", "attachment", "getTipElement", "addClass", "tip", "config", "<PERSON><PERSON><PERSON><PERSON>", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "call", "element", "removeClass", "getAttribute", "_cleanTipClass", "tabClass", "attr", "match", "length", "join", "_jQueryInterface", "each", "data", "_config", "test", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,SAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,YAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B,CAAA;EACA,IAAMO,YAAY,GAAG,YAArB,CAAA;EACA,IAAMC,kBAAkB,GAAG,IAAIC,MAAJ,aAAqBF,YAArB,GAAA,MAAA,EAAyC,GAAzC,CAA3B,CAAA;EAEA,IAAMG,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA,IAAMC,cAAc,GAAG,iBAAvB,CAAA;EACA,IAAMC,gBAAgB,GAAG,eAAzB,CAAA;;EAEA,IAAMC,OAAO,GAAA,QAAA,CAAA,EAAA,EACRC,2BAAO,CAACD,OADA,EAAA;EAEXE,EAAAA,SAAS,EAAE,OAFA;EAGXC,EAAAA,OAAO,EAAE,OAHE;EAIXC,EAAAA,OAAO,EAAE,EAJE;EAKXC,EAAAA,QAAQ,EAAE,sCAAA,GACE,2BADF,GAEE,kCAFF,GAGE,wCAAA;EARD,CAAb,CAAA,CAAA;;EAWA,IAAMC,WAAW,GAAA,QAAA,CAAA,EAAA,EACZL,2BAAO,CAACK,WADI,EAAA;EAEfF,EAAAA,OAAO,EAAE,2BAAA;EAFM,CAAjB,CAAA,CAAA;;EAKA,IAAMG,KAAK,GAAG;EACZC,EAAAA,IAAI,WAASnB,SADD;EAEZoB,EAAAA,MAAM,aAAWpB,SAFL;EAGZqB,EAAAA,IAAI,WAASrB,SAHD;EAIZsB,EAAAA,KAAK,YAAUtB,SAJH;EAKZuB,EAAAA,QAAQ,eAAavB,SALT;EAMZwB,EAAAA,KAAK,YAAUxB,SANH;EAOZyB,EAAAA,OAAO,cAAYzB,SAPP;EAQZ0B,EAAAA,QAAQ,eAAa1B,SART;EASZ2B,EAAAA,UAAU,iBAAe3B,SATb;EAUZ4B,EAAAA,UAAU,EAAe5B,YAAAA,GAAAA,SAAAA;EAVb,CAAd,CAAA;EAaA;EACA;EACA;;MAEM6B;;;;;;;;;EA8BJ;EACAC,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,OAAO,IAAKC,CAAAA,QAAL,EAAmB,IAAA,IAAA,CAAKC,WAAL,EAA1B,CAAA;;;WAGFC,qBAAA,SAAmBC,kBAAAA,CAAAA,UAAnB,EAA+B;MAC7BhC,qBAAC,CAAC,IAAKiC,CAAAA,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAoChC,YAApC,GAAA,GAAA,GAAoD8B,UAApD,CAAA,CAAA;;;EAGFC,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,IAAA,CAAKE,GAAL,GAAW,IAAKA,CAAAA,GAAL,IAAYnC,qBAAC,CAAC,IAAKoC,CAAAA,MAAL,CAAYtB,QAAb,CAAD,CAAwB,CAAxB,CAAvB,CAAA;EACA,IAAA,OAAO,KAAKqB,GAAZ,CAAA;;;EAGFE,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;MACX,IAAMC,IAAI,GAAGtC,qBAAC,CAAC,KAAKiC,aAAL,EAAD,CAAd,CADW;;MAIX,IAAKM,CAAAA,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAUjC,cAAV,CAAvB,EAAkD,IAAKsB,CAAAA,QAAL,EAAlD,CAAA,CAAA;;EACA,IAAA,IAAIhB,OAAO,GAAG,IAAKiB,CAAAA,WAAL,EAAd,CAAA;;EACA,IAAA,IAAI,OAAOjB,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAAC4B,IAAR,CAAa,IAAA,CAAKC,OAAlB,CAAV,CAAA;EACD,KAAA;;MAED,IAAKH,CAAAA,iBAAL,CAAuBD,IAAI,CAACE,IAAL,CAAUhC,gBAAV,CAAvB,EAAoDK,OAApD,CAAA,CAAA;EAEAyB,IAAAA,IAAI,CAACK,WAAL,CAAoBtC,eAApB,SAAuCC,eAAvC,CAAA,CAAA;EACD;;;EAGDwB,EAAAA,MAAAA,CAAAA,cAAA,SAAc,WAAA,GAAA;MACZ,OAAO,IAAA,CAAKY,OAAL,CAAaE,YAAb,CAA0B,cAA1B,CACL,IAAA,IAAA,CAAKR,MAAL,CAAYvB,OADd,CAAA;;;EAIFgC,EAAAA,MAAAA,CAAAA,iBAAA,SAAiB,cAAA,GAAA;EACf,IAAA,IAAMP,IAAI,GAAGtC,qBAAC,CAAC,IAAKiC,CAAAA,aAAL,EAAD,CAAd,CAAA;MACA,IAAMa,QAAQ,GAAGR,IAAI,CAACS,IAAL,CAAU,OAAV,CAAmBC,CAAAA,KAAnB,CAAyB7C,kBAAzB,CAAjB,CAAA;;MACA,IAAI2C,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACG,MAAT,GAAkB,CAA3C,EAA8C;QAC5CX,IAAI,CAACK,WAAL,CAAiBG,QAAQ,CAACI,IAAT,CAAc,EAAd,CAAjB,CAAA,CAAA;EACD,KAAA;EACF;;;YAGMC,mBAAP,SAAwBf,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKgB,IAAL,CAAU,YAAY;QAC3B,IAAIC,IAAI,GAAGrD,qBAAC,CAAC,IAAD,CAAD,CAAQqD,IAAR,CAAaxD,QAAb,CAAX,CAAA;;QACA,IAAMyD,OAAO,GAAG,OAAOlB,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD,CAAA;;QAEA,IAAI,CAACiB,IAAD,IAAS,cAAA,CAAeE,IAAf,CAAoBnB,MAApB,CAAb,EAA0C;EACxC,QAAA,OAAA;EACD,OAAA;;QAED,IAAI,CAACiB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI1B,OAAJ,CAAY,IAAZ,EAAkB2B,OAAlB,CAAP,CAAA;UACAtD,qBAAC,CAAC,IAAD,CAAD,CAAQqD,IAAR,CAAaxD,QAAb,EAAuBwD,IAAvB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOjB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOiB,IAAI,CAACjB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIoB,SAAJ,CAAkCpB,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDiB,IAAI,CAACjB,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KApBM,CAAP,CAAA;;;;;EA1EF,IAAA,GAAA;MACA,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOxC,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOa,OAAP,CAAA;EACD,KAAA;;;WAED,SAAkB,GAAA,GAAA;EAChB,MAAA,OAAOd,IAAP,CAAA;EACD,KAAA;;;WAED,SAAsB,GAAA,GAAA;EACpB,MAAA,OAAOE,QAAP,CAAA;EACD,KAAA;;;WAED,SAAmB,GAAA,GAAA;EACjB,MAAA,OAAOmB,KAAP,CAAA;EACD,KAAA;;;WAED,SAAuB,GAAA,GAAA;EACrB,MAAA,OAAOlB,SAAP,CAAA;EACD,KAAA;;;WAED,SAAyB,GAAA,GAAA;EACvB,MAAA,OAAOiB,WAAP,CAAA;EACD,KAAA;;;;IA5BmBL;EAmGtB;EACA;EACA;;;AAEAV,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAagC,GAAAA,OAAO,CAACwB,gBAArB,CAAA;AACAnD,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAW8D,CAAAA,WAAX,GAAyB9B,OAAzB,CAAA;;AACA3B,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAW+D,CAAAA,UAAX,GAAwB,YAAM;EAC5B1D,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb,CAAA;IACA,OAAO4B,OAAO,CAACwB,gBAAf,CAAA;EACD,CAHD;;;;;;;;"}
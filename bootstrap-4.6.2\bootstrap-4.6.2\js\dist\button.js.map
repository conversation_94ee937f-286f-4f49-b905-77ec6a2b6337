{"version": 3, "file": "button.js", "sources": ["../src/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_BUTTON = 'btn'\nconst CLASS_NAME_FOCUS = 'focus'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_FOCUS_BLUR_DATA_API = `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DATA_TOGGLE_CARROT = '[data-toggle^=\"button\"]'\nconst SELECTOR_DATA_TOGGLES = '[data-toggle=\"buttons\"]'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\nconst SELECTOR_DATA_TOGGLES_BUTTONS = '[data-toggle=\"buttons\"] .btn'\nconst SELECTOR_INPUT = 'input:not([type=\"hidden\"])'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_BUTTON = '.btn'\n\n/**\n * Class definition\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    this.shouldAvoidTriggerChange = false\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(SELECTOR_DATA_TOGGLES)[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(SELECTOR_INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked && this._element.classList.contains(CLASS_NAME_ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(SELECTOR_ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n            }\n          }\n        }\n\n        if (triggerChangeEvent) {\n          // if it's not a radio button or checkbox don't add a pointless/invalid checked property to the input\n          if (input.type === 'checkbox' || input.type === 'radio') {\n            input.checked = !this._element.classList.contains(CLASS_NAME_ACTIVE)\n          }\n\n          if (!this.shouldAvoidTriggerChange) {\n            $(input).trigger('change')\n          }\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (!(this._element.hasAttribute('disabled') || this._element.classList.contains('disabled'))) {\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed', !this._element.classList.contains(CLASS_NAME_ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n  static _jQueryInterface(config, avoidTriggerChange) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      data.shouldAvoidTriggerChange = avoidTriggerChange\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    let button = event.target\n    const initialButton = button\n\n    if (!$(button).hasClass(CLASS_NAME_BUTTON)) {\n      button = $(button).closest(SELECTOR_BUTTON)[0]\n    }\n\n    if (!button || button.hasAttribute('disabled') || button.classList.contains('disabled')) {\n      event.preventDefault() // work around Firefox bug #1540995\n    } else {\n      const inputBtn = button.querySelector(SELECTOR_INPUT)\n\n      if (inputBtn && (inputBtn.hasAttribute('disabled') || inputBtn.classList.contains('disabled'))) {\n        event.preventDefault() // work around Firefox bug #1540995\n        return\n      }\n\n      if (initialButton.tagName === 'INPUT' || button.tagName !== 'LABEL') {\n        Button._jQueryInterface.call($(button), 'toggle', initialButton.tagName === 'INPUT')\n      }\n    }\n  })\n  .on(EVENT_FOCUS_BLUR_DATA_API, SELECTOR_DATA_TOGGLE_CARROT, event => {\n    const button = $(event.target).closest(SELECTOR_BUTTON)[0]\n    $(button).toggleClass(CLASS_NAME_FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  // ensure correct active class is set to match the controls' actual values/states\n\n  // find all checkboxes/readio buttons inside data-toggle groups\n  let buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLES_BUTTONS))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    const input = button.querySelector(SELECTOR_INPUT)\n    if (input.checked || input.hasAttribute('checked')) {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // find all button toggles\n  buttons = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    if (button.getAttribute('aria-pressed') === 'true') {\n      button.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      button.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_ACTIVE", "CLASS_NAME_BUTTON", "CLASS_NAME_FOCUS", "EVENT_CLICK_DATA_API", "EVENT_FOCUS_BLUR_DATA_API", "EVENT_LOAD_DATA_API", "SELECTOR_DATA_TOGGLE_CARROT", "SELECTOR_DATA_TOGGLES", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLES_BUTTONS", "SELECTOR_INPUT", "SELECTOR_ACTIVE", "SELECTOR_BUTTON", "<PERSON><PERSON>", "element", "_element", "shouldAvoidTriggerChange", "toggle", "triggerChangeEvent", "addAriaPressed", "rootElement", "closest", "input", "querySelector", "type", "checked", "classList", "contains", "activeElement", "removeClass", "trigger", "focus", "hasAttribute", "setAttribute", "toggleClass", "dispose", "removeData", "_jQueryInterface", "config", "avoidTriggerChange", "each", "$element", "data", "document", "on", "event", "button", "target", "initialButton", "hasClass", "preventDefault", "inputBtn", "tagName", "call", "test", "window", "buttons", "slice", "querySelectorAll", "i", "len", "length", "add", "remove", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EASA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,QAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,WAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EAEA,IAAMQ,iBAAiB,GAAG,QAA1B,CAAA;EACA,IAAMC,iBAAiB,GAAG,KAA1B,CAAA;EACA,IAAMC,gBAAgB,GAAG,OAAzB,CAAA;EAEA,IAAMC,oBAAoB,GAAA,OAAA,GAAWR,SAAX,GAAuBC,YAAjD,CAAA;EACA,IAAMQ,yBAAyB,GAAG,OAAA,GAAQT,SAAR,GAAoBC,YAApB,GACDD,GAAAA,IAAAA,MAAAA,GAAAA,SADC,GACWC,YADX,CAAlC,CAAA;EAEA,IAAMS,mBAAmB,GAAA,MAAA,GAAUV,SAAV,GAAsBC,YAA/C,CAAA;EAEA,IAAMU,2BAA2B,GAAG,yBAApC,CAAA;EACA,IAAMC,qBAAqB,GAAG,yBAA9B,CAAA;EACA,IAAMC,oBAAoB,GAAG,wBAA7B,CAAA;EACA,IAAMC,6BAA6B,GAAG,8BAAtC,CAAA;EACA,IAAMC,cAAc,GAAG,4BAAvB,CAAA;EACA,IAAMC,eAAe,GAAG,SAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA;EACA;EACA;;MAEMC;EACJ,EAAA,SAAA,MAAA,CAAYC,OAAZ,EAAqB;MACnB,IAAKC,CAAAA,QAAL,GAAgBD,OAAhB,CAAA;MACA,IAAKE,CAAAA,wBAAL,GAAgC,KAAhC,CAAA;EACD;;;;;EAOD;EACAC,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;MACP,IAAIC,kBAAkB,GAAG,IAAzB,CAAA;MACA,IAAIC,cAAc,GAAG,IAArB,CAAA;EACA,IAAA,IAAMC,WAAW,GAAGtB,qBAAC,CAAC,IAAKiB,CAAAA,QAAN,CAAD,CAAiBM,OAAjB,CAAyBd,qBAAzB,CAAA,CAAgD,CAAhD,CAApB,CAAA;;EAEA,IAAA,IAAIa,WAAJ,EAAiB;QACf,IAAME,KAAK,GAAG,IAAKP,CAAAA,QAAL,CAAcQ,aAAd,CAA4Bb,cAA5B,CAAd,CAAA;;EAEA,MAAA,IAAIY,KAAJ,EAAW;EACT,QAAA,IAAIA,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;EAC1B,UAAA,IAAIF,KAAK,CAACG,OAAN,IAAiB,IAAKV,CAAAA,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAArB,EAA0E;EACxEkB,YAAAA,kBAAkB,GAAG,KAArB,CAAA;EACD,WAFD,MAEO;EACL,YAAA,IAAMU,aAAa,GAAGR,WAAW,CAACG,aAAZ,CAA0BZ,eAA1B,CAAtB,CAAA;;EAEA,YAAA,IAAIiB,aAAJ,EAAmB;EACjB9B,cAAAA,qBAAC,CAAC8B,aAAD,CAAD,CAAiBC,WAAjB,CAA6B7B,iBAA7B,CAAA,CAAA;EACD,aAAA;EACF,WAAA;EACF,SAAA;;EAED,QAAA,IAAIkB,kBAAJ,EAAwB;EACtB;YACA,IAAII,KAAK,CAACE,IAAN,KAAe,UAAf,IAA6BF,KAAK,CAACE,IAAN,KAAe,OAAhD,EAAyD;EACvDF,YAAAA,KAAK,CAACG,OAAN,GAAgB,CAAC,IAAKV,CAAAA,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAAjB,CAAA;EACD,WAAA;;YAED,IAAI,CAAC,IAAKgB,CAAAA,wBAAV,EAAoC;EAClClB,YAAAA,qBAAC,CAACwB,KAAD,CAAD,CAASQ,OAAT,CAAiB,QAAjB,CAAA,CAAA;EACD,WAAA;EACF,SAAA;;EAEDR,QAAAA,KAAK,CAACS,KAAN,EAAA,CAAA;EACAZ,QAAAA,cAAc,GAAG,KAAjB,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAI,EAAE,IAAKJ,CAAAA,QAAL,CAAciB,YAAd,CAA2B,UAA3B,CAA0C,IAAA,IAAA,CAAKjB,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC,UAAjC,CAA5C,CAAJ,EAA+F;EAC7F,MAAA,IAAIR,cAAJ,EAAoB;EAClB,QAAA,IAAA,CAAKJ,QAAL,CAAckB,YAAd,CAA2B,cAA3B,EAA2C,CAAC,IAAKlB,CAAAA,QAAL,CAAcW,SAAd,CAAwBC,QAAxB,CAAiC3B,iBAAjC,CAA5C,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAIkB,kBAAJ,EAAwB;EACtBpB,QAAAA,qBAAC,CAAC,IAAKiB,CAAAA,QAAN,CAAD,CAAiBmB,WAAjB,CAA6BlC,iBAA7B,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;;EAGHmC,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACRrC,IAAAA,qBAAC,CAACsC,UAAF,CAAa,IAAKrB,CAAAA,QAAlB,EAA4BrB,QAA5B,CAAA,CAAA;MACA,IAAKqB,CAAAA,QAAL,GAAgB,IAAhB,CAAA;EACD;;;EAGMsB,EAAAA,MAAAA,CAAAA,mBAAP,SAAA,gBAAA,CAAwBC,MAAxB,EAAgCC,kBAAhC,EAAoD;MAClD,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;EAC3B,MAAA,IAAMC,QAAQ,GAAG3C,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,MAAA,IAAI4C,IAAI,GAAGD,QAAQ,CAACC,IAAT,CAAchD,QAAd,CAAX,CAAA;;QAEA,IAAI,CAACgD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI7B,MAAJ,CAAW,IAAX,CAAP,CAAA;EACA4B,QAAAA,QAAQ,CAACC,IAAT,CAAchD,QAAd,EAAwBgD,IAAxB,CAAA,CAAA;EACD,OAAA;;QAEDA,IAAI,CAAC1B,wBAAL,GAAgCuB,kBAAhC,CAAA;;QAEA,IAAID,MAAM,KAAK,QAAf,EAAyB;UACvBI,IAAI,CAACJ,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAdM,CAAP,CAAA;;;;;WA5DF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAO7C,OAAP,CAAA;EACD,KAAA;;;;;EA4EH;EACA;EACA;;;AAEAK,uBAAC,CAAC6C,QAAD,CAAD,CACGC,EADH,CACMzC,oBADN,EAC4BG,2BAD5B,EACyD,UAAAuC,KAAK,EAAI;EAC9D,EAAA,IAAIC,MAAM,GAAGD,KAAK,CAACE,MAAnB,CAAA;IACA,IAAMC,aAAa,GAAGF,MAAtB,CAAA;;IAEA,IAAI,CAAChD,qBAAC,CAACgD,MAAD,CAAD,CAAUG,QAAV,CAAmBhD,iBAAnB,CAAL,EAA4C;MAC1C6C,MAAM,GAAGhD,qBAAC,CAACgD,MAAD,CAAD,CAAUzB,OAAV,CAAkBT,eAAlB,CAAmC,CAAA,CAAnC,CAAT,CAAA;EACD,GAAA;;EAED,EAAA,IAAI,CAACkC,MAAD,IAAWA,MAAM,CAACd,YAAP,CAAoB,UAApB,CAAX,IAA8Cc,MAAM,CAACpB,SAAP,CAAiBC,QAAjB,CAA0B,UAA1B,CAAlD,EAAyF;MACvFkB,KAAK,CAACK,cAAN,EAAA,CADuF;EAExF,GAFD,MAEO;EACL,IAAA,IAAMC,QAAQ,GAAGL,MAAM,CAACvB,aAAP,CAAqBb,cAArB,CAAjB,CAAA;;EAEA,IAAA,IAAIyC,QAAQ,KAAKA,QAAQ,CAACnB,YAAT,CAAsB,UAAtB,CAAA,IAAqCmB,QAAQ,CAACzB,SAAT,CAAmBC,QAAnB,CAA4B,UAA5B,CAA1C,CAAZ,EAAgG;QAC9FkB,KAAK,CAACK,cAAN,EAAA,CAD8F;;EAE9F,MAAA,OAAA;EACD,KAAA;;MAED,IAAIF,aAAa,CAACI,OAAd,KAA0B,OAA1B,IAAqCN,MAAM,CAACM,OAAP,KAAmB,OAA5D,EAAqE;EACnEvC,MAAAA,MAAM,CAACwB,gBAAP,CAAwBgB,IAAxB,CAA6BvD,qBAAC,CAACgD,MAAD,CAA9B,EAAwC,QAAxC,EAAkDE,aAAa,CAACI,OAAd,KAA0B,OAA5E,CAAA,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAvBH,CAAA,CAwBGR,EAxBH,CAwBMxC,yBAxBN,EAwBiCE,2BAxBjC,EAwB8D,UAAAuC,KAAK,EAAI;EACnE,EAAA,IAAMC,MAAM,GAAGhD,qBAAC,CAAC+C,KAAK,CAACE,MAAP,CAAD,CAAgB1B,OAAhB,CAAwBT,eAAxB,CAAA,CAAyC,CAAzC,CAAf,CAAA;EACAd,EAAAA,qBAAC,CAACgD,MAAD,CAAD,CAAUZ,WAAV,CAAsBhC,gBAAtB,EAAwC,cAAA,CAAeoD,IAAf,CAAoBT,KAAK,CAACrB,IAA1B,CAAxC,CAAA,CAAA;EACD,CA3BH,CAAA,CAAA;AA6BA1B,uBAAC,CAACyD,MAAD,CAAD,CAAUX,EAAV,CAAavC,mBAAb,EAAkC,YAAM;EACtC;EAEA;EACA,EAAA,IAAImD,OAAO,GAAG,EAAGC,CAAAA,KAAH,CAASJ,IAAT,CAAcV,QAAQ,CAACe,gBAAT,CAA0BjD,6BAA1B,CAAd,CAAd,CAAA;;EACA,EAAA,KAAK,IAAIkD,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,IAAA,IAAMb,MAAM,GAAGU,OAAO,CAACG,CAAD,CAAtB,CAAA;EACA,IAAA,IAAMrC,KAAK,GAAGwB,MAAM,CAACvB,aAAP,CAAqBb,cAArB,CAAd,CAAA;;MACA,IAAIY,KAAK,CAACG,OAAN,IAAiBH,KAAK,CAACU,YAAN,CAAmB,SAAnB,CAArB,EAAoD;EAClDc,MAAAA,MAAM,CAACpB,SAAP,CAAiBoC,GAAjB,CAAqB9D,iBAArB,CAAA,CAAA;EACD,KAFD,MAEO;EACL8C,MAAAA,MAAM,CAACpB,SAAP,CAAiBqC,MAAjB,CAAwB/D,iBAAxB,CAAA,CAAA;EACD,KAAA;EACF,GAbqC;;;EAgBtCwD,EAAAA,OAAO,GAAG,EAAA,CAAGC,KAAH,CAASJ,IAAT,CAAcV,QAAQ,CAACe,gBAAT,CAA0BlD,oBAA1B,CAAd,CAAV,CAAA;;EACA,EAAA,KAAK,IAAImD,EAAC,GAAG,CAAR,EAAWC,IAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,EAAC,GAAGC,IAA1C,EAA+CD,EAAC,EAAhD,EAAoD;EAClD,IAAA,IAAMb,OAAM,GAAGU,OAAO,CAACG,EAAD,CAAtB,CAAA;;EACA,IAAA,IAAIb,OAAM,CAACkB,YAAP,CAAoB,cAApB,CAAA,KAAwC,MAA5C,EAAoD;EAClDlB,MAAAA,OAAM,CAACpB,SAAP,CAAiBoC,GAAjB,CAAqB9D,iBAArB,CAAA,CAAA;EACD,KAFD,MAEO;EACL8C,MAAAA,OAAM,CAACpB,SAAP,CAAiBqC,MAAjB,CAAwB/D,iBAAxB,CAAA,CAAA;EACD,KAAA;EACF,GAAA;EACF,CAzBD,CAAA,CAAA;EA2BA;EACA;EACA;;AAEAF,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAaqB,GAAAA,MAAM,CAACwB,gBAApB,CAAA;AACAvC,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWyE,CAAAA,WAAX,GAAyBpD,MAAzB,CAAA;;AACAf,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW0E,CAAAA,UAAX,GAAwB,YAAM;EAC5BpE,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOgB,MAAM,CAACwB,gBAAd,CAAA;EACD,CAHD;;;;;;;;"}
{% extends 'base.html' %}

{% block title %}仓库详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">仓库详情</h5>
                    <div class="card-tools">
                        <a href="{{ url_for('warehouse_new.edit', id=warehouse.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('warehouse_new.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>ID</label>
                                <p>{{ warehouse.id }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>名称</label>
                                <p>{{ warehouse.name }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>区域</label>
                                <p>{{ warehouse.area.name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>位置</label>
                                <p>{{ warehouse.location }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>管理员</label>
                                <p>{{ warehouse.manager.real_name or warehouse.manager.username }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>容量</label>
                                <p>{{ warehouse.capacity }} {{ warehouse.capacity_unit }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>温度范围</label>
                                <p>{{ warehouse.temperature_range }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>湿度范围</label>
                                <p>{{ warehouse.humidity_range }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>状态</label>
                                <p>
                                    {% if warehouse.status == '正常' %}
                                    <span class="badge badge-success">{{ warehouse.status }}</span>
                                    {% elif warehouse.status == '维护中' %}
                                    <span class="badge badge-warning">{{ warehouse.status }}</span>
                                    {% else %}
                                    <span class="badge badge-danger">{{ warehouse.status }}</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>创建时间</label>
                                <p>{{ warehouse.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>备注</label>
                                <p>{{ warehouse.notes }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频引导服务
为用户引导系统提供视频演示功能
"""

from flask import current_app
import json

class VideoGuideService:
    """视频引导服务类"""
    
    # 视频资源配置
    VIDEO_RESOURCES = {
        'daily_management': {
            'title': '日常管理模块操作演示',
            'duration': '5:30',
            'videos': [
                {
                    'name': '检查记录创建',
                    'url': '/static/videos/daily_management/inspection_record.mp4',
                    'thumbnail': '/static/images/video_thumbnails/inspection_record.jpg',
                    'duration': '2:15',
                    'description': '演示如何创建食品安全检查记录，包括二维码扫描上传'
                },
                {
                    'name': 'PDF报告生成',
                    'url': '/static/videos/daily_management/pdf_generation.mp4',
                    'thumbnail': '/static/images/video_thumbnails/pdf_generation.jpg',
                    'duration': '1:45',
                    'description': '演示如何生成专业的PDF检查报告'
                },
                {
                    'name': '陪餐记录管理',
                    'url': '/static/videos/daily_management/companion_dining.mp4',
                    'thumbnail': '/static/images/video_thumbnails/companion_dining.jpg',
                    'duration': '1:30',
                    'description': '演示陪餐记录的创建和管理流程'
                }
            ]
        },
        'suppliers': {
            'title': '供应商管理操作演示',
            'duration': '4:20',
            'videos': [
                {
                    'name': '供应商档案创建',
                    'url': '/static/videos/suppliers/create_supplier.mp4',
                    'thumbnail': '/static/images/video_thumbnails/create_supplier.jpg',
                    'duration': '2:30',
                    'description': '演示如何创建完整的供应商档案'
                },
                {
                    'name': '供应商评价管理',
                    'url': '/static/videos/suppliers/supplier_evaluation.mp4',
                    'thumbnail': '/static/images/video_thumbnails/supplier_evaluation.jpg',
                    'duration': '1:50',
                    'description': '演示供应商评价和管理功能'
                }
            ]
        },
        'ingredients_recipes': {
            'title': '食材食谱管理演示',
            'duration': '6:45',
            'videos': [
                {
                    'name': '食材档案创建',
                    'url': '/static/videos/ingredients/create_ingredient.mp4',
                    'thumbnail': '/static/images/video_thumbnails/create_ingredient.jpg',
                    'duration': '3:15',
                    'description': '演示食材基础档案的创建和营养信息设置'
                },
                {
                    'name': '食谱制作流程',
                    'url': '/static/videos/recipes/create_recipe.mp4',
                    'thumbnail': '/static/images/video_thumbnails/create_recipe.jpg',
                    'duration': '3:30',
                    'description': '演示营养食谱的创建和成本核算'
                }
            ]
        },
        'weekly_menu': {
            'title': '周菜单制定演示',
            'duration': '5:15',
            'videos': [
                {
                    'name': '菜单计划创建',
                    'url': '/static/videos/weekly_menu/create_menu.mp4',
                    'thumbnail': '/static/images/video_thumbnails/create_menu.jpg',
                    'duration': '2:45',
                    'description': '演示周菜单计划的创建流程'
                },
                {
                    'name': '菜品安排编辑',
                    'url': '/static/videos/weekly_menu/edit_menu.mp4',
                    'thumbnail': '/static/images/video_thumbnails/edit_menu.jpg',
                    'duration': '2:30',
                    'description': '演示如何进入编辑状态安排具体菜品'
                }
            ]
        },
        'purchase_order': {
            'title': '采购订单管理演示',
            'duration': '4:50',
            'videos': [
                {
                    'name': '从菜单生成采购单',
                    'url': '/static/videos/purchase/generate_order.mp4',
                    'thumbnail': '/static/images/video_thumbnails/generate_order.jpg',
                    'duration': '2:25',
                    'description': '演示如何从周菜单自动生成采购订单'
                },
                {
                    'name': '供应商选择和确认',
                    'url': '/static/videos/purchase/supplier_selection.mp4',
                    'thumbnail': '/static/images/video_thumbnails/supplier_selection.jpg',
                    'duration': '2:25',
                    'description': '演示供应商选择和订单确认流程'
                }
            ]
        },
        'stock_in': {
            'title': '食材入库管理演示',
            'duration': '6:30',
            'videos': [
                {
                    'name': '入库检查流程',
                    'url': '/static/videos/stock/stock_in_inspection.mp4',
                    'thumbnail': '/static/images/video_thumbnails/stock_in_inspection.jpg',
                    'duration': '3:45',
                    'description': '演示食材入库时的质量检查和记录流程'
                },
                {
                    'name': '储存位置分配',
                    'url': '/static/videos/stock/storage_allocation.mp4',
                    'thumbnail': '/static/images/video_thumbnails/storage_allocation.jpg',
                    'duration': '2:45',
                    'description': '演示如何为不同食材分配合适的储存位置'
                }
            ]
        },
        'consumption_plan': {
            'title': '消耗量计划制定演示',
            'duration': '4:15',
            'videos': [
                {
                    'name': '日消耗计划制定',
                    'url': '/static/videos/consumption/daily_plan.mp4',
                    'thumbnail': '/static/images/video_thumbnails/daily_plan.jpg',
                    'duration': '2:10',
                    'description': '演示如何制定每日食材消耗计划'
                },
                {
                    'name': '周消耗计划优化',
                    'url': '/static/videos/consumption/weekly_optimization.mp4',
                    'thumbnail': '/static/images/video_thumbnails/weekly_optimization.jpg',
                    'duration': '2:05',
                    'description': '演示周消耗计划的制定和优化'
                }
            ]
        },
        'stock_out': {
            'title': '食材出库管理演示',
            'duration': '3:40',
            'videos': [
                {
                    'name': '出库操作流程',
                    'url': '/static/videos/stock/stock_out_process.mp4',
                    'thumbnail': '/static/images/video_thumbnails/stock_out_process.jpg',
                    'duration': '2:20',
                    'description': '演示食材出库的完整操作流程'
                },
                {
                    'name': '先进先出管理',
                    'url': '/static/videos/stock/fifo_management.mp4',
                    'thumbnail': '/static/images/video_thumbnails/fifo_management.jpg',
                    'duration': '1:20',
                    'description': '演示先进先出原则的实际应用'
                }
            ]
        },
        'traceability': {
            'title': '食材溯源系统演示',
            'duration': '5:25',
            'videos': [
                {
                    'name': '溯源查询操作',
                    'url': '/static/videos/traceability/trace_query.mp4',
                    'thumbnail': '/static/images/video_thumbnails/trace_query.jpg',
                    'duration': '2:45',
                    'description': '演示如何进行食材溯源查询'
                },
                {
                    'name': '溯源链条分析',
                    'url': '/static/videos/traceability/chain_analysis.mp4',
                    'thumbnail': '/static/images/video_thumbnails/chain_analysis.jpg',
                    'duration': '2:40',
                    'description': '演示完整溯源链条的分析和展示'
                }
            ]
        },
        'food_samples': {
            'title': '留样记录管理演示',
            'duration': '3:55',
            'videos': [
                {
                    'name': '留样记录创建',
                    'url': '/static/videos/samples/create_sample.mp4',
                    'thumbnail': '/static/images/video_thumbnails/create_sample.jpg',
                    'duration': '2:15',
                    'description': '演示留样记录的创建和管理'
                },
                {
                    'name': '一键生成功能',
                    'url': '/static/videos/samples/auto_generation.mp4',
                    'thumbnail': '/static/images/video_thumbnails/auto_generation.jpg',
                    'duration': '1:40',
                    'description': '演示一键生成留样记录的功能'
                }
            ]
        }
    }
    
    @staticmethod
    def get_video_resources(step_name):
        """获取指定步骤的视频资源"""
        return VideoGuideService.VIDEO_RESOURCES.get(step_name, {})
    
    @staticmethod
    def get_all_videos():
        """获取所有视频资源"""
        return VideoGuideService.VIDEO_RESOURCES
    
    @staticmethod
    def get_video_playlist(step_names):
        """获取指定步骤的视频播放列表"""
        playlist = []
        for step_name in step_names:
            step_videos = VideoGuideService.VIDEO_RESOURCES.get(step_name, {})
            if step_videos and 'videos' in step_videos:
                for video in step_videos['videos']:
                    playlist.append({
                        'step': step_name,
                        'step_title': step_videos['title'],
                        **video
                    })
        return playlist
    
    @staticmethod
    def create_video_placeholder(step_name, video_name):
        """为尚未制作的视频创建占位符"""
        return {
            'name': video_name,
            'url': f'/static/videos/placeholder/{step_name}_{video_name.lower().replace(" ", "_")}.mp4',
            'thumbnail': '/static/images/video_thumbnails/placeholder.jpg',
            'duration': '待制作',
            'description': f'{video_name}操作演示视频（制作中）',
            'is_placeholder': True
        }
    
    @staticmethod
    def get_video_analytics():
        """获取视频观看统计"""
        # 这里可以集成视频分析服务
        return {
            'total_videos': sum(len(step.get('videos', [])) for step in VideoGuideService.VIDEO_RESOURCES.values()),
            'total_duration': '45:30',  # 总时长
            'most_watched': 'weekly_menu',
            'completion_rate': 0.85
        }

import json
from datetime import datetime, date
from flask import current_app
import redis

class MenuPlanCache:
    """周菜单计划缓存管理类"""
    
    @staticmethod
    def _get_cache_key(area_id, week_start, user_id):
        """生成缓存键"""
        return f"menu_plan:{area_id}:{week_start.strftime('%Y-%m-%d')}:{user_id}"

    @staticmethod
    def _get_redis_client():
        """获取Redis客户端"""
        return redis.Redis(
            host=current_app.config.get('REDIS_HOST', 'localhost'),
            port=current_app.config.get('REDIS_PORT', 6379),
            db=current_app.config.get('REDIS_DB', 0),
            decode_responses=True
        )

    @classmethod
    def save_to_cache(cls, area_id, week_start, menu_data, user_id):
        """保存菜单数据到缓存"""
        try:
            redis_client = cls._get_redis_client()
            cache_key = cls._get_cache_key(area_id, week_start, user_id)
            
            # 将数据转换为JSON字符串
            menu_data_json = json.dumps(menu_data, default=str)
            
            # 设置缓存，过期时间24小时
            redis_client.setex(cache_key, 24 * 60 * 60, menu_data_json)
            return True
        except Exception as e:
            current_app.logger.error(f"保存菜单缓存失败: {str(e)}")
            return False

    @classmethod
    def get_from_cache(cls, area_id, week_start, user_id):
        """从缓存获取菜单数据"""
        try:
            redis_client = cls._get_redis_client()
            cache_key = cls._get_cache_key(area_id, week_start, user_id)
            
            # 获取缓存数据
            menu_data_json = redis_client.get(cache_key)
            if menu_data_json:
                # 将JSON字符串转换回Python对象
                menu_data = json.loads(menu_data_json)
                
                # 转换日期字符串为date对象
                for date_str, day_data in menu_data['days'].items():
                    day_data['date'] = datetime.strptime(date_str, '%Y-%m-%d').date()
                
                return menu_data
            return None
        except Exception as e:
            current_app.logger.error(f"获取菜单缓存失败: {str(e)}")
            return None

    @classmethod
    def clear_cache(cls, area_id, week_start, user_id):
        """清除缓存"""
        try:
            redis_client = cls._get_redis_client()
            cache_key = cls._get_cache_key(area_id, week_start, user_id)
            redis_client.delete(cache_key)
            return True
        except Exception as e:
            current_app.logger.error(f"清除菜单缓存失败: {str(e)}")
            return False 
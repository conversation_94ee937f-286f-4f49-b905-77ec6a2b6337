{% extends 'base.html' %}

{% block title %}
{% if category %}编辑食材分类{% else %}添加食材分类{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if category %}编辑食材分类{% else %}添加食材分类{% endif %}</h3>
                </div>
                <div class="card-body">
                    <form method="POST"><div class="form-group">
                            <label for="name">分类名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ category.name if category else '' }}" required>
                        </div>
                        <div class="form-group">
                            <label for="parent_id">父分类</label>
                            <select class="form-control" id="parent_id" name="parent_id">
                                <option value="">-- 无父分类（顶级分类）--</option>
                                {% for parent in parent_categories %}
                                <option value="{{ parent.id }}" {% if category and category.parent_id == parent.id %}selected{% endif %}>
                                    {{ parent.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="description">描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ category.description if category else '' }}</textarea>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('ingredient_category.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

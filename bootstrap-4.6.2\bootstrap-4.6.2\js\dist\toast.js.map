{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 500\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\n/**\n * Class definition\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  show() {\n    const showEvent = $.Event(EVENT_SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      $(this._element).trigger(EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      $(this._element).trigger(EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_FADE", "CLASS_NAME_HIDE", "CLASS_NAME_SHOW", "CLASS_NAME_SHOWING", "EVENT_CLICK_DISMISS", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "SELECTOR_DATA_DISMISS", "<PERSON><PERSON><PERSON>", "animation", "autohide", "delay", "DefaultType", "Toast", "element", "config", "_element", "_config", "_getConfig", "_timeout", "_setListeners", "show", "showEvent", "Event", "trigger", "isDefaultPrevented", "_clearTimeout", "classList", "add", "complete", "remove", "setTimeout", "hide", "<PERSON><PERSON>", "reflow", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "contains", "hideEvent", "_close", "dispose", "off", "removeData", "data", "typeCheckConfig", "constructor", "on", "clearTimeout", "_jQueryInterface", "each", "$element", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,OAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B,CAAA;EAEA,IAAMO,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,kBAAkB,GAAG,SAA3B,CAAA;EAEA,IAAMC,mBAAmB,qBAAmBR,SAA5C,CAAA;EACA,IAAMS,UAAU,YAAUT,SAA1B,CAAA;EACA,IAAMU,YAAY,cAAYV,SAA9B,CAAA;EACA,IAAMW,UAAU,YAAUX,SAA1B,CAAA;EACA,IAAMY,WAAW,aAAWZ,SAA5B,CAAA;EAEA,IAAMa,qBAAqB,GAAG,wBAA9B,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,GAAA;EAHO,CAAhB,CAAA;EAMA,IAAMC,WAAW,GAAG;EAClBH,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,QAAA;EAHW,CAApB,CAAA;EAMA;EACA;EACA;;MAEME;IACJ,SAAYC,KAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;MAC3B,IAAKC,CAAAA,QAAL,GAAgBF,OAAhB,CAAA;EACA,IAAA,IAAA,CAAKG,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBH,MAAhB,CAAf,CAAA;MACA,IAAKI,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;EACA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;EACD;;;;;EAeD;EACAC,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;EACL,IAAA,IAAMC,SAAS,GAAG1B,qBAAC,CAAC2B,KAAF,CAAQlB,UAAR,CAAlB,CAAA;EAEAT,IAAAA,qBAAC,CAAC,IAAKoB,CAAAA,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBF,SAAzB,CAAA,CAAA;;EACA,IAAA,IAAIA,SAAS,CAACG,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;;EAEA,IAAA,IAAI,IAAKT,CAAAA,OAAL,CAAaR,SAAjB,EAA4B;EAC1B,MAAA,IAAA,CAAKO,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4B9B,eAA5B,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAM+B,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACb,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+B7B,kBAA/B,CAAA,CAAA;;EACA,MAAA,KAAI,CAACe,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4B5B,eAA5B,CAAA,CAAA;;QAEAJ,qBAAC,CAAC,KAAI,CAACoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBlB,WAAzB,CAAA,CAAA;;EAEA,MAAA,IAAI,KAAI,CAACW,OAAL,CAAaP,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACS,QAAL,GAAgBY,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAACC,IAAL,EAAA,CAAA;EACD,SAFyB,EAEvB,KAAI,CAACf,OAAL,CAAaN,KAFU,CAA1B,CAAA;EAGD,OAAA;OAVH,CAAA;;EAaA,IAAA,IAAA,CAAKK,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+B/B,eAA/B,CAAA,CAAA;;EACAkC,IAAAA,wBAAI,CAACC,MAAL,CAAY,IAAA,CAAKlB,QAAjB,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKA,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4B3B,kBAA5B,CAAA,CAAA;;EACA,IAAA,IAAI,IAAKgB,CAAAA,OAAL,CAAaR,SAAjB,EAA4B;QAC1B,IAAM0B,kBAAkB,GAAGF,wBAAI,CAACG,gCAAL,CAAsC,IAAA,CAAKpB,QAA3C,CAA3B,CAAA;EAEApB,MAAAA,qBAAC,CAAC,IAAA,CAAKoB,QAAN,CAAD,CACGqB,GADH,CACOJ,wBAAI,CAACK,cADZ,EAC4BT,QAD5B,CAEGU,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;EAGD,KAND,MAMO;QACLN,QAAQ,EAAA,CAAA;EACT,KAAA;;;EAGHG,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;MACL,IAAI,CAAC,IAAKhB,CAAAA,QAAL,CAAcW,SAAd,CAAwBa,QAAxB,CAAiCxC,eAAjC,CAAL,EAAwD;EACtD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMyC,SAAS,GAAG7C,qBAAC,CAAC2B,KAAF,CAAQpB,UAAR,CAAlB,CAAA;EAEAP,IAAAA,qBAAC,CAAC,IAAKoB,CAAAA,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBiB,SAAzB,CAAA,CAAA;;EACA,IAAA,IAAIA,SAAS,CAAChB,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKiB,MAAL,EAAA,CAAA;;;EAGFC,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACR,IAAA,IAAA,CAAKjB,aAAL,EAAA,CAAA;;MAEA,IAAI,IAAA,CAAKV,QAAL,CAAcW,SAAd,CAAwBa,QAAxB,CAAiCxC,eAAjC,CAAJ,EAAuD;EACrD,MAAA,IAAA,CAAKgB,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+B9B,eAA/B,CAAA,CAAA;EACD,KAAA;;EAEDJ,IAAAA,qBAAC,CAAC,IAAKoB,CAAAA,QAAN,CAAD,CAAiB4B,GAAjB,CAAqB1C,mBAArB,CAAA,CAAA;EAEAN,IAAAA,qBAAC,CAACiD,UAAF,CAAa,IAAK7B,CAAAA,QAAlB,EAA4BvB,QAA5B,CAAA,CAAA;MACA,IAAKuB,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAe,IAAf,CAAA;EACD;;;WAGDC,aAAA,SAAWH,UAAAA,CAAAA,MAAX,EAAmB;MACjBA,MAAM,GAAA,QAAA,CAAA,EAAA,EACDP,OADC,EAEDZ,qBAAC,CAAC,IAAKoB,CAAAA,QAAN,CAAD,CAAiB8B,IAAjB,EAFC,EAGA,OAAO/B,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN,CAAA;MAMAkB,wBAAI,CAACc,eAAL,CACExD,IADF,EAEEwB,MAFF,EAGE,IAAA,CAAKiC,WAAL,CAAiBpC,WAHnB,CAAA,CAAA;EAMA,IAAA,OAAOG,MAAP,CAAA;;;EAGFK,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACdxB,qBAAC,CAAC,IAAKoB,CAAAA,QAAN,CAAD,CAAiBiC,EAAjB,CAAoB/C,mBAApB,EAAyCK,qBAAzC,EAAgE,YAAA;QAAA,OAAM,MAAI,CAACyB,IAAL,EAAN,CAAA;OAAhE,CAAA,CAAA;;;EAGFU,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACP,IAAA,IAAMb,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACb,QAAL,CAAcW,SAAd,CAAwBC,GAAxB,CAA4B7B,eAA5B,CAAA,CAAA;;QACAH,qBAAC,CAAC,MAAI,CAACoB,QAAN,CAAD,CAAiBQ,OAAjB,CAAyBpB,YAAzB,CAAA,CAAA;OAFF,CAAA;;EAKA,IAAA,IAAA,CAAKY,QAAL,CAAcW,SAAd,CAAwBG,MAAxB,CAA+B9B,eAA/B,CAAA,CAAA;;EACA,IAAA,IAAI,IAAKiB,CAAAA,OAAL,CAAaR,SAAjB,EAA4B;QAC1B,IAAM0B,kBAAkB,GAAGF,wBAAI,CAACG,gCAAL,CAAsC,IAAA,CAAKpB,QAA3C,CAA3B,CAAA;EAEApB,MAAAA,qBAAC,CAAC,IAAA,CAAKoB,QAAN,CAAD,CACGqB,GADH,CACOJ,wBAAI,CAACK,cADZ,EAC4BT,QAD5B,CAEGU,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;EAGD,KAND,MAMO;QACLN,QAAQ,EAAA,CAAA;EACT,KAAA;;;EAGHH,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;MACdwB,YAAY,CAAC,IAAK/B,CAAAA,QAAN,CAAZ,CAAA;MACA,IAAKA,CAAAA,QAAL,GAAgB,IAAhB,CAAA;EACD;;;UAGMgC,mBAAP,SAAwBpC,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKqC,IAAL,CAAU,YAAY;EAC3B,MAAA,IAAMC,QAAQ,GAAGzD,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,MAAA,IAAIkD,IAAI,GAAGO,QAAQ,CAACP,IAAT,CAAcrD,QAAd,CAAX,CAAA;;EACA,MAAA,IAAMwB,OAAO,GAAG,OAAOF,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C,CAAA;;QAEA,IAAI,CAAC+B,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIjC,KAAJ,CAAU,IAAV,EAAgBI,OAAhB,CAAP,CAAA;EACAoC,QAAAA,QAAQ,CAACP,IAAT,CAAcrD,QAAd,EAAwBqD,IAAxB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAO/B,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAO+B,IAAI,CAAC/B,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIuC,SAAJ,CAAkCvC,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;EAED+B,QAAAA,IAAI,CAAC/B,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;EACD,OAAA;EACF,KAjBM,CAAP,CAAA;;;;;WAjIF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOvB,OAAP,CAAA;EACD,KAAA;;;WAED,SAAyB,GAAA,GAAA;EACvB,MAAA,OAAOoB,WAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOJ,OAAP,CAAA;EACD,KAAA;;;;;EA4IH;EACA;EACA;;;AAEAZ,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAasB,GAAAA,KAAK,CAACsC,gBAAnB,CAAA;AACAvD,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAWgE,CAAAA,WAAX,GAAyB1C,KAAzB,CAAA;;AACAjB,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAWiE,CAAAA,UAAX,GAAwB,YAAM;EAC5B5D,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb,CAAA;IACA,OAAOkB,KAAK,CAACsC,gBAAb,CAAA;EACD,CAHD;;;;;;;;"}
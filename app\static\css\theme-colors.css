/* 主题颜色系统 */
:root {
  /* 默认主题颜色 (Primary - 蓝色) */
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;

  /* 辅助颜色 */
  --theme-secondary: #6c757d;
  --theme-success: #28a745;
  --theme-info: #17a2b8;
  --theme-warning: #ffc107;
  --theme-danger: #dc3545;
  --theme-light: #f8f9fa;
  --theme-dark: #343a40;
}

/* 主题颜色变体 */
[data-theme="primary"] {
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;
}

[data-theme="secondary"] {
  --theme-primary: #6c757d;
  --theme-primary-light: #9ca3ab;
  --theme-primary-dark: #495057;
  --theme-primary-rgb: 108, 117, 125;
}

[data-theme="success"] {
  --theme-primary: #28a745;
  --theme-primary-light: #6cbf47;
  --theme-primary-dark: #1e7e34;
  --theme-primary-rgb: 40, 167, 69;
}

[data-theme="danger"] {
  --theme-primary: #dc3545;
  --theme-primary-light: #e4606d;
  --theme-primary-dark: #c82333;
  --theme-primary-rgb: 220, 53, 69;
}

[data-theme="warning"] {
  --theme-primary: #ffc107;
  --theme-primary-light: #ffcd39;
  --theme-primary-dark: #e0a800;
  --theme-primary-rgb: 255, 193, 7;
}

[data-theme="info"] {
  --theme-primary: #17a2b8;
  --theme-primary-light: #46b5c7;
  --theme-primary-dark: #117a8b;
  --theme-primary-rgb: 23, 162, 184;
}

[data-theme="dark"] {
  --theme-primary: #343a40;
  --theme-primary-light: #6c757d;
  --theme-primary-dark: #23272b;
  --theme-primary-rgb: 52, 58, 64;
}

/* 应用主题颜色到界面元素 */
.navbar-brand {
  color: var(--theme-primary) !important;
}

.navbar-nav .nav-link.active {
  background-color: var(--theme-primary) !important;
  color: white !important;
}

.btn-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

.btn-primary:hover {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
}

.btn-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.5);
}

.card-header.bg-primary {
  background-color: var(--theme-primary) !important;
}

.badge-primary {
  background-color: var(--theme-primary);
}

.text-primary {
  color: var(--theme-primary) !important;
}

.border-primary {
  border-color: var(--theme-primary) !important;
}

.dropdown-item.active,
.dropdown-item:active {
  background-color: var(--theme-primary);
}

.form-control:focus {
  border-color: var(--theme-primary-light);
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.25);
}

.progress-bar {
  background-color: var(--theme-primary);
}

.pagination .page-link {
  color: var(--theme-primary);
}

.pagination .page-item.active .page-link {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

.alert-primary {
  color: var(--theme-primary-dark);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}

/* 链接颜色 */
a {
  color: var(--theme-primary);
}

a:hover {
  color: var(--theme-primary-dark);
}

/* 表格样式 */
.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

.table-hover .table-primary:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.2);
}

/* 导航栏样式 */
.navbar-dark .navbar-nav .nav-link:hover {
  color: var(--theme-primary-light);
}

/* 侧边栏样式（如果有的话） */
.sidebar .nav-link.active {
  background-color: var(--theme-primary);
  color: white;
}

/* 图标颜色 */
.text-primary i {
  color: var(--theme-primary) !important;
}

/* 主题切换动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 深色主题特殊处理 */
[data-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #fff;
}

[data-theme="dark"] .card {
  background-color: #343a40;
  color: #fff;
}

[data-theme="dark"] .navbar {
  background-color: var(--theme-primary) !important;
}

[data-theme="dark"] .table {
  color: #fff;
}

[data-theme="dark"] .form-control {
  background-color: #495057;
  border-color: #6c757d;
  color: #fff;
}

[data-theme="dark"] .form-control:focus {
  background-color: #495057;
  border-color: var(--theme-primary);
  color: #fff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-nav .nav-link.active {
    border-radius: 0.25rem;
    margin: 0.25rem;
  }
}

/* 主题切换按钮样式 */
.theme-selector {
  position: relative;
}

.theme-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  vertical-align: middle;
}

.theme-preview.primary { background-color: #007bff; }
.theme-preview.secondary { background-color: #6c757d; }
.theme-preview.success { background-color: #28a745; }
.theme-preview.danger { background-color: #dc3545; }
.theme-preview.warning { background-color: #ffc107; }
.theme-preview.info { background-color: #17a2b8; }
.theme-preview.dark { background-color: #343a40; }

/* 主题切换器下拉菜单样式 */
#themeDropdown {
  color: var(--theme-primary) !important;
  transition: color 0.3s ease;
}

#themeDropdown:hover {
  color: var(--theme-primary-dark) !important;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  text-decoration: none;
}

.theme-option.active {
  background-color: var(--theme-primary);
  color: white;
}

.theme-option.active .theme-preview {
  border-color: white;
}

/* 主题切换器在系统设置页面的样式 */
.theme-color-selector {
  position: relative;
}

.theme-color-selector option {
  padding: 0.5rem;
}

/* 主题切换动画增强 */
.navbar-nav .nav-link {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.dropdown-menu {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 主题预览提示样式 */
.theme-preview-toast {
  background: linear-gradient(45deg, var(--theme-primary), var(--theme-primary-light));
  color: white;
  border: none;
}

/* 响应式主题切换器 */
@media (max-width: 768px) {
  .theme-preview {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  .theme-option {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

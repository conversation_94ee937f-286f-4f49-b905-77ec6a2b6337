/* 专业主题颜色系统 - 基于色彩心理学和设计原理 */
:root {
  /* 默认主题颜色 (Ocean Blue - 海洋蓝) */
  --theme-primary: #2563eb;
  --theme-primary-50: #eff6ff;
  --theme-primary-100: #dbeafe;
  --theme-primary-200: #bfdbfe;
  --theme-primary-300: #93c5fd;
  --theme-primary-400: #60a5fa;
  --theme-primary-500: #3b82f6;
  --theme-primary-600: #2563eb;
  --theme-primary-700: #1d4ed8;
  --theme-primary-800: #1e40af;
  --theme-primary-900: #1e3a8a;
  --theme-primary-rgb: 37, 99, 235;

  /* 语义化颜色 */
  --theme-success: #059669;
  --theme-success-light: #10b981;
  --theme-success-dark: #047857;
  --theme-info: #0891b2;
  --theme-info-light: #06b6d4;
  --theme-info-dark: #0e7490;
  --theme-warning: #d97706;
  --theme-warning-light: #f59e0b;
  --theme-warning-dark: #b45309;
  --theme-danger: #dc2626;
  --theme-danger-light: #ef4444;
  --theme-danger-dark: #b91c1c;

  /* 中性色调 */
  --theme-gray-50: #f9fafb;
  --theme-gray-100: #f3f4f6;
  --theme-gray-200: #e5e7eb;
  --theme-gray-300: #d1d5db;
  --theme-gray-400: #9ca3af;
  --theme-gray-500: #6b7280;
  --theme-gray-600: #4b5563;
  --theme-gray-700: #374151;
  --theme-gray-800: #1f2937;
  --theme-gray-900: #111827;
}

/* 专业主题配色方案 */

/* 1. 海洋蓝主题 (Ocean Blue) - 专业、信任、稳定 */
[data-theme="primary"] {
  --theme-primary: #2563eb;
  --theme-primary-light: #3b82f6;
  --theme-primary-dark: #1d4ed8;
  --theme-primary-rgb: 37, 99, 235;
  --theme-accent: #06b6d4;
  --theme-surface: #f8fafc;
  --theme-surface-dark: #e2e8f0;
}

/* 2. 现代灰主题 (Modern Gray) - 简约、专业、平衡 */
[data-theme="secondary"] {
  --theme-primary: #64748b;
  --theme-primary-light: #94a3b8;
  --theme-primary-dark: #475569;
  --theme-primary-rgb: 100, 116, 139;
  --theme-accent: #8b5cf6;
  --theme-surface: #f8fafc;
  --theme-surface-dark: #e2e8f0;
}

/* 3. 自然绿主题 (Nature Green) - 健康、成长、和谐 */
[data-theme="success"] {
  --theme-primary: #059669;
  --theme-primary-light: #10b981;
  --theme-primary-dark: #047857;
  --theme-primary-rgb: 5, 150, 105;
  --theme-accent: #84cc16;
  --theme-surface: #f0fdf4;
  --theme-surface-dark: #dcfce7;
}

/* 4. 活力橙主题 (Energy Orange) - 活力、创新、温暖 */
[data-theme="warning"] {
  --theme-primary: #ea580c;
  --theme-primary-light: #f97316;
  --theme-primary-dark: #c2410c;
  --theme-primary-rgb: 234, 88, 12;
  --theme-accent: #eab308;
  --theme-surface: #fffbeb;
  --theme-surface-dark: #fef3c7;
}

/* 5. 优雅紫主题 (Elegant Purple) - 创新、优雅、神秘 */
[data-theme="info"] {
  --theme-primary: #7c3aed;
  --theme-primary-light: #8b5cf6;
  --theme-primary-dark: #6d28d9;
  --theme-primary-rgb: 124, 58, 237;
  --theme-accent: #ec4899;
  --theme-surface: #faf5ff;
  --theme-surface-dark: #f3e8ff;
}

/* 6. 深邃红主题 (Deep Red) - 力量、重要、警示 */
[data-theme="danger"] {
  --theme-primary: #dc2626;
  --theme-primary-light: #ef4444;
  --theme-primary-dark: #b91c1c;
  --theme-primary-rgb: 220, 38, 38;
  --theme-accent: #f59e0b;
  --theme-surface: #fef2f2;
  --theme-surface-dark: #fecaca;
}

/* 7. 深色主题 (Dark Mode) - 现代、护眼、专业 */
[data-theme="dark"] {
  --theme-primary: #3b82f6;
  --theme-primary-light: #60a5fa;
  --theme-primary-dark: #2563eb;
  --theme-primary-rgb: 59, 130, 246;
  --theme-accent: #10b981;
  --theme-surface: #1f2937;
  --theme-surface-dark: #111827;
  --theme-text: #f9fafb;
  --theme-text-secondary: #d1d5db;

  /* 深色主题特殊变量 */
  --bs-body-bg: #111827;
  --bs-body-color: #f9fafb;
  --bs-border-color: #374151;
}

/* 专业界面元素样式应用 */

/* 导航栏样式 */
.navbar {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
}

.navbar-brand {
  color: white !important;
  font-weight: 600;
  font-size: 1.25rem;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 0 2px;
  padding: 8px 12px !important;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--theme-primary-dark) 0%, var(--theme-primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--theme-primary-rgb), 0.4);
}

.btn-primary:focus,
.btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.5);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.3);
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  color: white;
  border: none;
  border-radius: 12px 12px 0 0 !important;
  font-weight: 600;
  padding: 1rem 1.5rem;
}

.card-header.bg-primary {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%) !important;
}

/* 表单控件样式 */
.form-control {
  border: 2px solid var(--theme-gray-200, #e5e7eb);
  border-radius: 8px;
  padding: 12px 16px;
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.15);
  background-color: var(--theme-surface, #ffffff);
}

/* 徽章样式 */
.badge-primary {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
  border-radius: 6px;
  font-weight: 500;
  padding: 6px 10px;
}

/* 文本颜色 */
.text-primary {
  color: var(--theme-primary) !important;
}

/* 边框颜色 */
.border-primary {
  border-color: var(--theme-primary) !important;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--theme-primary-dark);
  text-decoration: none;
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 8px;
  background-color: var(--theme-surface, #ffffff);
}

.dropdown-item {
  border-radius: 8px;
  padding: 10px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.dropdown-item:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  color: var(--theme-primary);
  transform: translateX(4px);
}

.dropdown-item.active,
.dropdown-item:active {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
}

/* 表格样式 */
.table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table thead th {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  color: white;
  border: none;
  font-weight: 600;
  padding: 16px;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.05);
  transform: scale(1.01);
}

.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

/* 分页样式 */
.pagination {
  gap: 4px;
}

.pagination .page-link {
  color: var(--theme-primary);
  border: 2px solid var(--theme-gray-200, #e5e7eb);
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  border-color: var(--theme-primary);
  color: white;
  box-shadow: 0 4px 8px rgba(var(--theme-primary-rgb), 0.3);
}

/* 警告框样式 */
.alert {
  border: none;
  border-radius: 12px;
  padding: 16px 20px;
  font-weight: 500;
}

.alert-primary {
  background: linear-gradient(135deg, rgba(var(--theme-primary-rgb), 0.1) 0%, rgba(var(--theme-primary-rgb), 0.05) 100%);
  color: var(--theme-primary-dark);
  border-left: 4px solid var(--theme-primary);
}

/* 进度条样式 */
.progress {
  height: 8px;
  border-radius: 8px;
  background-color: var(--theme-gray-200, #e5e7eb);
}

.progress-bar {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 图标样式 */
.text-primary i {
  color: var(--theme-primary) !important;
}

/* 全局动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* 深色主题特殊处理 */
[data-theme="dark"] {
  --bs-body-bg: #212529;
  --bs-body-color: #fff;
}

[data-theme="dark"] .card {
  background-color: #343a40;
  color: #fff;
}

[data-theme="dark"] .navbar {
  background-color: var(--theme-primary) !important;
}

[data-theme="dark"] .table {
  color: #fff;
}

[data-theme="dark"] .form-control {
  background-color: #495057;
  border-color: #6c757d;
  color: #fff;
}

[data-theme="dark"] .form-control:focus {
  background-color: #495057;
  border-color: var(--theme-primary);
  color: #fff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-nav .nav-link.active {
    border-radius: 0.25rem;
    margin: 0.25rem;
  }
}

/* 主题切换按钮样式 */
.theme-selector {
  position: relative;
}

.theme-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  vertical-align: middle;
}

/* 主题预览颜色 - 与新配色方案匹配 */
.theme-preview.primary {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}
.theme-preview.secondary {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  box-shadow: 0 2px 4px rgba(100, 116, 139, 0.3);
}
.theme-preview.success {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);
}
.theme-preview.warning {
  background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
  box-shadow: 0 2px 4px rgba(234, 88, 12, 0.3);
}
.theme-preview.info {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  box-shadow: 0 2px 4px rgba(124, 58, 237, 0.3);
}
.theme-preview.danger {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}
.theme-preview.dark {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  box-shadow: 0 2px 4px rgba(31, 41, 55, 0.3);
}

/* 主题切换器下拉菜单样式 */
#themeDropdown {
  color: var(--theme-primary) !important;
  transition: color 0.3s ease;
}

#themeDropdown:hover {
  color: var(--theme-primary-dark) !important;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  text-decoration: none;
}

.theme-option.active {
  background-color: var(--theme-primary);
  color: white;
}

.theme-option.active .theme-preview {
  border-color: white;
}

/* 主题切换器在系统设置页面的样式 */
.theme-color-selector {
  position: relative;
}

.theme-color-selector option {
  padding: 0.5rem;
}

/* 主题切换动画增强 */
.navbar-nav .nav-link {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.dropdown-menu {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 主题预览提示样式 */
.theme-preview-toast {
  background: linear-gradient(45deg, var(--theme-primary), var(--theme-primary-light));
  color: white;
  border: none;
}

/* 响应式主题切换器 */
@media (max-width: 768px) {
  .theme-preview {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  .theme-option {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

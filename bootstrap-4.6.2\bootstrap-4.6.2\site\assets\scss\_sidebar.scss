//
// Left side navigation
//

.bd-sidebar {
  order: 0;
  // background-color: #f5f2f9;
  border-bottom: 1px solid rgba(0, 0, 0, .1);

  @include media-breakpoint-up(md) {
    @supports (position: sticky) {
      position: sticky;
      top: 4rem;
      z-index: 1000;
      height: subtract(100vh, 4rem);
    }
    border-right: 1px solid rgba(0, 0, 0, .1);
  }

  @include media-breakpoint-up(xl) {
    flex: 0 1 320px;
  }
}

.bd-links {
  width: 100%;
  padding-top: 1rem;
  padding-bottom: 1rem;
  border-top: 1px solid rgba(0, 0, 0, .05);

  @include media-breakpoint-up(md) {
    @supports (position: sticky) {
      max-height: subtract(100vh, 9rem);
      overflow-y: auto;
    }
  }
}

.bd-search {
  position: relative; // To contain the Algolia search
  padding: 1rem 15px;
  margin-right: -15px;
  margin-left: -15px;

  .form-control:focus {
    border-color: $bd-purple-bright;
    box-shadow: 0 0 0 3px rgba($bd-purple-bright, .25);
  }
}

.bd-search-docs-toggle {
  color: $gray-900;
}

.bd-sidenav {
  display: none;
}

.bd-toc-link {
  display: block;
  padding: .25rem 1.5rem;
  font-weight: 600;
  color: rgba(0, 0, 0, .65);

  &:hover {
    color: rgba(0, 0, 0, .85);
    text-decoration: none;
  }
}

.bd-toc-item {
  &.active {
    margin-bottom: 1rem;

    &:not(:first-child) {
      margin-top: 1rem;
    }

    > .bd-toc-link {
      color: rgba(0, 0, 0, .85);

      &:hover {
        background-color: transparent;
      }
    }

    > .bd-sidenav {
      display: block;
    }
  }
}

// All levels of nav
.bd-sidebar .nav > li > a {
  display: block;
  padding: .25rem 1.5rem;
  @include font-size(90%);
  color: rgba(0, 0, 0, .65);
}

.bd-sidebar .nav > li > a:hover {
  color: rgba(0, 0, 0, .85);
  text-decoration: none;
  background-color: transparent;
}

.bd-sidebar .nav > .active > a,
.bd-sidebar .nav > .active:hover > a {
  font-weight: 600;
  color: rgba(0, 0, 0, .85);
  background-color: transparent;
}

<!doctype html>
<html lang="en">
  <head>
    {{ partial "header" . }}
  </head>
  <body>
    {{ partial "skippy" . }}

    {{ partial "docs-navbar" . }}

    <div class="container-fluid">
      <div class="row flex-xl-nowrap">
        <div class="col-md-3 col-xl-2 bd-sidebar">
          {{ partial "docs-sidebar" . }}
        </div>

        {{ if (eq .Page.Params.toc true) }}
          <nav class="d-none d-xl-block col-xl-2 bd-toc" aria-label="Secondary navigation">
            {{ .TableOfContents }}
          </nav>
        {{ end }}

        <main class="col-md-9 col-xl-8 py-md-3 pl-md-5 bd-content" role="main">
          <div class="d-md-flex flex-md-row-reverse align-items-center justify-content-between">
            <a class="btn btn-sm btn-bd-light my-2 my-md-0" href="{{ .Site.Params.repo }}/blob/v4-dev/site/content/{{ .Page.File.Path | replaceRE `\\` "/" }}" title="View and edit this file on GitHub" target="_blank" rel="noopener">View on GitHub</a>
            <h1 class="bd-title" id="content">{{ .Title | markdownify }}</h1>
          </div>
          <p class="bd-lead">{{ .Page.Params.Description | markdownify }}</p>
          {{ partial "ads" . }}
          {{ .Content }}
        </main>
      </div>
    </div>

    {{ partial "scripts" . }}
  </body>
</html>

{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(CLASS_NAME_SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || $(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(EVENT_SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Totally disable Popper for Dropdowns in Navbar\n    if (!this._inNavbar && usePopper) {\n      // Check for Popper dependency\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(SELECTOR_NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(CLASS_NAME_DISABLED) || !$(this._menu).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(CLASS_NAME_SHOW)\n    $(parent)\n      .toggleClass(CLASS_NAME_SHOW)\n      .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n  _addEventListeners() {\n    $(this._element).on(EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(SELECTOR_MENU)\n      }\n    }\n\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(CLASS_NAME_DROPUP)) {\n      placement = $(this._menu).hasClass(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if ($parentDropdown.hasClass(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if ($(this._menu).hasClass(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element)\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(EVENT_HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(CLASS_NAME_SHOW)\n      $(parent)\n        .removeClass(CLASS_NAME_SHOW)\n        .trigger($.Event(EVENT_HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(SELECTOR_MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    if (this.disabled || $(this).hasClass(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(CLASS_NAME_SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (!isActive || (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        $(parent.querySelector(SELECTOR_DATA_TOGGLE)).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(SELECTOR_VISIBLE_ITEMS))\n      .filter(item => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${EVENT_CLICK_DATA_API} ${EVENT_KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => {\n    e.stopPropagation()\n  })\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\nexport default Dropdown\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "RegExp", "CLASS_NAME_DISABLED", "CLASS_NAME_SHOW", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_POSITION_STATIC", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "offset", "flip", "boundary", "reference", "display", "popperConfig", "DefaultType", "Dropdown", "element", "config", "_element", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "toggle", "disabled", "hasClass", "isActive", "_clearMenus", "show", "usePopper", "relatedTarget", "showEvent", "Event", "parent", "_getParentFromElement", "trigger", "isDefaultPrevented", "<PERSON><PERSON>", "TypeError", "referenceElement", "<PERSON><PERSON>", "isElement", "j<PERSON>y", "addClass", "_getPopperConfig", "document", "documentElement", "closest", "length", "body", "children", "on", "noop", "focus", "setAttribute", "toggleClass", "hide", "hideEvent", "destroy", "dispose", "removeData", "off", "update", "scheduleUpdate", "event", "preventDefault", "stopPropagation", "constructor", "data", "typeCheckConfig", "querySelector", "_getPlacement", "$parentDropdown", "parentNode", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "_jQueryInterface", "each", "which", "type", "toggles", "slice", "call", "querySelectorAll", "i", "len", "context", "clickEvent", "dropdownMenu", "test", "target", "tagName", "contains", "removeClass", "selector", "getSelectorFromElement", "_dataApiKeydownHandler", "items", "filter", "item", "is", "index", "indexOf", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAWA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EACA,IAAMQ,cAAc,GAAG,EAAvB;;EACA,IAAMC,aAAa,GAAG,EAAtB;;EACA,IAAMC,WAAW,GAAG,CAApB;;EACA,IAAMC,gBAAgB,GAAG,EAAzB;;EACA,IAAMC,kBAAkB,GAAG,EAA3B;;EACA,IAAMC,wBAAwB,GAAG,CAAjC;;EACA,IAAMC,cAAc,GAAG,IAAIC,MAAJ,CAAcJ,gBAAd,GAAkCC,GAAAA,GAAAA,kBAAlC,GAAwDJ,GAAAA,GAAAA,cAAxD,CAAvB,CAAA;EAEA,IAAMQ,mBAAmB,GAAG,UAA5B,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,IAAMC,oBAAoB,GAAG,WAA7B,CAAA;EACA,IAAMC,mBAAmB,GAAG,UAA5B,CAAA;EACA,IAAMC,oBAAoB,GAAG,qBAA7B,CAAA;EACA,IAAMC,0BAA0B,GAAG,iBAAnC,CAAA;EAEA,IAAMC,UAAU,YAAUpB,SAA1B,CAAA;EACA,IAAMqB,YAAY,cAAYrB,SAA9B,CAAA;EACA,IAAMsB,UAAU,YAAUtB,SAA1B,CAAA;EACA,IAAMuB,WAAW,aAAWvB,SAA5B,CAAA;EACA,IAAMwB,WAAW,aAAWxB,SAA5B,CAAA;EACA,IAAMyB,oBAAoB,GAAA,OAAA,GAAWzB,SAAX,GAAuBC,YAAjD,CAAA;EACA,IAAMyB,sBAAsB,GAAA,SAAA,GAAa1B,SAAb,GAAyBC,YAArD,CAAA;EACA,IAAM0B,oBAAoB,GAAA,OAAA,GAAW3B,SAAX,GAAuBC,YAAjD,CAAA;EAEA,IAAM2B,oBAAoB,GAAG,0BAA7B,CAAA;EACA,IAAMC,mBAAmB,GAAG,gBAA5B,CAAA;EACA,IAAMC,aAAa,GAAG,gBAAtB,CAAA;EACA,IAAMC,mBAAmB,GAAG,aAA5B,CAAA;EACA,IAAMC,sBAAsB,GAAG,6DAA/B,CAAA;EAEA,IAAMC,aAAa,GAAG,WAAtB,CAAA;EACA,IAAMC,gBAAgB,GAAG,SAAzB,CAAA;EACA,IAAMC,gBAAgB,GAAG,cAAzB,CAAA;EACA,IAAMC,mBAAmB,GAAG,YAA5B,CAAA;EACA,IAAMC,eAAe,GAAG,aAAxB,CAAA;EACA,IAAMC,cAAc,GAAG,YAAvB,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,CADM;EAEdC,EAAAA,IAAI,EAAE,IAFQ;EAGdC,EAAAA,QAAQ,EAAE,cAHI;EAIdC,EAAAA,SAAS,EAAE,QAJG;EAKdC,EAAAA,OAAO,EAAE,SALK;EAMdC,EAAAA,YAAY,EAAE,IAAA;EANA,CAAhB,CAAA;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,MAAM,EAAE,0BADU;EAElBC,EAAAA,IAAI,EAAE,SAFY;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,kBAJO;EAKlBC,EAAAA,OAAO,EAAE,QALS;EAMlBC,EAAAA,YAAY,EAAE,eAAA;EANI,CAApB,CAAA;EASA;EACA;EACA;;MAEME;IACJ,SAAYC,QAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;MAC3B,IAAKC,CAAAA,QAAL,GAAgBF,OAAhB,CAAA;MACA,IAAKG,CAAAA,OAAL,GAAe,IAAf,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBJ,MAAhB,CAAf,CAAA;EACA,IAAA,IAAA,CAAKK,KAAL,GAAa,IAAKC,CAAAA,eAAL,EAAb,CAAA;EACA,IAAA,IAAA,CAAKC,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;;EAEA,IAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;EACD;;;;;EAeD;EACAC,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;EACP,IAAA,IAAI,IAAKT,CAAAA,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBW,QAAjB,CAA0BhD,mBAA1B,CAA9B,EAA8E;EAC5E,MAAA,OAAA;EACD,KAAA;;MAED,IAAMiD,QAAQ,GAAG3D,qBAAC,CAAC,IAAA,CAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuB/C,eAAvB,CAAjB,CAAA;;EAEAiC,IAAAA,QAAQ,CAACgB,WAAT,EAAA,CAAA;;EAEA,IAAA,IAAID,QAAJ,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;MAED,IAAKE,CAAAA,IAAL,CAAU,IAAV,CAAA,CAAA;;;WAGFA,OAAA,SAAKC,IAAAA,CAAAA,SAAL,EAAwB;EAAA,IAAA,IAAnBA,SAAmB,KAAA,KAAA,CAAA,EAAA;EAAnBA,MAAAA,SAAmB,GAAP,KAAO,CAAA;EAAA,KAAA;;MACtB,IAAI,IAAA,CAAKf,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBW,QAAjB,CAA0BhD,mBAA1B,CAA1B,IAA4EV,qBAAC,CAAC,IAAKmD,CAAAA,KAAN,CAAD,CAAcO,QAAd,CAAuB/C,eAAvB,CAAhF,EAAyH;EACvH,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMoD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,IAAKhB,CAAAA,QAAAA;OADtB,CAAA;MAGA,IAAMiB,SAAS,GAAGhE,qBAAC,CAACiE,KAAF,CAAQ9C,UAAR,EAAoB4C,aAApB,CAAlB,CAAA;;MACA,IAAMG,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,IAAA,CAAKpB,QAApC,CAAf,CAAA;;EAEA/C,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBJ,SAAlB,CAAA,CAAA;;EAEA,IAAA,IAAIA,SAAS,CAACK,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAfqB;;;EAkBtB,IAAA,IAAI,CAAC,IAAA,CAAKhB,SAAN,IAAmBS,SAAvB,EAAkC;EAChC;EACA,MAAA,IAAI,OAAOQ,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,QAAA,MAAM,IAAIC,SAAJ,CAAc,+DAAd,CAAN,CAAA;EACD,OAAA;;QAED,IAAIC,gBAAgB,GAAG,IAAA,CAAKzB,QAA5B,CAAA;;EAEA,MAAA,IAAI,KAAKE,OAAL,CAAaT,SAAb,KAA2B,QAA/B,EAAyC;EACvCgC,QAAAA,gBAAgB,GAAGN,MAAnB,CAAA;SADF,MAEO,IAAIO,wBAAI,CAACC,SAAL,CAAe,IAAA,CAAKzB,OAAL,CAAaT,SAA5B,CAAJ,EAA4C;EACjDgC,QAAAA,gBAAgB,GAAG,IAAKvB,CAAAA,OAAL,CAAaT,SAAhC,CADiD;;UAIjD,IAAI,OAAO,KAAKS,OAAL,CAAaT,SAAb,CAAuBmC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDH,UAAAA,gBAAgB,GAAG,IAAKvB,CAAAA,OAAL,CAAaT,SAAb,CAAuB,CAAvB,CAAnB,CAAA;EACD,SAAA;EACF,OAjB+B;EAoBhC;EACA;;;EACA,MAAA,IAAI,KAAKS,OAAL,CAAaV,QAAb,KAA0B,cAA9B,EAA8C;EAC5CvC,QAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUU,QAAV,CAAmB5D,0BAAnB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKgC,OAAL,GAAe,IAAIsB,0BAAJ,CAAWE,gBAAX,EAA6B,IAAA,CAAKrB,KAAlC,EAAyC,IAAK0B,CAAAA,gBAAL,EAAzC,CAAf,CAAA;EACD,KA7CqB;EAgDtB;EACA;EACA;;;EACA,IAAA,IAAI,kBAAkBC,QAAQ,CAACC,eAA3B,IACA/E,qBAAC,CAACkE,MAAD,CAAD,CAAUc,OAAV,CAAkBpD,mBAAlB,EAAuCqD,MAAvC,KAAkD,CADtD,EACyD;EACvDjF,MAAAA,qBAAC,CAAC8E,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,EAAA,CAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDpF,qBAAC,CAACqF,IAApD,CAAA,CAAA;EACD,KAAA;;MAED,IAAKtC,CAAAA,QAAL,CAAcuC,KAAd,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKvC,QAAL,CAAcwC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C,CAAA,CAAA;;EAEAvF,IAAAA,qBAAC,CAAC,IAAKmD,CAAAA,KAAN,CAAD,CAAcqC,WAAd,CAA0B7E,eAA1B,CAAA,CAAA;EACAX,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGsB,WADH,CACe7E,eADf,CAAA,CAEGyD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQ7C,WAAR,EAAqB2C,aAArB,CAFX,CAAA,CAAA;;;EAKF0B,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;MACL,IAAI,IAAA,CAAK1C,QAAL,CAAcU,QAAd,IAA0BzD,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBW,QAAjB,CAA0BhD,mBAA1B,CAA1B,IAA4E,CAACV,qBAAC,CAAC,IAAKmD,CAAAA,KAAN,CAAD,CAAcO,QAAd,CAAuB/C,eAAvB,CAAjF,EAA0H;EACxH,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMoD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,IAAKhB,CAAAA,QAAAA;OADtB,CAAA;MAGA,IAAM2C,SAAS,GAAG1F,qBAAC,CAACiE,KAAF,CAAQhD,UAAR,EAAoB8C,aAApB,CAAlB,CAAA;;MACA,IAAMG,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,IAAA,CAAKpB,QAApC,CAAf,CAAA;;EAEA/C,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB,CAAA,CAAA;;EAEA,IAAA,IAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKrB,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAa2C,OAAb,EAAA,CAAA;EACD,KAAA;;EAED3F,IAAAA,qBAAC,CAAC,IAAKmD,CAAAA,KAAN,CAAD,CAAcqC,WAAd,CAA0B7E,eAA1B,CAAA,CAAA;EACAX,IAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGsB,WADH,CACe7E,eADf,CAAA,CAEGyD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQ/C,YAAR,EAAsB6C,aAAtB,CAFX,CAAA,CAAA;;;EAKF6B,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACR5F,IAAAA,qBAAC,CAAC6F,UAAF,CAAa,IAAK9C,CAAAA,QAAlB,EAA4BnD,QAA5B,CAAA,CAAA;EACAI,IAAAA,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiB+C,GAAjB,CAAqBjG,SAArB,CAAA,CAAA;MACA,IAAKkD,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKI,CAAAA,KAAL,GAAa,IAAb,CAAA;;EACA,IAAA,IAAI,IAAKH,CAAAA,OAAL,KAAiB,IAArB,EAA2B;QACzB,IAAKA,CAAAA,OAAL,CAAa2C,OAAb,EAAA,CAAA;;QACA,IAAK3C,CAAAA,OAAL,GAAe,IAAf,CAAA;EACD,KAAA;;;EAGH+C,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;EACP,IAAA,IAAA,CAAK1C,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;;EACA,IAAA,IAAI,IAAKN,CAAAA,OAAL,KAAiB,IAArB,EAA2B;QACzB,IAAKA,CAAAA,OAAL,CAAagD,cAAb,EAAA,CAAA;EACD,KAAA;EACF;;;EAGDzC,EAAAA,MAAAA,CAAAA,qBAAA,SAAqB,kBAAA,GAAA;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;MACnBvD,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBqC,EAAjB,CAAoB/D,WAApB,EAAiC,UAAA4E,KAAK,EAAI;EACxCA,MAAAA,KAAK,CAACC,cAAN,EAAA,CAAA;EACAD,MAAAA,KAAK,CAACE,eAAN,EAAA,CAAA;;EACA,MAAA,KAAI,CAAC3C,MAAL,EAAA,CAAA;OAHF,CAAA,CAAA;;;WAOFN,aAAA,SAAWJ,UAAAA,CAAAA,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACD,IAAKsD,CAAAA,WAAL,CAAiBhE,OADhB,EAEDpC,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBsD,IAAjB,EAFC,EAGDvD,MAHC,CAAN,CAAA;MAMA2B,wBAAI,CAAC6B,eAAL,CACE5G,IADF,EAEEoD,MAFF,EAGE,IAAA,CAAKsD,WAAL,CAAiBzD,WAHnB,CAAA,CAAA;EAMA,IAAA,OAAOG,MAAP,CAAA;;;EAGFM,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;MAChB,IAAI,CAAC,IAAKD,CAAAA,KAAV,EAAiB;QACf,IAAMe,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,IAAA,CAAKpB,QAApC,CAAf,CAAA;;EAEA,MAAA,IAAImB,MAAJ,EAAY;EACV,QAAA,IAAA,CAAKf,KAAL,GAAae,MAAM,CAACqC,aAAP,CAAqB5E,aAArB,CAAb,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,OAAO,KAAKwB,KAAZ,CAAA;;;EAGFqD,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;MACd,IAAMC,eAAe,GAAGzG,qBAAC,CAAC,KAAK+C,QAAL,CAAc2D,UAAf,CAAzB,CAAA;EACA,IAAA,IAAIC,SAAS,GAAG3E,gBAAhB,CAFc;;EAKd,IAAA,IAAIyE,eAAe,CAAC/C,QAAhB,CAAyB9C,iBAAzB,CAAJ,EAAiD;EAC/C+F,MAAAA,SAAS,GAAG3G,qBAAC,CAAC,IAAA,CAAKmD,KAAN,CAAD,CAAcO,QAAd,CAAuB3C,oBAAvB,CACVgB,GAAAA,gBADU,GAEVD,aAFF,CAAA;OADF,MAIO,IAAI2E,eAAe,CAAC/C,QAAhB,CAAyB7C,oBAAzB,CAAJ,EAAoD;EACzD8F,MAAAA,SAAS,GAAGzE,eAAZ,CAAA;OADK,MAEA,IAAIuE,eAAe,CAAC/C,QAAhB,CAAyB5C,mBAAzB,CAAJ,EAAmD;EACxD6F,MAAAA,SAAS,GAAGxE,cAAZ,CAAA;EACD,KAFM,MAEA,IAAInC,qBAAC,CAAC,IAAKmD,CAAAA,KAAN,CAAD,CAAcO,QAAd,CAAuB3C,oBAAvB,CAAJ,EAAkD;EACvD4F,MAAAA,SAAS,GAAG1E,mBAAZ,CAAA;EACD,KAAA;;EAED,IAAA,OAAO0E,SAAP,CAAA;;;EAGFrD,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,OAAOtD,qBAAC,CAAC,IAAK+C,CAAAA,QAAN,CAAD,CAAiBiC,OAAjB,CAAyB,SAAzB,CAAA,CAAoCC,MAApC,GAA6C,CAApD,CAAA;;;EAGF2B,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACX,IAAMvE,MAAM,GAAG,EAAf,CAAA;;EAEA,IAAA,IAAI,OAAO,IAAKY,CAAAA,OAAL,CAAaZ,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAACpC,EAAP,GAAY,UAAAoG,IAAI,EAAI;UAClBA,IAAI,CAACQ,OAAL,GACKR,QAAAA,CAAAA,EAAAA,EAAAA,IAAI,CAACQ,OADV,EAEK,MAAI,CAAC5D,OAAL,CAAaZ,MAAb,CAAoBgE,IAAI,CAACQ,OAAzB,EAAkC,MAAI,CAAC9D,QAAvC,CAFL,CAAA,CAAA;EAKA,QAAA,OAAOsD,IAAP,CAAA;SANF,CAAA;EAQD,KATD,MASO;EACLhE,MAAAA,MAAM,CAACA,MAAP,GAAgB,IAAKY,CAAAA,OAAL,CAAaZ,MAA7B,CAAA;EACD,KAAA;;EAED,IAAA,OAAOA,MAAP,CAAA;;;EAGFwC,EAAAA,MAAAA,CAAAA,mBAAA,SAAmB,gBAAA,GAAA;EACjB,IAAA,IAAMnC,YAAY,GAAG;QACnBiE,SAAS,EAAE,IAAKH,CAAAA,aAAL,EADQ;EAEnBM,MAAAA,SAAS,EAAE;UACTzE,MAAM,EAAE,IAAKuE,CAAAA,UAAL,EADC;EAETtE,QAAAA,IAAI,EAAE;YACJyE,OAAO,EAAE,IAAK9D,CAAAA,OAAL,CAAaX,IAAAA;WAHf;EAKT0E,QAAAA,eAAe,EAAE;YACfC,iBAAiB,EAAE,IAAKhE,CAAAA,OAAL,CAAaV,QAAAA;EADjB,SAAA;EALR,OAAA;EAFQ,KAArB,CADiB;;EAejB,IAAA,IAAI,KAAKU,OAAL,CAAaR,OAAb,KAAyB,QAA7B,EAAuC;EACrCC,MAAAA,YAAY,CAACoE,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE,KAAA;SADX,CAAA;EAGD,KAAA;;EAED,IAAA,OAAA,QAAA,CAAA,EAAA,EACKrE,YADL,EAEK,IAAKO,CAAAA,OAAL,CAAaP,YAFlB,CAAA,CAAA;EAID;;;aAGMyE,mBAAP,SAAwBrE,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKsE,IAAL,CAAU,YAAY;QAC3B,IAAIf,IAAI,GAAGrG,qBAAC,CAAC,IAAD,CAAD,CAAQqG,IAAR,CAAazG,QAAb,CAAX,CAAA;;QACA,IAAMqD,OAAO,GAAG,OAAOH,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD,CAAA;;QAEA,IAAI,CAACuD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIzD,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP,CAAA;UACAjD,qBAAC,CAAC,IAAD,CAAD,CAAQqG,IAAR,CAAazG,QAAb,EAAuByG,IAAvB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOvD,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOuD,IAAI,CAACvD,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIyB,SAAJ,CAAkCzB,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDuD,IAAI,CAACvD,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAhBM,CAAP,CAAA;;;aAmBKc,cAAP,SAAmBqC,WAAAA,CAAAA,KAAnB,EAA0B;MACxB,IAAIA,KAAK,KAAKA,KAAK,CAACoB,KAAN,KAAgB9G,wBAAhB,IACZ0F,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBjH,WADnC,CAAT,EAC0D;EACxD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMmH,OAAO,GAAG,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAAc3C,QAAQ,CAAC4C,gBAAT,CAA0BjG,oBAA1B,CAAd,CAAhB,CAAA;;EAEA,IAAA,KAAK,IAAIkG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGL,OAAO,CAACtC,MAA9B,EAAsC0C,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;QAClD,IAAMzD,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+BoD,OAAO,CAACI,CAAD,CAAtC,CAAf,CAAA;;EACA,MAAA,IAAME,OAAO,GAAG7H,qBAAC,CAACuH,OAAO,CAACI,CAAD,CAAR,CAAD,CAActB,IAAd,CAAmBzG,QAAnB,CAAhB,CAAA;EACA,MAAA,IAAMmE,aAAa,GAAG;UACpBA,aAAa,EAAEwD,OAAO,CAACI,CAAD,CAAA;SADxB,CAAA;;EAIA,MAAA,IAAI1B,KAAK,IAAIA,KAAK,CAACqB,IAAN,KAAe,OAA5B,EAAqC;UACnCvD,aAAa,CAAC+D,UAAd,GAA2B7B,KAA3B,CAAA;EACD,OAAA;;QAED,IAAI,CAAC4B,OAAL,EAAc;EACZ,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,IAAME,YAAY,GAAGF,OAAO,CAAC1E,KAA7B,CAAA;;QACA,IAAI,CAACnD,qBAAC,CAACkE,MAAD,CAAD,CAAUR,QAAV,CAAmB/C,eAAnB,CAAL,EAA0C;EACxC,QAAA,SAAA;EACD,OAAA;;EAED,MAAA,IAAIsF,KAAK,KAAKA,KAAK,CAACqB,IAAN,KAAe,OAAf,IACV,iBAAA,CAAkBU,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,CADU,IACsCjC,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBjH,WADrF,CAAL,IAEAJ,qBAAC,CAACmI,QAAF,CAAWjE,MAAX,EAAmB+B,KAAK,CAACgC,MAAzB,CAFJ,EAEsC;EACpC,QAAA,SAAA;EACD,OAAA;;QAED,IAAMvC,SAAS,GAAG1F,qBAAC,CAACiE,KAAF,CAAQhD,UAAR,EAAoB8C,aAApB,CAAlB,CAAA;EACA/D,MAAAA,qBAAC,CAACkE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB,CAAA,CAAA;;EACA,MAAA,IAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC,QAAA,SAAA;EACD,OA9BiD;EAiClD;;;EACA,MAAA,IAAI,cAAkBS,IAAAA,QAAQ,CAACC,eAA/B,EAAgD;EAC9C/E,QAAAA,qBAAC,CAAC8E,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,EAAA,CAA4BW,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmD9F,qBAAC,CAACqF,IAArD,CAAA,CAAA;EACD,OAAA;;QAEDkC,OAAO,CAACI,CAAD,CAAP,CAAWpC,YAAX,CAAwB,eAAxB,EAAyC,OAAzC,CAAA,CAAA;;QAEA,IAAIsC,OAAO,CAAC7E,OAAZ,EAAqB;UACnB6E,OAAO,CAAC7E,OAAR,CAAgB2C,OAAhB,EAAA,CAAA;EACD,OAAA;;EAED3F,MAAAA,qBAAC,CAAC+H,YAAD,CAAD,CAAgBK,WAAhB,CAA4BzH,eAA5B,CAAA,CAAA;EACAX,MAAAA,qBAAC,CAACkE,MAAD,CAAD,CACGkE,WADH,CACezH,eADf,CAAA,CAEGyD,OAFH,CAEWpE,qBAAC,CAACiE,KAAF,CAAQ/C,YAAR,EAAsB6C,aAAtB,CAFX,CAAA,CAAA;EAGD,KAAA;;;aAGII,wBAAP,SAA6BtB,qBAAAA,CAAAA,OAA7B,EAAsC;EACpC,IAAA,IAAIqB,MAAJ,CAAA;EACA,IAAA,IAAMmE,QAAQ,GAAG5D,wBAAI,CAAC6D,sBAAL,CAA4BzF,OAA5B,CAAjB,CAAA;;EAEA,IAAA,IAAIwF,QAAJ,EAAc;EACZnE,MAAAA,MAAM,GAAGY,QAAQ,CAACyB,aAAT,CAAuB8B,QAAvB,CAAT,CAAA;EACD,KAAA;;EAED,IAAA,OAAOnE,MAAM,IAAIrB,OAAO,CAAC6D,UAAzB,CAAA;EACD;;;aAGM6B,yBAAP,SAA8BtC,sBAAAA,CAAAA,KAA9B,EAAqC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;MACA,IAAI,iBAAA,CAAkB+B,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,CAAA,GACFjC,KAAK,CAACoB,KAAN,KAAgBlH,aAAhB,IAAiC8F,KAAK,CAACoB,KAAN,KAAgBnH,cAAhB,KAChC+F,KAAK,CAACoB,KAAN,KAAgB/G,kBAAhB,IAAsC2F,KAAK,CAACoB,KAAN,KAAgBhH,gBAAtD,IACCL,qBAAC,CAACiG,KAAK,CAACgC,MAAP,CAAD,CAAgBjD,OAAhB,CAAwBrD,aAAxB,EAAuCsD,MAFR,CAD/B,GAGiD,CAACzE,cAAc,CAACwH,IAAf,CAAoB/B,KAAK,CAACoB,KAA1B,CAHtD,EAGwF;EACtF,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAK5D,CAAAA,QAAL,IAAiBzD,qBAAC,CAAC,IAAD,CAAD,CAAQ0D,QAAR,CAAiBhD,mBAAjB,CAArB,EAA4D;EAC1D,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMwD,MAAM,GAAGtB,QAAQ,CAACuB,qBAAT,CAA+B,IAA/B,CAAf,CAAA;;MACA,IAAMR,QAAQ,GAAG3D,qBAAC,CAACkE,MAAD,CAAD,CAAUR,QAAV,CAAmB/C,eAAnB,CAAjB,CAAA;;MAEA,IAAI,CAACgD,QAAD,IAAasC,KAAK,CAACoB,KAAN,KAAgBnH,cAAjC,EAAiD;EAC/C,MAAA,OAAA;EACD,KAAA;;EAED+F,IAAAA,KAAK,CAACC,cAAN,EAAA,CAAA;EACAD,IAAAA,KAAK,CAACE,eAAN,EAAA,CAAA;;EAEA,IAAA,IAAI,CAACxC,QAAD,IAAcsC,KAAK,CAACoB,KAAN,KAAgBnH,cAAhB,IAAkC+F,KAAK,CAACoB,KAAN,KAAgBlH,aAApE,EAAoF;EAClF,MAAA,IAAI8F,KAAK,CAACoB,KAAN,KAAgBnH,cAApB,EAAoC;UAClCF,qBAAC,CAACkE,MAAM,CAACqC,aAAP,CAAqB9E,oBAArB,CAAD,CAAD,CAA8C2C,OAA9C,CAAsD,OAAtD,CAAA,CAAA;EACD,OAAA;;EAEDpE,MAAAA,qBAAC,CAAC,IAAD,CAAD,CAAQoE,OAAR,CAAgB,OAAhB,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMoE,KAAK,GAAG,EAAA,CAAGhB,KAAH,CAASC,IAAT,CAAcvD,MAAM,CAACwD,gBAAP,CAAwB7F,sBAAxB,CAAd,EACX4G,MADW,CACJ,UAAAC,IAAI,EAAA;QAAA,OAAI1I,qBAAC,CAAC0I,IAAD,CAAD,CAAQC,EAAR,CAAW,UAAX,CAAJ,CAAA;EAAA,KADA,CAAd,CAAA;;EAGA,IAAA,IAAIH,KAAK,CAACvD,MAAN,KAAiB,CAArB,EAAwB;EACtB,MAAA,OAAA;EACD,KAAA;;MAED,IAAI2D,KAAK,GAAGJ,KAAK,CAACK,OAAN,CAAc5C,KAAK,CAACgC,MAApB,CAAZ,CAAA;;MAEA,IAAIhC,KAAK,CAACoB,KAAN,KAAgBhH,gBAAhB,IAAoCuI,KAAK,GAAG,CAAhD,EAAmD;EAAE;QACnDA,KAAK,EAAA,CAAA;EACN,KAAA;;EAED,IAAA,IAAI3C,KAAK,CAACoB,KAAN,KAAgB/G,kBAAhB,IAAsCsI,KAAK,GAAGJ,KAAK,CAACvD,MAAN,GAAe,CAAjE,EAAoE;EAAE;QACpE2D,KAAK,EAAA,CAAA;EACN,KAAA;;MAED,IAAIA,KAAK,GAAG,CAAZ,EAAe;EACbA,MAAAA,KAAK,GAAG,CAAR,CAAA;EACD,KAAA;;EAEDJ,IAAAA,KAAK,CAACI,KAAD,CAAL,CAAatD,KAAb,EAAA,CAAA;;;;;WA7YF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAO3F,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOyC,OAAP,CAAA;EACD,KAAA;;;WAED,SAAyB,GAAA,GAAA;EACvB,MAAA,OAAOO,WAAP,CAAA;EACD,KAAA;;;;;EAuYH;EACA;EACA;;;AAEA3C,uBAAC,CAAC8E,QAAD,CAAD,CACGM,EADH,CACM7D,sBADN,EAC8BE,oBAD9B,EACoDmB,QAAQ,CAAC2F,sBAD7D,CAEGnD,CAAAA,EAFH,CAEM7D,sBAFN,EAE8BI,aAF9B,EAE6CiB,QAAQ,CAAC2F,sBAFtD,EAGGnD,EAHH,CAGS9D,oBAHT,GAGiCE,GAAAA,GAAAA,oBAHjC,EAGyDoB,QAAQ,CAACgB,WAHlE,CAIGwB,CAAAA,EAJH,CAIM9D,oBAJN,EAI4BG,oBAJ5B,EAIkD,UAAUwE,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN,EAAA,CAAA;EACAD,EAAAA,KAAK,CAACE,eAAN,EAAA,CAAA;;IACAvD,QAAQ,CAACuE,gBAAT,CAA0BM,IAA1B,CAA+BzH,qBAAC,CAAC,IAAD,CAAhC,EAAwC,QAAxC,CAAA,CAAA;EACD,CARH,CAAA,CASGoF,EATH,CASM9D,oBATN,EAS4BI,mBAT5B,EASiD,UAAAoH,CAAC,EAAI;EAClDA,EAAAA,CAAC,CAAC3C,eAAF,EAAA,CAAA;EACD,CAXH,CAAA,CAAA;EAaA;EACA;EACA;;AAEAnG,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAakD,GAAAA,QAAQ,CAACuE,gBAAtB,CAAA;AACAnH,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWqJ,CAAAA,WAAX,GAAyBnG,QAAzB,CAAA;;AACA5C,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWsJ,CAAAA,UAAX,GAAwB,YAAM;EAC5BhJ,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAO6C,QAAQ,CAACuE,gBAAhB,CAAA;EACD,CAHD;;;;;;;;"}
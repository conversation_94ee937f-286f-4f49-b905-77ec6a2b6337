{% extends 'base.html' %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2>控制面板</h2>
        <p class="text-muted">欢迎回来，{{ current_user.real_name or current_user.username }}</p>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">供应商</h6>
                        <h2 class="mb-0">{{ suppliers_count }}</h2>
                    </div>
                    <i class="fas fa-building fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>查看详情</span>
                <a href="{{ url_for('main.suppliers') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">食材</h6>
                        <h2 class="mb-0">{{ ingredients_count }}</h2>
                    </div>
                    <i class="fas fa-carrot fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>查看详情</span>
                <a href="{{ url_for('main.ingredients') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">食谱</h6>
                        <h2 class="mb-0">{{ recipes_count }}</h2>
                    </div>
                    <i class="fas fa-utensils fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>查看详情</span>
                <a href="{{ url_for('main.recipes') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">留样</h6>
                        <h2 class="mb-0">{{ samples_count }}</h2>
                    </div>
                    <i class="fas fa-vial fa-3x"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <span>查看详情</span>
                <a href="{{ url_for('main.food_samples') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">最近采购订单</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>供应商</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>{{ order.id }}</td>
                                <td>{{ order.supplier.name }}</td>
                                <td>¥{{ order.total_amount }}</td>
                                <td>
                                    {% if order.status == '待审核' %}
                                    <span class="badge badge-warning">{{ order.status }}</span>
                                    {% elif order.status == '已发货' %}
                                    <span class="badge badge-info">{{ order.status }}</span>
                                    {% elif order.status == '已完成' %}
                                    <span class="badge badge-success">{{ order.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{  order.order_date|format_datetime('%Y-%m-%d')  }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无采购订单</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-primary">查看所有订单</a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">快捷操作</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle mr-2"></i> 添加供应商
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle mr-2"></i> 添加食材
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle mr-2"></i> 添加食谱
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle mr-2"></i> 创建采购订单
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle mr-2"></i> 添加留样记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

/**
 * 用户引导系统 JavaScript
 * 管理分步骤的用户引导流程
 */

class UserGuide {
    constructor() {
        this.currentStep = 'welcome';
        this.totalSteps = 11;
        this.stepOrder = [
            'welcome', 'daily_management', 'suppliers', 'ingredients_recipes',
            'weekly_menu', 'purchase_order', 'stock_in', 'consumption_plan',
            'stock_out', 'traceability', 'food_samples', 'completed'
        ];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadGuideStatus();
    }

    bindEvents() {
        // 下一步按钮
        $('#nextStepBtn').on('click', () => {
            this.nextStep();
        });

        // 上一步按钮
        $('#prevStepBtn').on('click', () => {
            this.prevStep();
        });

        // 跳过引导按钮
        $('#skipGuideBtn').on('click', () => {
            this.skipGuide();
        });

        // 模态框关闭事件
        $('#guideStepModal').on('hidden.bs.modal', () => {
            this.pauseGuide();
        });
    }

    loadGuideStatus() {
        $.ajax({
            url: '/api/guide/status',
            type: 'GET',
            success: (data) => {
                if (data.is_active && data.current_step !== 'completed') {
                    this.currentStep = data.current_step;
                    this.showStep(this.currentStep);
                }
            },
            error: (xhr, status, error) => {
                console.error('加载引导状态失败:', error);
            }
        });
    }

    showStep(stepName) {
        this.currentStep = stepName;
        
        // 更新进度条
        const stepIndex = this.stepOrder.indexOf(stepName);
        const progress = (stepIndex / (this.totalSteps - 1)) * 100;
        $('#guideProgress').css('width', progress + '%');

        // 加载步骤内容
        this.loadStepContent(stepName);
        
        // 显示模态框
        $('#guideStepModal').modal('show');
        
        // 更新按钮状态
        this.updateButtons(stepIndex);
    }

    loadStepContent(stepName) {
        const templateId = stepName + 'StepTemplate';
        const template = $('#' + templateId).html();
        
        if (template) {
            $('#stepContent').html(template);
            
            // 更新标题
            const stepInfo = this.getStepInfo(stepName);
            $('#stepTitle').text(stepInfo.title);
        } else {
            // 如果没有模板，通过AJAX加载
            $.ajax({
                url: `/api/guide/step/${stepName}`,
                type: 'GET',
                success: (data) => {
                    $('#stepContent').html(data.content);
                    $('#stepTitle').text(data.title);
                },
                error: (xhr, status, error) => {
                    console.error('加载步骤内容失败:', error);
                    $('#stepContent').html('<p class="text-danger">加载内容失败，请刷新页面重试。</p>');
                }
            });
        }
    }

    updateButtons(stepIndex) {
        // 上一步按钮
        if (stepIndex > 0) {
            $('#prevStepBtn').show();
        } else {
            $('#prevStepBtn').hide();
        }

        // 下一步按钮文本
        if (stepIndex === this.totalSteps - 1) {
            $('#nextStepBtn').html('<i class="fas fa-check ml-1"></i>完成引导');
        } else {
            $('#nextStepBtn').html('<i class="fas fa-arrow-right ml-1"></i>下一步');
        }
    }

    nextStep() {
        const currentIndex = this.stepOrder.indexOf(this.currentStep);
        
        if (currentIndex < this.totalSteps - 1) {
            const nextStep = this.stepOrder[currentIndex + 1];
            
            // 标记当前步骤为完成
            this.completeStep(this.currentStep, () => {
                this.showStep(nextStep);
            });
        } else {
            // 完成引导
            this.completeGuide();
        }
    }

    prevStep() {
        const currentIndex = this.stepOrder.indexOf(this.currentStep);
        
        if (currentIndex > 0) {
            const prevStep = this.stepOrder[currentIndex - 1];
            this.showStep(prevStep);
        }
    }

    completeStep(stepName, callback) {
        $.ajax({
            url: '/api/guide/complete-step',
            type: 'POST',
            data: {
                step: stepName
            },
            success: (data) => {
                if (callback) callback();
            },
            error: (xhr, status, error) => {
                console.error('完成步骤失败:', error);
                if (callback) callback(); // 即使失败也继续
            }
        });
    }

    completeGuide() {
        $.ajax({
            url: '/api/guide/complete',
            type: 'POST',
            success: (data) => {
                $('#guideStepModal').modal('hide');
                this.showCompletionMessage();
            },
            error: (xhr, status, error) => {
                console.error('完成引导失败:', error);
                $('#guideStepModal').modal('hide');
            }
        });
    }

    skipGuide() {
        if (confirm('确定要跳过引导吗？您可以随时在帮助菜单中重新开始。')) {
            $.ajax({
                url: '/api/guide/skip',
                type: 'POST',
                success: (data) => {
                    $('#guideStepModal').modal('hide');
                },
                error: (xhr, status, error) => {
                    console.error('跳过引导失败:', error);
                    $('#guideStepModal').modal('hide');
                }
            });
        }
    }

    pauseGuide() {
        // 暂停引导，保存当前状态
        console.log('引导已暂停，当前步骤:', this.currentStep);
    }

    showCompletionMessage() {
        const message = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <h5><i class="fas fa-graduation-cap mr-2"></i>恭喜您完成了系统引导！</h5>
                <p>您已经掌握了食堂管理的完整流程，现在可以开始正式使用系统了。</p>
                <hr>
                <p class="mb-0">
                    <a href="${window.location.origin}/help" class="btn btn-sm btn-outline-success mr-2">
                        <i class="fas fa-question-circle mr-1"></i>查看帮助文档
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="userGuide.restartGuide()">
                        <i class="fas fa-redo mr-1"></i>重新开始引导
                    </button>
                </p>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        
        // 在页面顶部显示完成消息
        $('body').prepend(message);
        
        // 滚动到顶部
        $('html, body').animate({ scrollTop: 0 }, 500);
    }

    restartGuide() {
        $.ajax({
            url: '/api/guide/restart',
            type: 'POST',
            success: (data) => {
                this.currentStep = 'welcome';
                this.showStep('welcome');
            },
            error: (xhr, status, error) => {
                console.error('重启引导失败:', error);
            }
        });
    }

    getStepInfo(stepName) {
        const stepInfoMap = {
            'welcome': { title: '欢迎使用校园餐智慧食堂平台' },
            'daily_management': { title: '食堂日常管理模块' },
            'suppliers': { title: '供应商管理' },
            'ingredients_recipes': { title: '食材食谱管理' },
            'weekly_menu': { title: '周菜单计划' },
            'purchase_order': { title: '采购订单' },
            'stock_in': { title: '食材入库' },
            'consumption_plan': { title: '消耗量计划' },
            'stock_out': { title: '食材出库' },
            'traceability': { title: '食材溯源' },
            'food_samples': { title: '留样记录' },
            'completed': { title: '引导完成' }
        };
        
        return stepInfoMap[stepName] || { title: '系统引导' };
    }
}

// 全局函数，供模板调用
function generateQRCode() {
    // 生成检查二维码的逻辑
    alert('正在生成学校专属检查二维码...');
    // 这里可以调用实际的二维码生成API
}

function createDemoSupplier() {
    $.ajax({
        url: '/api/guide/create-demo/supplier',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示供应商创建成功！您可以在供应商管理页面查看。');
            } else {
                alert('创建失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('创建演示供应商失败:', error);
            alert('创建失败，请稍后重试。');
        }
    });
}

function createDemoIngredients() {
    $.ajax({
        url: '/api/guide/create-demo/ingredients',
        type: 'POST',
        success: (data) => {
            if (data.success) {
                alert('演示食材和食谱创建成功！您可以在相应页面查看。');
            } else {
                alert('创建失败：' + data.message);
            }
        },
        error: (xhr, status, error) => {
            console.error('创建演示食材失败:', error);
            alert('创建失败，请稍后重试。');
        }
    });
}

// 初始化用户引导
let userGuide;
$(document).ready(function() {
    userGuide = new UserGuide();
});

name: CSS (Dart Sass)

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 16

jobs:
  css:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "${{ env.NODE }}"

      - name: Build CSS with Dart Sass
        run: |
          npx --package sass@latest sass --version
          npx --package sass@latest sass --style expanded --source-map --embed-sources --no-error-css scss/:dist-sass/css/
          ls -Al dist-sass/css

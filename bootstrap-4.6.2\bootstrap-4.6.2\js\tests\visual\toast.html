<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="../../../dist/css/bootstrap.min.css">
    <title>Toast</title>
    <style>
      .notifications {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Toast <small>Bootstrap Visual Test</small></h1>

      <div class="row mt-3">
        <div class="col-md-12">
          <button id="btnShowToast" class="btn btn-primary">Show toast</button>
          <button id="btnHideToast" class="btn btn-primary">Hide toast</button>
        </div>
      </div>
    </div>

    <div class="notifications">
      <div id="toastAutoHide" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-delay="2000">
        <div class="toast-header">
          <span class="d-block bg-primary rounded mr-2" style="width: 20px; height: 20px;"></span>
          <strong class="mr-auto">Bootstrap</strong>
          <small>11 mins ago</small>
        </div>
        <div class="toast-body">
          Hello, world! This is a toast message with <strong>autohide</strong> in 2 seconds
        </div>
      </div>

      <div class="toast" data-autohide="false" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
          <span class="d-block bg-primary rounded mr-2" style="width: 20px; height: 20px;"></span>
          <strong class="mr-auto">Bootstrap</strong>
          <small class="text-muted">2 seconds ago</small>
          <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="toast-body">
          Heads up, toasts will stack automatically
        </div>
      </div>
    </div>

    <script src="../../../node_modules/jquery/dist/jquery.slim.min.js"></script>
    <script src="../../dist/util.js"></script>
    <script src="../../dist/toast.js"></script>
    <script>
      $(function () {
        $('.toast').toast()

        $('#btnShowToast').on('click', function () {
          $('.toast').toast('show')
        })

        $('#btnHideToast').on('click', function () {
          $('.toast').toast('hide')
        })
      })
    </script>
  </body>
</html>

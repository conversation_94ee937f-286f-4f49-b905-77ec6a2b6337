{% extends 'base.html' %}

{% block title %}食材详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('ingredient.edit', id=ingredient.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('ingredient.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">食材名称</th>
                                    <td>{{ ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>食材分类</th>
                                    <td>
                                        {% if ingredient.category_rel %}
                                            {{ ingredient.category_rel.name }}
                                        {% else %}
                                            {{ ingredient.category }}
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>计量单位</th>
                                    <td>{{ ingredient.unit }}</td>
                                </tr>
                                <tr>
                                    <th>规格</th>
                                    <td>{{ ingredient.specification or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>存储温度</th>
                                    <td>{{ ingredient.storage_temp or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>存储条件</th>
                                    <td>{{ ingredient.storage_condition or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>保质期（天）</th>
                                    <td>{{ ingredient.shelf_life or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if ingredient.status == 1 %}
                                        <span class="badge badge-success">启用</span>
                                        {% else %}
                                        <span class="badge badge-danger">停用</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ ingredient.created_at }}</td>
                                </tr>
                                <tr>
                                    <th>更新时间</th>
                                    <td>{{ ingredient.updated_at }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">食材图片</h5>
                                </div>
                                <div class="card-body text-center">
                                    {% if ingredient.base_image %}
                                    <img src="{{ url_for('static', filename=ingredient.base_image) }}" alt="{{ ingredient.name }}" class="img-fluid" style="max-height: 300px;">
                                    {% else %}
                                    <img src="{{ url_for('static', filename='img/no-image.png') }}" alt="No Image" class="img-fluid" style="max-height: 300px;">
                                    {% endif %}
                                </div>
                            </div>

                            {% if ingredient.nutrition_info %}
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title">营养信息</h5>
                                </div>
                                <div class="card-body">
                                    <p>{{  ingredient.nutrition_info|nl2br|safe  }}</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 供应商产品信息 -->
                    {% if ingredient.supplier_products.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">供应商产品信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>供应商</th>
                                                    <th>型号</th>
                                                    <th>规格</th>
                                                    <th>价格</th>
                                                    <th>质量标准</th>
                                                    <th>供货周期</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for product in ingredient.supplier_products %}
                                                <tr>
                                                    <td>{{ product.supplier.name }}</td>
                                                    <td>{{ product.model_number or '-' }}</td>
                                                    <td>{{ product.specification or '-' }}</td>
                                                    <td>¥{{ product.price }}</td>
                                                    <td>{{ product.quality_standard or '-' }}</td>
                                                    <td>{{ product.lead_time or '-' }} 天</td>
                                                    <td>
                                                        {% if product.is_available == 1 %}
                                                        <span class="badge badge-success">可供货</span>
                                                        {% else %}
                                                        <span class="badge badge-danger">不可供货</span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 食谱使用信息 -->
                    {% if ingredient.recipe_ingredients.count() > 0 %}
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">食谱使用信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>食谱名称</th>
                                                    <th>食谱分类</th>
                                                    <th>用量</th>
                                                    <th>单位</th>
                                                    <th>处理方法</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for recipe_ingredient in ingredient.recipe_ingredients %}
                                                <tr>
                                                    <td>{{ recipe_ingredient.recipe.name }}</td>
                                                    <td>{{ recipe_ingredient.recipe.category }}</td>
                                                    <td>{{ recipe_ingredient.quantity }}</td>
                                                    <td>{{ recipe_ingredient.unit }}</td>
                                                    <td>{{ recipe_ingredient.processing_method or '-' }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

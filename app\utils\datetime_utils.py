"""
日期时间工具函数

用于处理日期时间相关的工具函数，特别是处理SQL Server的DATETIME2精度问题。
"""
from datetime import datetime

def safe_datetime(dt=None):
    """
    创建一个安全的datetime对象，确保微秒部分为0。
    
    Args:
        dt (datetime, optional): 要处理的datetime对象。如果为None，则使用当前时间。
        
    Returns:
        datetime: 处理后的datetime对象，微秒部分为0。
    """
    if dt is None:
        dt = datetime.now()
    return dt.replace(microsecond=0)

def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """
    格式化datetime对象为字符串。
    
    Args:
        dt (datetime): 要格式化的datetime对象。
        format_str (str, optional): 格式化字符串。默认为'%Y-%m-%d %H:%M:%S'。
        
    Returns:
        str: 格式化后的字符串。
    """
    if dt is None:
        return ''
    return dt.strftime(format_str)

def parse_datetime(date_str, format_str='%Y-%m-%d %H:%M:%S'):
    """
    解析字符串为datetime对象。
    
    Args:
        date_str (str): 要解析的字符串。
        format_str (str, optional): 格式化字符串。默认为'%Y-%m-%d %H:%M:%S'。
        
    Returns:
        datetime: 解析后的datetime对象，微秒部分为0。
    """
    if not date_str:
        return None
    try:
        dt = datetime.strptime(date_str, format_str)
        return dt.replace(microsecond=0)
    except ValueError:
        return None

{% extends 'base.html' %}

{% block title %}库存预警管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">库存预警列表</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" id="batchProcessBtn" disabled>
                            <i class="fas fa-check"></i> 批量处理
                        </button>
                        <button type="button" class="btn btn-success btn-sm" id="batchCreateRequisitionBtn" disabled>
                            <i class="fas fa-shopping-cart"></i> 批量创建采购申请
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" action="{{ url_for('inventory_alert.index') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="未处理" {% if status == '未处理' %}selected{% endif %}>未处理</option>
                                        <option value="已处理" {% if status == '已处理' %}selected{% endif %}>已处理</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>预警类型</label>
                                    <select name="alert_type" class="form-control">
                                        <option value="">全部</option>
                                        <option value="库存不足" {% if alert_type == '库存不足' %}selected{% endif %}>库存不足</option>
                                        <option value="库存过多" {% if alert_type == '库存过多' %}selected{% endif %}>库存过多</option>
                                        <option value="临近过期" {% if alert_type == '临近过期' %}selected{% endif %}>临近过期</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary form-control">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 批量处理表单 -->
                    <form id="batchProcessForm" action="{{ url_for('inventory_alert.batch_process') }}" method="post" style="display: none;"><div id="batchProcessAlertIds"></div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>

                    <!-- 批量创建采购申请表单 -->
                    <form id="batchCreateRequisitionForm" action="{{ url_for('inventory_alert.batch_create_requisition') }}" method="get" style="display: none;">
                        <div id="batchCreateRequisitionAlertIds"></div>
                    </form>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>ID</th>
                                    <th>区域</th>
                                    <th>食材</th>
                                    <th>当前库存</th>
                                    <th>预警类型</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for inventory_alert in inventory_alerts %}
                                <tr>
                                    <td>
                                        {% if inventory_alert.status == '未处理' %}
                                        <input type="checkbox" class="alert-checkbox" value="{{ inventory_alert.id }}">
                                        {% endif %}
                                    </td>
                                    <td>{{ inventory_alert.id }}</td>
                                    <td>{{ inventory_alert.area.name }}</td>
                                    <td>{{ inventory_alert.ingredient.name }}</td>
                                    <td>{{ inventory_alert.current_quantity }} {{ inventory_alert.unit }}</td>
                                    <td>
                                        {% if inventory_alert.alert_type == '库存不足' %}
                                        <span class="badge badge-danger">库存不足</span>
                                        {% elif inventory_alert.alert_type == '库存过多' %}
                                        <span class="badge badge-warning">库存过多</span>
                                        {% elif inventory_alert.alert_type == '临近过期' %}
                                        <span class="badge badge-info">临近过期</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if inventory_alert.status == '未处理' %}
                                        <span class="badge badge-warning">未处理</span>
                                        {% elif inventory_alert.status == '已处理' %}
                                        <span class="badge badge-success">已处理</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ inventory_alert.created_at }}</td>
                                    <td>
                                        <a href="{{ url_for('inventory_alert.view', id=inventory_alert.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        {% if inventory_alert.status == '未处理' %}
                                        <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#processModal{{ inventory_alert.id }}">
                                            <i class="fas fa-check"></i> 处理
                                        </button>
                                        <a href="{{ url_for('inventory_alert.create_requisition', id=inventory_alert.id) }}" class="btn btn-success btn-sm">
                                            <i class="fas fa-shopping-cart"></i> 创建采购申请
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>

                                <!-- 处理确认模态框 -->
                                <div class="modal fade" id="processModal{{ inventory_alert.id }}" tabindex="-1" role="dialog" aria-labelledby="processModalLabel{{ inventory_alert.id }}" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="processModalLabel{{ inventory_alert.id }}">处理库存预警</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form action="{{ url_for('inventory_alert.process', id=inventory_alert.id) }}" method="post"><div class="modal-body">
                                                    <div class="form-group">
                                                        <label>处理备注</label>
                                                        <textarea name="notes" class="form-control" rows="3" placeholder="请输入处理备注"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                    <button type="submit" class="btn btn-primary">确认处理</button>
                                                </div>
                                            
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory_alert.index', page=pagination.prev_num, area_id=area_id, status=status, alert_type=alert_type) }}">
                                    上一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">上一页</span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('inventory_alert.index', page=page, area_id=area_id, status=status, alert_type=alert_type) }}">
                                            {{ page }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('inventory_alert.index', page=pagination.next_num, area_id=area_id, status=status, alert_type=alert_type) }}">
                                    下一页
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">下一页</span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // 全选/取消全选
        $('#selectAll').change(function() {
            $('.alert-checkbox').prop('checked', $(this).prop('checked'));
            updateBatchButtons();
        });

        // 单个复选框变化时更新按钮状态
        $('.alert-checkbox').change(function() {
            updateBatchButtons();
        });

        // 更新批量操作按钮状态
        function updateBatchButtons() {
            var checkedCount = $('.alert-checkbox:checked').length;
            $('#batchProcessBtn, #batchCreateRequisitionBtn').prop('disabled', checkedCount === 0);
        }

        // 批量处理按钮点击事件
        $('#batchProcessBtn').click(function() {
            if (confirm('确定要批量处理选中的预警吗？')) {
                // 清空之前的内容
                $('#batchProcessAlertIds').empty();

                // 添加选中的预警ID
                $('.alert-checkbox:checked').each(function() {
                    $('#batchProcessAlertIds').append('<input type="hidden" name="alert_ids[]" value="' + $(this).val() + '">');
                });

                // 提交表单
                $('#batchProcessForm').submit();
            }
        });

        // 批量创建采购申请按钮点击事件
        $('#batchCreateRequisitionBtn').click(function() {
            // 清空之前的内容
            $('#batchCreateRequisitionAlertIds').empty();

            // 添加选中的预警ID
            $('.alert-checkbox:checked').each(function() {
                $('#batchCreateRequisitionAlertIds').append('<input type="hidden" name="alert_ids[]" value="' + $(this).val() + '">');
            });

            // 提交表单
            $('#batchCreateRequisitionForm').submit();
        });
    });
</script>
{% endblock %}
{% endblock %}

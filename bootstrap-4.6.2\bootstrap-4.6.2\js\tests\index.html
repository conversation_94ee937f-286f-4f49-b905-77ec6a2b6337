<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Bootstrap Plugin Test Suite</title>

    <!-- jQuery -->
    <script src="../../node_modules/jquery/dist/jquery.slim.min.js"></script>
    <script src="../../node_modules/popper.js/dist/umd/popper.min.js"></script>

    <!-- QUnit -->
    <link rel="stylesheet" href="../../node_modules/qunit/qunit/qunit.css" media="screen">
    <script src="../../node_modules/qunit/qunit/qunit.js"></script>

    <!-- Sinon -->
    <script src="../../node_modules/sinon/pkg/sinon-no-sourcemaps.js"></script>

    <!-- Hammer simulator -->
    <script src="../../node_modules/hammer-simulator/index.js"></script>

    <script>
      // Disable jQuery event aliases to ensure we don't accidentally use any of them
      [
        'blur',
        'focus',
        'focusin',
        'focusout',
        'resize',
        'scroll',
        'click',
        'dblclick',
        'mousedown',
        'mouseup',
        'mousemove',
        'mouseover',
        'mouseout',
        'mouseenter',
        'mouseleave',
        'change',
        'select',
        'submit',
        'keydown',
        'keypress',
        'keyup',
        'contextmenu'
      ].forEach(function(eventAlias) {
        $.fn[eventAlias] = function() {
          throw new Error('Using the ".' + eventAlias + '()" method is not allowed, so that Bootstrap can be compatible with custom jQuery builds which exclude the "event aliases" module that defines said method. See https://github.com/twbs/bootstrap/blob/v4-dev/.github/CONTRIBUTING.md#js')
        }
      })

      // Require assert.expect in each test
      QUnit.config.requireExpects = true

      // See https://github.com/axemclion/grunt-saucelabs#test-result-details-with-qunit
      var log = []
      var testName

      QUnit.done(function(testResults) {
        var tests = []
        for (var i = 0; i < log.length; i++) {
          var details = log[i]
          tests.push({
            name: details.name,
            result: details.result,
            expected: details.expected,
            actual: details.actual,
            source: details.source
          })
        }
        testResults.tests = tests

        window.global_test_results = testResults
      })

      QUnit.testStart(function(testDetails) {
        QUnit.log(function(details) {
          if (!details.result) {
            details.name = testDetails.name
            log.push(details)
          }
        })
      })

      // Display fixture on-screen on iOS to avoid false positives
      // See https://github.com/twbs/bootstrap/pull/15955
      if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
        QUnit.begin(function() {
          $('#qunit-fixture').css({ top: 0, left: 0 })
        })

        QUnit.done(function() {
          $('#qunit-fixture').css({ top: '', left: '' })
        })
      }
    </script>

    <!-- Transpiled Plugins -->
    <script src="../dist/util.js"></script>
    <script src="../dist/alert.js"></script>
    <script src="../dist/button.js"></script>
    <script src="../dist/carousel.js"></script>
    <script src="../dist/collapse.js"></script>
    <script src="../dist/dropdown.js"></script>
    <script src="../dist/modal.js"></script>
    <script src="../dist/scrollspy.js"></script>
    <script src="../dist/tab.js"></script>
    <script src="../dist/tooltip.js"></script>
    <script src="../dist/popover.js"></script>
    <script src="../dist/toast.js"></script>

    <!-- Unit Tests -->
    <script src="unit/alert.js"></script>
    <script src="unit/button.js"></script>
    <script src="unit/carousel.js"></script>
    <script src="unit/collapse.js"></script>
    <script src="unit/dropdown.js"></script>
    <script src="unit/modal.js"></script>
    <script src="unit/scrollspy.js"></script>
    <script src="unit/tab.js"></script>
    <script src="unit/tooltip.js"></script>
    <script src="unit/popover.js"></script>
    <script src="unit/util.js"></script>
    <script src="unit/toast.js"></script>
  </head>
  <body>
    <div id="qunit-container">
      <div id="qunit"></div>
      <div id="qunit-fixture"></div>
    </div>
  </body>
</html>

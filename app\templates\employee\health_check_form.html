{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2>{{ title }}</h2>
        <p class="text-muted">为员工 {{ employee.name }} 添加健康检查记录</p>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" novalidate>
            {{ form.hidden_tag() }}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.check_date.label }}
                        {{ form.check_date(class="form-control", type="date") }}
                        {% for error in form.check_date.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.temperature.label }}
                        {{ form.temperature(class="form-control", step="0.1") }}
                        {% for error in form.temperature.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="form-text text-muted">正常体温范围: 36.3°C - 37.2°C</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                {{ form.health_status.label }}
                {{ form.health_status(class="form-control") }}
                {% for error in form.health_status.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>
            
            <div class="form-group">
                {{ form.symptoms.label }}
                {{ form.symptoms(class="form-control", rows=3) }}
                {% for error in form.symptoms.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
                <small class="form-text text-muted">如有异常症状，请在此处详细描述</small>
            </div>
            
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=3) }}
                {% for error in form.notes.errors %}
                <small class="text-danger">{{ error }}</small>
                {% endfor %}
            </div>
            
            <div class="form-group text-center">
                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

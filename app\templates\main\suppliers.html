{% extends 'base.html' %}

{% block title %}供应商管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>供应商管理</h2>
    </div>
    <div class="col-md-4 text-right">
        <a href="#" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加供应商
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>供应商名称</th>
                        <th>联系人</th>
                        <th>联系电话</th>
                        <th>状态</th>
                        <th>评级</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in suppliers.items %}
                    <tr>
                        <td>{{ supplier.id }}</td>
                        <td>{{ supplier.name }}</td>
                        <td>{{ supplier.contact_person }}</td>
                        <td>{{ supplier.phone }}</td>
                        <td>
                            {% if supplier.status == 1 %}
                            <span class="badge badge-success">合作中</span>
                            {% else %}
                            <span class="badge badge-secondary">已停用</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.rating %}
                            <div class="text-warning">
                                {% for i in range(supplier.rating|int) %}
                                <i class="fas fa-star"></i>
                                {% endfor %}
                                {% if supplier.rating % 1 > 0 %}
                                <i class="fas fa-star-half-alt"></i>
                                {% endif %}
                            </div>
                            {% else %}
                            <span class="text-muted">暂无评级</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="#" class="btn btn-sm btn-info">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无供应商数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% if suppliers.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not suppliers.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.suppliers', page=suppliers.prev_num) if suppliers.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in suppliers.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == suppliers.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('main.suppliers', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not suppliers.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('main.suppliers', page=suppliers.next_num) if suppliers.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户引导API路由
提供引导系统的后端接口
"""

from flask import Blueprint, request, jsonify, render_template_string
from flask_login import login_required, current_user
from app.services.user_guide_service import UserGuideService

guide_api_bp = Blueprint('guide_api', __name__)

@guide_api_bp.route('/api/guide/status', methods=['GET'])
@login_required
def get_guide_status():
    """获取用户引导状态"""
    try:
        status = UserGuideService.get_user_guide_status(current_user.id)
        return jsonify({
            'success': True,
            **status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/start', methods=['POST'])
@login_required
def start_guide():
    """开始用户引导"""
    try:
        UserGuideService.start_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已开始'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/complete-step', methods=['POST'])
@login_required
def complete_step():
    """完成一个引导步骤"""
    try:
        step_name = request.form.get('step')
        if not step_name:
            return jsonify({
                'success': False,
                'message': '缺少步骤名称'
            }), 400
        
        next_step = UserGuideService.complete_step(current_user.id, step_name)
        return jsonify({
            'success': True,
            'next_step': next_step,
            'message': '步骤完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/skip', methods=['POST'])
@login_required
def skip_guide():
    """跳过引导"""
    try:
        UserGuideService.skip_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已跳过'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/complete', methods=['POST'])
@login_required
def complete_guide():
    """完成整个引导"""
    try:
        UserGuideService.complete_step(current_user.id, 'completed')
        return jsonify({
            'success': True,
            'message': '引导已完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/restart', methods=['POST'])
@login_required
def restart_guide():
    """重新开始引导"""
    try:
        UserGuideService.start_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已重新开始'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/step/<step_name>', methods=['GET'])
@login_required
def get_step_content(step_name):
    """获取步骤内容"""
    try:
        step_info = UserGuideService.get_step_info(step_name)
        step_content = UserGuideService.get_step_content(step_name)
        
        # 根据步骤生成HTML内容
        content_html = generate_step_html(step_name, step_content)
        
        return jsonify({
            'success': True,
            'title': step_info.get('title', '系统引导'),
            'content': content_html
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/create-demo/<demo_type>', methods=['POST'])
@login_required
def create_demo_data(demo_type):
    """创建演示数据"""
    try:
        if not current_user.area:
            return jsonify({
                'success': False,
                'message': '用户没有关联的区域'
            }), 400
        
        result = UserGuideService.generate_demo_data(demo_type, current_user.area.id)
        
        if result and 'error' not in result:
            return jsonify({
                'success': True,
                'message': f'演示{demo_type}创建成功',
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'message': result.get('error', '创建失败') if result else '创建失败'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

def generate_step_html(step_name, step_content):
    """生成步骤HTML内容"""
    
    if step_name == 'ingredients_recipes':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-carrot fa-3x text-warning mb-3"></i>
            <h4>食材食谱管理</h4>
            <p class="text-muted">建立食材数据库，创建营养食谱</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0"><i class="fas fa-apple-alt mr-2"></i>食材管理</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>建立食材基础档案</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置营养成分信息</li>
                            <li><i class="fas fa-check text-success mr-2"></i>管理储存条件要求</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置保质期提醒</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-utensils mr-2"></i>食谱管理</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>创建营养均衡食谱</li>
                            <li><i class="fas fa-check text-success mr-2"></i>计算营养成分含量</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置制作工艺流程</li>
                            <li><i class="fas fa-check text-success mr-2"></i>管理食谱成本核算</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-3">
            <h6><i class="fas fa-lightbulb mr-2"></i>操作建议</h6>
            <p class="mb-2">1. 先添加基础食材（如大米、面粉、蔬菜等）</p>
            <p class="mb-2">2. 创建简单食谱（如白米饭、青菜汤等）</p>
            <p class="mb-0">3. 体验食材的添加、编辑、删除功能</p>
        </div>
        
        <div class="text-center mt-4">
            <button class="btn btn-warning mr-2" onclick="createDemoIngredients()">
                <i class="fas fa-plus mr-1"></i>创建演示食材
            </button>
            <a href="{{ url_for('ingredient.index') }}" class="btn btn-outline-warning" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>食材管理
            </a>
            <a href="{{ url_for('recipe.index') }}" class="btn btn-outline-info ml-2" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>食谱管理
            </a>
        </div>
        ''')
    
    elif step_name == 'weekly_menu':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-calendar-week fa-3x text-primary mb-3"></i>
            <h4>周菜单计划</h4>
            <p class="text-muted">制定一周的菜单安排，合理搭配营养</p>
        </div>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-calendar mr-2"></i>菜单制定流程</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <span class="timeline-marker bg-primary">1</span>
                                <div class="timeline-content">
                                    <h6>创建周菜单计划</h6>
                                    <p class="text-muted">选择周期，设置基本信息</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-info">2</span>
                                <div class="timeline-content">
                                    <h6>进入编辑状态</h6>
                                    <p class="text-muted">点击编辑按钮，开始安排菜单</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-success">3</span>
                                <div class="timeline-content">
                                    <h6>安排每日菜品</h6>
                                    <p class="text-muted">为每天的早中晚餐选择食谱</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-warning">4</span>
                                <div class="timeline-content">
                                    <h6>发布菜单</h6>
                                    <p class="text-muted">确认无误后发布菜单计划</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>重要提醒</h6>
                    <p class="mb-2">创建周菜单后，需要：</p>
                    <ol class="mb-0">
                        <li>返回列表页面</li>
                        <li>点击"编辑"按钮</li>
                        <li>进入编辑状态</li>
                        <li>安排具体菜品</li>
                    </ol>
                </div>
                
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie fa-2x text-success mb-2"></i>
                        <h6>营养搭配</h6>
                        <p class="small text-muted">系统会自动计算营养成分，帮助您合理搭配</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-primary" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>开始制定周菜单
            </a>
        </div>
        ''')
    
    # 其他步骤的HTML内容...
    return f'<p>步骤 {step_name} 的内容正在开发中...</p>'

{% extends 'base.html' %}

{% block title %}食材溯源查询{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材溯源查询</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <div class="card">
                                <div class="card-header bg-primary">
                                    <h4 class="card-title text-white">溯源查询</h4>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{{ url_for('traceability.search') }}">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                                        <div class="form-group">
                                            <label for="search_type">查询类型</label>
                                            <select name="search_type" id="search_type" class="form-control">
                                                <option value="batch_number">批次号</option>
                                                <option value="ingredient_id">食材ID</option>
                                                <option value="menu_id">菜单计划ID</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="search_value">查询内容</label>
                                            <input type="text" name="search_value" id="search_value" class="form-control" required>
                                            <small class="form-text text-muted search-hint">请输入批次号进行查询</small>
                                        </div>

                                        <div class="form-group text-center">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-search"></i> 查询
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-success">
                                    <h4 class="card-title text-white">按食材查询</h4>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>选择食材</label>
                                        <select id="ingredient_select" class="form-control">
                                            <option value="">请选择食材</option>
                                            {% for ingredient in ingredients %}
                                            <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="form-group text-center">
                                        <button type="button" id="ingredient_search_btn" class="btn btn-success">
                                            <i class="fas fa-search"></i> 查询
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-info">
                                    <h4 class="card-title text-white">按供应商查询</h4>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>选择供应商</label>
                                        <select id="supplier_select" class="form-control">
                                            <option value="">请选择供应商</option>
                                            {% for supplier in suppliers %}
                                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="form-group text-center">
                                        <button type="button" id="supplier_search_btn" class="btn btn-info">
                                            <i class="fas fa-search"></i> 查询
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-warning">
                                    <h4 class="card-title text-white">按区域查询</h4>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>选择区域</label>
                                        <select id="area_select" class="form-control">
                                            <option value="">请选择区域</option>
                                            {% for area in areas %}
                                            <option value="{{ area.id }}">{{ area.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="form-group text-center">
                                        <button type="button" id="area_search_btn" class="btn btn-warning">
                                            <i class="fas fa-search"></i> 查询
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(function() {
        // 更新查询提示
        $('#search_type').change(function() {
            var searchType = $(this).val();
            var hint = '';

            if (searchType === 'batch_number') {
                hint = '请输入批次号进行查询';
            } else if (searchType === 'ingredient_id') {
                hint = '请输入食材ID进行查询';
            } else if (searchType === 'menu_id') {
                hint = '请输入菜单计划ID进行查询';
            }

            $('.search-hint').text(hint);
        });

        // 按食材查询
        $('#ingredient_search_btn').click(function() {
            var ingredientId = $('#ingredient_select').val();
            if (ingredientId) {
                window.location.href = "{{ url_for('traceability.trace_ingredient', id=0) }}".replace('0', ingredientId);
            } else {
                alert('请选择食材');
            }
        });

        // 按供应商查询
        $('#supplier_search_btn').click(function() {
            var supplierId = $('#supplier_select').val();
            if (supplierId) {
                // 暂时重定向到批次列表，并按供应商筛选
                window.location.href = "{{ url_for('material_batch.index') }}?supplier_id=" + supplierId;
            } else {
                alert('请选择供应商');
            }
        });

        // 按区域查询
        $('#area_search_btn').click(function() {
            var areaId = $('#area_select').val();
            if (areaId) {
                // 暂时重定向到批次列表，并按区域筛选
                window.location.href = "{{ url_for('material_batch.index') }}?area_id=" + areaId;
            } else {
                alert('请选择区域');
            }
        });
    });
</script>
{% endblock %}

---
layout: docs
title: Icons
description: Guidance and suggestions for using external icon libraries with Bootstrap.
group: extend
---

## Bootstrap Icons

While you won't find a built-in icon library in Bootstrap, our separate [Bootstrap Icons]({{< param icons >}}) projects is a growing set of open source SVGs you can use. While they're designed first and foremost to work with our components and documentation, you can use them in any project.

<a href="{{< param icons >}}" class="btn btn-bd-primary">Get Bootstrap Icons</a>

<img class="img-fluid mt-3 mx-auto" srcset="/docs/{{< param docs_version >}}/assets/img/bootstrap-icons.png,
                                            /docs/{{< param docs_version >}}/assets/img/<EMAIL> 2x"
                                            src="/docs/{{< param docs_version >}}/assets/img/bootstrap-icons.png"
                                            alt="Bootstrap Icons" width="966" height="600" loading="lazy">


## Additional icon sets

In addition to Bootstrap Icons, we have a handful of alternative icon libraries for you to choose from. While most icon sets include multiple file formats, we prefer SVG implementations for their improved accessibility and vector support.

### Preferred

We've tested and used these icon sets ourselves.

- [Font Awesome](https://fontawesome.com/)
- [Feather](https://feathericons.com/)
- [Octicons](https://octicons.github.com/)

### More

While we haven't tried these out, they do look promising and provide multiple formats—including SVG.

- [Bytesize](https://github.com/danklammer/bytesize-icons)
- [Google Material icons](https://material.io/resources/icons/)
- [Ionicons](https://ionicons.com/)
- [Dripicons](http://demo.amitjakhu.com/dripicons/)
- [Ikons](http://ikons.piotrkwiatkowski.co.uk/)
- [Icons8](https://icons8.com/)
- [icofont](https://icofont.com/)
- [CoreUI Icons](https://icons.coreui.io/)

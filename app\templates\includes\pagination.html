{% macro render_pagination(pagination, endpoint, **kwargs) %}
<nav aria-label="分页导航">
  <ul class="pagination justify-content-center">
    {% if pagination.has_prev %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for(endpoint, page=pagination.prev_num, **kwargs) }}" aria-label="上一页">
          <span aria-hidden="true">&laquo;</span>
          <span class="sr-only">上一页</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" aria-label="上一页">
          <span aria-hidden="true">&laquo;</span>
          <span class="sr-only">上一页</span>
        </a>
      </li>
    {% endif %}

    {%- for page in pagination.iter_pages() %}
      {% if page %}
        {% if page != pagination.page %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for(endpoint, page=page, **kwargs) }}">{{ page }}</a>
          </li>
        {% else %}
          <li class="page-item active" aria-current="page">
            <span class="page-link">
              {{ page }}
              <span class="sr-only">(当前页)</span>
            </span>
          </li>
        {% endif %}
      {% else %}
        <li class="page-item disabled">
          <a class="page-link" href="#">&hellip;</a>
        </li>
      {% endif %}
    {%- endfor %}

    {% if pagination.has_next %}
      <li class="page-item">
        <a class="page-link" href="{{ url_for(endpoint, page=pagination.next_num, **kwargs) }}" aria-label="下一页">
          <span aria-hidden="true">&raquo;</span>
          <span class="sr-only">下一页</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <a class="page-link" href="#" aria-label="下一页">
          <span aria-hidden="true">&raquo;</span>
          <span class="sr-only">下一页</span>
        </a>
      </li>
    {% endif %}
  </ul>
</nav>
<div class="text-center text-muted">
  显示第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页，总计 {{ pagination.total }} 条记录
</div>
{% endmacro %}

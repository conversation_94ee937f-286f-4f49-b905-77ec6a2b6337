/*!
 * Bootstrap Docs (https://getbootstrap.com/)
 * Copyright 2011-2022 The Bootstrap Authors
 * Copyright 2011-2022 Twitter, Inc.
 * Licensed under the Creative Commons Attribution 3.0 Unported License.
 * For details, see https://creativecommons.org/licenses/by/3.0/.
 */

// Dev notes
//
// Background information on nomenclature and architecture decisions here.
//
// - Bootstrap functions, variables, and mixins are included for easy reuse.
//   Doing so gives us access to the same core utilities provided by Bootstrap.
//   For example, consistent media queries through those mixins.
//
// - Bootstrap's **docs variables** are prefixed with `$bd-`.
//   These custom colors avoid collision with the components Bootstrap provides.
//
// - Classes are prefixed with `.bd-`.
//   These classes indicate custom-built or modified components for the design
//   and layout of the Bootstrap docs. They are not included in our builds.
//
// Happy Bootstrapping!

// Load Bootstrap variables and mixins
@import "../../../scss/functions";
@import "../../../scss/variables";
@import "../../../scss/mixins";

// Load docs components
@import "variables";
@import "nav";
@import "masthead";
@import "ads";
@import "content";
@import "skippy";
@import "sidebar";
@import "toc";
@import "footer";
@import "component-examples";
@import "buttons";
@import "callouts";
@import "browser-bugs";
@import "brand";
@import "colors";
@import "clipboard-js";
@import "placeholder-img";

// Load docs dependencies
@import "syntax";
@import "anchor";
@import "algolia";

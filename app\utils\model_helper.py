"""
模型辅助工具模块
"""

from app.utils.datetime_helper import format_datetime, format_date

def safe_to_dict(model, exclude_fields=None):
    """
    安全地将模型转换为字典，处理各种可能的错误
    
    参数:
        model: 要转换的模型实例
        exclude_fields: 要排除的字段列表
        
    返回:
        包含模型数据的字典
    """
    if exclude_fields is None:
        exclude_fields = []
        
    result = {}
    
    # 获取模型的所有列
    columns = [c.name for c in model.__table__.columns if c.name not in exclude_fields]
    
    for column in columns:
        try:
            value = getattr(model, column)
            
            # 处理日期时间字段
            if column.endswith('_at') or column.endswith('_date') or column.endswith('_time'):
                if column.endswith('_date'):
                    result[column] = format_date(value)
                else:
                    result[column] = format_datetime(value)
            else:
                result[column] = value
        except Exception as e:
            # 处理错误，设置为None
            result[column] = None
            
    return result

def safe_strftime(dt_value, format_str='%Y-%m-%d %H:%M:%S'):
    """
    安全地调用strftime方法，处理各种可能的错误
    
    参数:
        dt_value: 要格式化的日期时间值
        format_str: 格式化字符串
        
    返回:
        格式化后的字符串，如果出错则返回None
    """
    return format_datetime(dt_value, format_str)

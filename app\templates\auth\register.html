{% extends 'base.html' %}

{% block title %}注册 - {{ super() }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">创建学校并注册</h4>
                <p class="mb-0 mt-2"><small>注册后您将成为学校管理员，拥有完整的系统管理权限</small></p>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}

                    <!-- 学校信息 -->
                    <div class="form-group">
                        <h5 class="text-primary">学校信息</h5>
                        <hr>
                    </div>
                    <div class="form-group">
                        {{ form.school_name.label }}
                        {{ form.school_name(class="form-control", placeholder="请输入您的学校名称") }}
                        {% for error in form.school_name.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="text-muted">这将是您学校在系统中的显示名称</small>
                    </div>

                    <!-- 管理员信息 -->
                    <div class="form-group mt-4">
                        <h5 class="text-primary">管理员信息</h5>
                        <hr>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.username.label }}
                                {{ form.username(class="form-control", placeholder="用于登录的用户名") }}
                                {% for error in form.username.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.email.label }}
                                {{ form.email(class="form-control", placeholder="您的邮箱地址") }}
                                {% for error in form.email.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.real_name.label }}
                                {{ form.real_name(class="form-control", placeholder="您的真实姓名") }}
                                {% for error in form.real_name.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.phone.label }}
                                {{ form.phone(class="form-control", placeholder="您的手机号码") }}
                                {% for error in form.phone.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.password.label }}
                                {{ form.password(class="form-control", placeholder="设置登录密码") }}
                                {% for error in form.password.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.password2.label }}
                                {{ form.password2(class="form-control", placeholder="再次输入密码") }}
                                {% for error in form.password2.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- 注册说明 -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 注册说明</h6>
                        <ul class="mb-0">
                            <li>注册成功后，系统将为您创建独立的学校管理环境</li>
                            <li>您将获得学校管理员权限，可以管理食堂、菜单、采购等所有功能</li>
                            <li>系统完全免费使用，无需审核，注册即可开始使用</li>
                            <li>您的学校数据完全独立，不会与其他学校混淆</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary btn-block btn-lg") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">已有账号？<a href="{{ url_for('auth.login') }}">立即登录</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

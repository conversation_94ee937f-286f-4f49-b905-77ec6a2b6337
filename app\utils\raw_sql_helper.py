"""
使用原始SQL插入记录的辅助函数

此模块提供了使用原始SQL插入记录的辅助函数，避免SQLAlchemy的日期时间处理问题。
"""

from app import db
from sqlalchemy import text
from datetime import datetime
import json

def insert_area_change_history(area_id, change_type, old_parent_id=None, new_parent_id=None, 
                              old_data=None, new_data=None, changed_by=None):
    """
    使用原始SQL插入area_change_history记录
    
    参数:
        area_id: 区域ID
        change_type: 变更类型，例如'create'、'update'、'delete'
        old_parent_id: 旧的父级ID
        new_parent_id: 新的父级ID
        old_data: 旧的数据，JSON字符串或字典
        new_data: 新的数据，JSON字符串或字典
        changed_by: 变更人ID
    
    返回:
        新插入记录的ID
    """
    # 处理JSON数据
    if old_data and not isinstance(old_data, str):
        old_data = json.dumps(old_data)
    if new_data and not isinstance(new_data, str):
        new_data = json.dumps(new_data)
    
    # 构建SQL语句
    sql = text('''
    INSERT INTO area_change_history
    (area_id, change_type, old_parent_id, new_parent_id, old_data, new_data, changed_by, created_at)
    OUTPUT inserted.id
    VALUES
    (:area_id, :change_type, :old_parent_id, :new_parent_id, :old_data, :new_data, :changed_by, GETDATE())
    ''')
    
    # 执行SQL
    result = db.session.execute(
        sql,
        {
            'area_id': area_id,
            'change_type': change_type,
            'old_parent_id': old_parent_id,
            'new_parent_id': new_parent_id,
            'old_data': old_data,
            'new_data': new_data,
            'changed_by': changed_by
        }
    )
    
    # 获取新插入记录的ID
    new_id = result.scalar()
    
    return new_id

def insert_audit_log(action, resource_type, resource_id=None, user_id=None, 
                    area_id=None, details=None, ip_address=None):
    """
    使用原始SQL插入audit_log记录
    
    参数:
        action: 操作类型，例如'create'、'update'、'delete'、'login'
        resource_type: 资源类型，例如'User'、'Area'、'Recipe'
        resource_id: 资源ID
        user_id: 用户ID
        area_id: 区域ID
        details: 详细信息，JSON字符串或字典
        ip_address: IP地址
    
    返回:
        新插入记录的ID
    """
    # 处理JSON数据
    if details and not isinstance(details, str):
        details = json.dumps(details)
    
    # 构建SQL语句
    sql = text('''
    INSERT INTO audit_log
    (action, resource_type, resource_id, user_id, area_id, details, ip_address, created_at)
    OUTPUT inserted.id
    VALUES
    (:action, :resource_type, :resource_id, :user_id, :area_id, :details, :ip_address, GETDATE())
    ''')
    
    # 执行SQL
    result = db.session.execute(
        sql,
        {
            'action': action,
            'resource_type': resource_type,
            'resource_id': resource_id,
            'user_id': user_id,
            'area_id': area_id,
            'details': details,
            'ip_address': ip_address
        }
    )
    
    # 获取新插入记录的ID
    new_id = result.scalar()
    
    return new_id

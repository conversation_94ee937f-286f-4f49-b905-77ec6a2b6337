name: Docs

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 16

jobs:
  docs:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "${{ env.NODE }}"
          cache: npm

      - run: java -version

      - name: Install npm dependencies
        run: npm ci

      - name: Build docs
        run: npm run docs-build

      - name: Validate HTML
        run: npm run docs-vnu

      - name: Run linkinator
        uses: JustinBeckwith/linkinator-action@v1
        with:
          paths: _site
          recurse: true
          verbosity: error
          skip: "^(?!http://localhost)"

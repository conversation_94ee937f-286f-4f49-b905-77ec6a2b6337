---
layout: docs
title: Brand guidelines
description: Documentation and examples for Bootstrap's logo and brand usage guidelines.
group: about
toc: true
---

Have a need for Bootstrap's brand resources? Great! We have only a few guidelines we follow, and in turn ask you to follow as well. These guidelines were inspired by MailChimp's [Brand Assets](https://mailchimp.com/about/brand-assets/).

## Mark and logo

Use either the Bootstrap mark (a capital **B**) or the standard logo (just **Bootstrap**). It should always appear in San Francisco Display Semibold. **Do not use the Twitter bird** in association with Bootstrap.

<div class="bd-brand-logos">
  <div class="bd-brand-item">
    <img class="svg" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-solid.svg" alt="Bootstrap" width="144" height="144" loading="lazy">
  </div>
  <div class="bd-brand-item inverse">
    <img class="svg" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-outline.svg" alt="Bootstrap" width="144" height="144" loading="lazy">
  </div>
</div>
<div class="bd-brand-logos">
  <div class="bd-brand-item">
    <span class="h1">Bootstrap</span>
  </div>
  <div class="bd-brand-item inverse">
    <span class="h1">Bootstrap</span>
  </div>
</div>

## Download mark

Download the Bootstrap mark in one of three styles, each available as an SVG file. Right click, Save as.

<div class="bd-brand-logos">
  <div class="bd-brand-item">
    <img class="svg" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-solid.svg" alt="Bootstrap" width="144" height="144" loading="lazy">
  </div>
  <div class="bd-brand-item inverse">
    <img class="svg" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-outline.svg" alt="Bootstrap" width="144" height="144" loading="lazy">
  </div>
  <div class="bd-brand-item inverse">
    <img class="svg" src="/docs/{{< param docs_version >}}/assets/brand/bootstrap-punchout.svg" alt="Bootstrap" width="144" height="144" loading="lazy">
  </div>
</div>

## Name

The project and framework should always be referred to as **Bootstrap**. No Twitter before it, no capital _s_, and no abbreviations except for one, a capital **B**.

<div class="bd-brand-logos">
  <div class="bd-brand-item">
    <span class="h3">Bootstrap</span>
    <strong class="text-success">Right</strong>
  </div>
  <div class="bd-brand-item">
    <span class="h3 text-muted">BootStrap</span>
    <strong class="text-warning">Wrong</strong>
  </div>
  <div class="bd-brand-item">
    <span class="h3 text-muted">Twitter Bootstrap</span>
    <strong class="text-warning">Wrong</strong>
  </div>
</div>

## Colors

Our docs and branding use a handful of primary colors to differentiate what *is* Bootstrap from what *is in* Bootstrap. In other words, if it's purple, it's representative of Bootstrap.

<div class="bd-brand">
  <div class="color-swatches">
    <div class="color-swatch bd-purple"></div>
    <div class="color-swatch bd-purple-light"></div>
    <div class="color-swatch bd-purple-lighter"></div>
    <div class="color-swatch bd-gray"></div>
  </div>
</div>

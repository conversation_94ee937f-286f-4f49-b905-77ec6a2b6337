{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建产品批次</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <div class="progress mb-4">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100">步骤 1/5</div>
                            </div>
                            
                            <form method="post">
                                {{ form.csrf_token }}
                                
                                <div class="form-group">
                                    {{ form.name.label }}
                                    {{ form.name(class="form-control", placeholder="请输入批次名称，如：蔬菜类产品-2023年5月") }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">批次名称用于标识一组产品，建议包含分类和日期信息</small>
                                </div>
                                
                                <div class="form-group">
                                    {{ form.category_id.label }}
                                    {{ form.category_id(class="form-control") }}
                                    {% if form.category_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.category_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">选择产品所属的食材分类，如：蔬菜、肉类等</small>
                                </div>
                                
                                <div class="form-group">
                                    {{ form.supplier_id.label }}
                                    {{ form.supplier_id(class="form-control") }}
                                    {% if form.supplier_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.supplier_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">选择产品的供应商</small>
                                </div>
                                
                                <div class="form-group text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.index') }}" class="btn btn-default">取消</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends 'base.html' %}

{% block title %}仓库详情{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">仓库详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('warehouse.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('warehouse.edit', id=warehouse.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">仓库名称</th>
                                    <td>{{ warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>所属区域</th>
                                    <td>{{ warehouse.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>位置</th>
                                    <td>{{ warehouse.location }}</td>
                                </tr>
                                <tr>
                                    <th>管理员</th>
                                    <td>{{ warehouse.manager.real_name or warehouse.manager.username }}</td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{  warehouse.created_at|format_datetime('%Y-%m-%d %H:%M:%S')  }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">容量</th>
                                    <td>{{ warehouse.capacity }} {{ warehouse.capacity_unit }}</td>
                                </tr>
                                <tr>
                                    <th>温度范围</th>
                                    <td>{{ warehouse.temperature_range or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>湿度范围</th>
                                    <td>{{ warehouse.humidity_range or '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if warehouse.status == '正常' %}
                                        <span class="badge badge-success">正常</span>
                                        {% elif warehouse.status == '维护中' %}
                                        <span class="badge badge-warning">维护中</span>
                                        {% elif warehouse.status == '已关闭' %}
                                        <span class="badge badge-danger">已关闭</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ warehouse.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 存储位置列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">存储位置列表</h4>
                            <div class="card-tools">
                                <a href="{{ url_for('storage_location.create', warehouse_id=warehouse.id) }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> 添加存储位置
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>位置编码</th>
                                            <th>存储类型</th>
                                            <th>容量</th>
                                            <th>温度范围</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for location in storage_locations %}
                                        <tr>
                                            <td>{{ location.name }}</td>
                                            <td>{{ location.location_code }}</td>
                                            <td>{{ location.storage_type }}</td>
                                            <td>{{ location.capacity }} {{ location.capacity_unit }}</td>
                                            <td>{{ location.temperature_range or '-' }}</td>
                                            <td>
                                                {% if location.status == '正常' %}
                                                <span class="badge badge-success">正常</span>
                                                {% elif location.status == '维护中' %}
                                                <span class="badge badge-warning">维护中</span>
                                                {% elif location.status == '已关闭' %}
                                                <span class="badge badge-danger">已关闭</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('storage_location.view', id=location.id) }}" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                                <a href="{{ url_for('storage_location.edit', id=location.id) }}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-edit"></i> 编辑
                                                </a>
                                                <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#deleteModal{{ location.id }}">
                                                    <i class="fas fa-trash"></i> 删除
                                                </button>
                                                
                                                <!-- 删除确认模态框 -->
                                                <div class="modal fade" id="deleteModal{{ location.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel{{ location.id }}" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="deleteModalLabel{{ location.id }}">确认删除</h5>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                确定要删除存储位置 "{{ location.name }}" 吗？此操作不可撤销。
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                                                                <form action="{{ url_for('storage_location.delete', id=location.id) }}" method="post"><button type="submit" class="btn btn-danger">确认删除</button>
                                                                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无存储位置数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{"version": 3, "file": "modal.js", "sources": ["../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst CLASS_NAME_SCROLLABLE = 'modal-dialog-scrollable'\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = element.querySelector(SELECTOR_DIALOG)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = $.Event(EVENT_SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    $(this._dialog).on(EVENT_MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(EVENT_MOUSEUP_DISMISS, event => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(EVENT_HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(EVENT_FOCUSIN)\n\n    $(this._element).removeClass(CLASS_NAME_SHOW)\n\n    $(this._element).off(EVENT_CLICK_DISMISS)\n    $(this._dialog).off(EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, event => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    $(document).off(EVENT_FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _triggerBackdropTransition() {\n    const hideEventPrevented = $.Event(EVENT_HIDE_PREVENTED)\n\n    $(this._element).trigger(hideEventPrevented)\n    if (hideEventPrevented.isDefaultPrevented()) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n\n    const modalTransitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n    $(this._element).off(Util.TRANSITION_END)\n\n    $(this._element).one(Util.TRANSITION_END, () => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      if (!isModalOverflowing) {\n        $(this._element).one(Util.TRANSITION_END, () => {\n          this._element.style.overflowY = ''\n        })\n          .emulateTransitionEnd(this._element, modalTransitionDuration)\n      }\n    })\n      .emulateTransitionEnd(modalTransitionDuration)\n    this._element.focus()\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(CLASS_NAME_FADE)\n    const modalBody = this._dialog ? this._dialog.querySelector(SELECTOR_MODAL_BODY) : null\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n\n    if ($(this._dialog).hasClass(CLASS_NAME_SCROLLABLE) && modalBody) {\n      modalBody.scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(EVENT_SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(EVENT_FOCUSIN) // Guard against infinite focus loop\n      .on(EVENT_FOCUSIN, event => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      $(this._element).on(EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.which === ESCAPE_KEYCODE) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(EVENT_RESIZE, event => this.handleUpdate(event))\n    } else {\n      $(window).off(EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n        } else {\n          this.hide()\n        }\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(CLASS_NAME_SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(SELECTOR_STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(SELECTOR_FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${SELECTOR_STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY) ?\n    'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(EVENT_SHOW, showEvent => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(EVENT_HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "CLASS_NAME_SCROLLABLE", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "CLASS_NAME_STATIC", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_CLICK_DATA_API", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "Modal", "element", "config", "_config", "_getConfig", "_element", "_dialog", "querySelector", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_scrollbarWidth", "toggle", "relatedTarget", "hide", "showEvent", "Event", "trigger", "isDefaultPrevented", "hasClass", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "hideEvent", "transition", "document", "off", "removeClass", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "window", "for<PERSON>ach", "htmlElement", "removeData", "handleUpdate", "typeCheckConfig", "_triggerBackdropTransition", "hideEventPrevented", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "style", "overflowY", "classList", "add", "modalTransitionDuration", "remove", "modalBody", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "body", "append<PERSON><PERSON><PERSON>", "display", "removeAttribute", "setAttribute", "scrollTop", "reflow", "addClass", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "createElement", "className", "appendTo", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "rect", "getBoundingClientRect", "Math", "round", "left", "right", "innerWidth", "_getScrollbarWidth", "fixedContent", "slice", "call", "querySelectorAll", "sticky<PERSON>ontent", "each", "index", "actualPadding", "calculatedPadding", "css", "data", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "elements", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "_jQueryInterface", "TypeError", "selector", "getSelectorFromElement", "tagName", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,OAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EACA,IAAMQ,cAAc,GAAG,EAAvB;;EAEA,IAAMC,qBAAqB,GAAG,yBAA9B,CAAA;EACA,IAAMC,6BAA6B,GAAG,yBAAtC,CAAA;EACA,IAAMC,mBAAmB,GAAG,gBAA5B,CAAA;EACA,IAAMC,eAAe,GAAG,YAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,iBAAiB,GAAG,cAA1B,CAAA;EAEA,IAAMC,UAAU,YAAUb,SAA1B,CAAA;EACA,IAAMc,oBAAoB,qBAAmBd,SAA7C,CAAA;EACA,IAAMe,YAAY,cAAYf,SAA9B,CAAA;EACA,IAAMgB,UAAU,YAAUhB,SAA1B,CAAA;EACA,IAAMiB,WAAW,aAAWjB,SAA5B,CAAA;EACA,IAAMkB,aAAa,eAAalB,SAAhC,CAAA;EACA,IAAMmB,YAAY,cAAYnB,SAA9B,CAAA;EACA,IAAMoB,mBAAmB,qBAAmBpB,SAA5C,CAAA;EACA,IAAMqB,qBAAqB,uBAAqBrB,SAAhD,CAAA;EACA,IAAMsB,qBAAqB,uBAAqBtB,SAAhD,CAAA;EACA,IAAMuB,uBAAuB,yBAAuBvB,SAApD,CAAA;EACA,IAAMwB,oBAAoB,GAAA,OAAA,GAAWxB,SAAX,GAAuBC,YAAjD,CAAA;EAEA,IAAMwB,eAAe,GAAG,eAAxB,CAAA;EACA,IAAMC,mBAAmB,GAAG,aAA5B,CAAA;EACA,IAAMC,oBAAoB,GAAG,uBAA7B,CAAA;EACA,IAAMC,qBAAqB,GAAG,wBAA9B,CAAA;EACA,IAAMC,sBAAsB,GAAG,mDAA/B,CAAA;EACA,IAAMC,uBAAuB,GAAG,aAAhC,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,IAHO;EAIdC,EAAAA,IAAI,EAAE,IAAA;EAJQ,CAAhB,CAAA;EAOA,IAAMC,WAAW,GAAG;EAClBJ,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,SAHW;EAIlBC,EAAAA,IAAI,EAAE,SAAA;EAJY,CAApB,CAAA;EAOA;EACA;EACA;;MAEME;IACJ,SAAYC,KAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBF,MAAhB,CAAf,CAAA;MACA,IAAKG,CAAAA,QAAL,GAAgBJ,OAAhB,CAAA;EACA,IAAA,IAAA,CAAKK,OAAL,GAAeL,OAAO,CAACM,aAAR,CAAsBnB,eAAtB,CAAf,CAAA;MACA,IAAKoB,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,KAAhB,CAAA;MACA,IAAKC,CAAAA,kBAAL,GAA0B,KAA1B,CAAA;MACA,IAAKC,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;MACA,IAAKC,CAAAA,eAAL,GAAuB,CAAvB,CAAA;EACD;;;;;EAWD;WACAC,SAAA,SAAOC,MAAAA,CAAAA,aAAP,EAAsB;MACpB,OAAO,IAAA,CAAKN,QAAL,GAAgB,IAAKO,CAAAA,IAAL,EAAhB,GAA8B,IAAKlB,CAAAA,IAAL,CAAUiB,aAAV,CAArC,CAAA;;;WAGFjB,OAAA,SAAKiB,IAAAA,CAAAA,aAAL,EAAoB;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;EAClB,IAAA,IAAI,IAAKN,CAAAA,QAAL,IAAiB,IAAA,CAAKG,gBAA1B,EAA4C;EAC1C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMK,SAAS,GAAGnD,qBAAC,CAACoD,KAAF,CAAQvC,UAAR,EAAoB;EACpCoC,MAAAA,aAAa,EAAbA,aAAAA;EADoC,KAApB,CAAlB,CAAA;EAIAjD,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBc,OAAjB,CAAyBF,SAAzB,CAAA,CAAA;;EAEA,IAAA,IAAIA,SAAS,CAACG,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;MAED,IAAKX,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;MAEA,IAAI3C,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CAAiBgB,QAAjB,CAA0BhD,eAA1B,CAAJ,EAAgD;QAC9C,IAAKuC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKU,eAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;;EAEA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;;EAEA,IAAA,IAAA,CAAKC,eAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKC,eAAL,EAAA,CAAA;;EAEA5D,IAAAA,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CAAiBsB,EAAjB,CACE5C,mBADF,EAEEQ,qBAFF,EAGE,UAAAqC,KAAK,EAAA;EAAA,MAAA,OAAI,KAAI,CAACZ,IAAL,CAAUY,KAAV,CAAJ,CAAA;OAHP,CAAA,CAAA;MAMA9D,qBAAC,CAAC,KAAKwC,OAAN,CAAD,CAAgBqB,EAAhB,CAAmBzC,uBAAnB,EAA4C,YAAM;EAChDpB,MAAAA,qBAAC,CAAC,KAAI,CAACuC,QAAN,CAAD,CAAiBwB,GAAjB,CAAqB5C,qBAArB,EAA4C,UAAA2C,KAAK,EAAI;EACnD,QAAA,IAAI9D,qBAAC,CAAC8D,KAAK,CAACE,MAAP,CAAD,CAAgBC,EAAhB,CAAmB,KAAI,CAAC1B,QAAxB,CAAJ,EAAuC;YACrC,KAAI,CAACM,oBAAL,GAA4B,IAA5B,CAAA;EACD,SAAA;SAHH,CAAA,CAAA;OADF,CAAA,CAAA;;EAQA,IAAA,IAAA,CAAKqB,aAAL,CAAmB,YAAA;EAAA,MAAA,OAAM,KAAI,CAACC,YAAL,CAAkBlB,aAAlB,CAAN,CAAA;OAAnB,CAAA,CAAA;;;WAGFC,OAAA,SAAKY,IAAAA,CAAAA,KAAL,EAAY;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACV,IAAA,IAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAACM,cAAN,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAAC,IAAKzB,CAAAA,QAAN,IAAkB,IAAA,CAAKG,gBAA3B,EAA6C;EAC3C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMuB,SAAS,GAAGrE,qBAAC,CAACoD,KAAF,CAAQ1C,UAAR,CAAlB,CAAA;EAEAV,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBc,OAAjB,CAAyBgB,SAAzB,CAAA,CAAA;;MAEA,IAAI,CAAC,KAAK1B,QAAN,IAAkB0B,SAAS,CAACf,kBAAV,EAAtB,EAAsD;EACpD,MAAA,OAAA;EACD,KAAA;;MAED,IAAKX,CAAAA,QAAL,GAAgB,KAAhB,CAAA;MACA,IAAM2B,UAAU,GAAGtE,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CAAiBgB,QAAjB,CAA0BhD,eAA1B,CAAnB,CAAA;;EAEA,IAAA,IAAI+D,UAAJ,EAAgB;QACd,IAAKxB,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKa,eAAL,EAAA,CAAA;;EACA,IAAA,IAAA,CAAKC,eAAL,EAAA,CAAA;;EAEA5D,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CAAYC,GAAZ,CAAgBzD,aAAhB,CAAA,CAAA;EAEAf,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBkC,WAAjB,CAA6BjE,eAA7B,CAAA,CAAA;EAEAR,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBvD,mBAArB,CAAA,CAAA;EACAjB,IAAAA,qBAAC,CAAC,IAAKwC,CAAAA,OAAN,CAAD,CAAgBgC,GAAhB,CAAoBpD,uBAApB,CAAA,CAAA;;EAEA,IAAA,IAAIkD,UAAJ,EAAgB;QACd,IAAMI,kBAAkB,GAAGC,wBAAI,CAACC,gCAAL,CAAsC,IAAA,CAAKrC,QAA3C,CAA3B,CAAA;EAEAvC,MAAAA,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CACGwB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4B,UAAAf,KAAK,EAAA;EAAA,QAAA,OAAI,MAAI,CAACgB,UAAL,CAAgBhB,KAAhB,CAAJ,CAAA;SADjC,CAAA,CAEGiB,oBAFH,CAEwBL,kBAFxB,CAAA,CAAA;EAGD,KAND,MAMO;EACL,MAAA,IAAA,CAAKI,UAAL,EAAA,CAAA;EACD,KAAA;;;EAGHE,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;MACR,CAACC,MAAD,EAAS,IAAA,CAAK1C,QAAd,EAAwB,IAAKC,CAAAA,OAA7B,CACG0C,CAAAA,OADH,CACW,UAAAC,WAAW,EAAA;QAAA,OAAInF,qBAAC,CAACmF,WAAD,CAAD,CAAeX,GAAf,CAAmB3E,SAAnB,CAAJ,CAAA;OADtB,CAAA,CAAA;EAGA;EACJ;EACA;EACA;EACA;;EACIG,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CAAYC,GAAZ,CAAgBzD,aAAhB,CAAA,CAAA;EAEAf,IAAAA,qBAAC,CAACoF,UAAF,CAAa,IAAK7C,CAAAA,QAAlB,EAA4B3C,QAA5B,CAAA,CAAA;MAEA,IAAKyC,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKE,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKE,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,kBAAL,GAA0B,IAA1B,CAAA;MACA,IAAKC,CAAAA,oBAAL,GAA4B,IAA5B,CAAA;MACA,IAAKC,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;MACA,IAAKC,CAAAA,eAAL,GAAuB,IAAvB,CAAA;;;EAGFsC,EAAAA,MAAAA,CAAAA,eAAA,SAAe,YAAA,GAAA;EACb,IAAA,IAAA,CAAK3B,aAAL,EAAA,CAAA;EACD;;;WAGDpB,aAAA,SAAWF,UAAAA,CAAAA,MAAX,EAAmB;EACjBA,IAAAA,MAAM,GACDR,QAAAA,CAAAA,EAAAA,EAAAA,OADC,EAEDQ,MAFC,CAAN,CAAA;EAIAuC,IAAAA,wBAAI,CAACW,eAAL,CAAqB5F,IAArB,EAA2B0C,MAA3B,EAAmCH,WAAnC,CAAA,CAAA;EACA,IAAA,OAAOG,MAAP,CAAA;;;EAGFmD,EAAAA,MAAAA,CAAAA,6BAAA,SAA6B,0BAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EAC3B,IAAA,IAAMC,kBAAkB,GAAGxF,qBAAC,CAACoD,KAAF,CAAQzC,oBAAR,CAA3B,CAAA;EAEAX,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBc,OAAjB,CAAyBmC,kBAAzB,CAAA,CAAA;;EACA,IAAA,IAAIA,kBAAkB,CAAClC,kBAAnB,EAAJ,EAA6C;EAC3C,MAAA,OAAA;EACD,KAAA;;MAED,IAAMmC,kBAAkB,GAAG,IAAA,CAAKlD,QAAL,CAAcmD,YAAd,GAA6BnB,QAAQ,CAACoB,eAAT,CAAyBC,YAAjF,CAAA;;MAEA,IAAI,CAACH,kBAAL,EAAyB;EACvB,MAAA,IAAA,CAAKlD,QAAL,CAAcsD,KAAd,CAAoBC,SAApB,GAAgC,QAAhC,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKvD,QAAL,CAAcwD,SAAd,CAAwBC,GAAxB,CAA4BvF,iBAA5B,CAAA,CAAA;;MAEA,IAAMwF,uBAAuB,GAAGtB,wBAAI,CAACC,gCAAL,CAAsC,IAAA,CAAKpC,OAA3C,CAAhC,CAAA;MACAxC,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBG,wBAAI,CAACE,cAA1B,CAAA,CAAA;MAEA7E,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBwB,GAAjB,CAAqBY,wBAAI,CAACE,cAA1B,EAA0C,YAAM;EAC9C,MAAA,MAAI,CAACtC,QAAL,CAAcwD,SAAd,CAAwBG,MAAxB,CAA+BzF,iBAA/B,CAAA,CAAA;;QACA,IAAI,CAACgF,kBAAL,EAAyB;EACvBzF,QAAAA,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBwB,GAAjB,CAAqBY,wBAAI,CAACE,cAA1B,EAA0C,YAAM;EAC9C,UAAA,MAAI,CAACtC,QAAL,CAAcsD,KAAd,CAAoBC,SAApB,GAAgC,EAAhC,CAAA;EACD,SAFD,EAGGf,oBAHH,CAGwB,MAAI,CAACxC,QAH7B,EAGuC0D,uBAHvC,CAAA,CAAA;EAID,OAAA;OAPH,CAAA,CASGlB,oBATH,CASwBkB,uBATxB,CAAA,CAAA;;MAUA,IAAK1D,CAAAA,QAAL,CAAcR,KAAd,EAAA,CAAA;;;WAGFoC,eAAA,SAAalB,YAAAA,CAAAA,aAAb,EAA4B;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MAC1B,IAAMqB,UAAU,GAAGtE,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CAAiBgB,QAAjB,CAA0BhD,eAA1B,CAAnB,CAAA;EACA,IAAA,IAAM4F,SAAS,GAAG,IAAK3D,CAAAA,OAAL,GAAe,IAAA,CAAKA,OAAL,CAAaC,aAAb,CAA2BlB,mBAA3B,CAAf,GAAiE,IAAnF,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAKgB,CAAAA,QAAL,CAAc6D,UAAf,IACA,IAAK7D,CAAAA,QAAL,CAAc6D,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAhC,MAAAA,QAAQ,CAACiC,IAAT,CAAcC,WAAd,CAA0B,KAAKlE,QAA/B,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKA,QAAL,CAAcsD,KAAd,CAAoBa,OAApB,GAA8B,OAA9B,CAAA;;EACA,IAAA,IAAA,CAAKnE,QAAL,CAAcoE,eAAd,CAA8B,aAA9B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKpE,QAAL,CAAcqE,YAAd,CAA2B,YAA3B,EAAyC,IAAzC,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKrE,QAAL,CAAcqE,YAAd,CAA2B,MAA3B,EAAmC,QAAnC,CAAA,CAAA;;MAEA,IAAI5G,qBAAC,CAAC,IAAA,CAAKwC,OAAN,CAAD,CAAgBe,QAAhB,CAAyBpD,qBAAzB,CAAmDgG,IAAAA,SAAvD,EAAkE;QAChEA,SAAS,CAACU,SAAV,GAAsB,CAAtB,CAAA;EACD,KAFD,MAEO;EACL,MAAA,IAAA,CAAKtE,QAAL,CAAcsE,SAAd,GAA0B,CAA1B,CAAA;EACD,KAAA;;EAED,IAAA,IAAIvC,UAAJ,EAAgB;EACdK,MAAAA,wBAAI,CAACmC,MAAL,CAAY,IAAA,CAAKvE,QAAjB,CAAA,CAAA;EACD,KAAA;;EAEDvC,IAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBwE,QAAjB,CAA0BvG,eAA1B,CAAA,CAAA;;EAEA,IAAA,IAAI,IAAK6B,CAAAA,OAAL,CAAaN,KAAjB,EAAwB;EACtB,MAAA,IAAA,CAAKiF,aAAL,EAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAMC,UAAU,GAAGjH,qBAAC,CAACoD,KAAF,CAAQtC,WAAR,EAAqB;EACtCmC,MAAAA,aAAa,EAAbA,aAAAA;EADsC,KAArB,CAAnB,CAAA;;EAIA,IAAA,IAAMiE,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,MAAA,IAAI,MAAI,CAAC7E,OAAL,CAAaN,KAAjB,EAAwB;UACtB,MAAI,CAACQ,QAAL,CAAcR,KAAd,EAAA,CAAA;EACD,OAAA;;QAED,MAAI,CAACe,gBAAL,GAAwB,KAAxB,CAAA;QACA9C,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyB4D,UAAzB,CAAA,CAAA;OANF,CAAA;;EASA,IAAA,IAAI3C,UAAJ,EAAgB;QACd,IAAMI,kBAAkB,GAAGC,wBAAI,CAACC,gCAAL,CAAsC,IAAA,CAAKpC,OAA3C,CAA3B,CAAA;EAEAxC,MAAAA,qBAAC,CAAC,IAAA,CAAKwC,OAAN,CAAD,CACGuB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4BqC,kBAD5B,CAEGnC,CAAAA,oBAFH,CAEwBL,kBAFxB,CAAA,CAAA;EAGD,KAND,MAMO;QACLwC,kBAAkB,EAAA,CAAA;EACnB,KAAA;;;EAGHF,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACdhH,IAAAA,qBAAC,CAACuE,QAAD,CAAD,CACGC,GADH,CACOzD,aADP,CACsB;EADtB,KAEG8C,EAFH,CAEM9C,aAFN,EAEqB,UAAA+C,KAAK,EAAI;EAC1B,MAAA,IAAIS,QAAQ,KAAKT,KAAK,CAACE,MAAnB,IACA,MAAI,CAACzB,QAAL,KAAkBuB,KAAK,CAACE,MADxB,IAEAhE,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiB4E,GAAjB,CAAqBrD,KAAK,CAACE,MAA3B,CAAA,CAAmCoD,MAAnC,KAA8C,CAFlD,EAEqD;UACnD,MAAI,CAAC7E,QAAL,CAAcR,KAAd,EAAA,CAAA;EACD,OAAA;OAPL,CAAA,CAAA;;;EAWF4B,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MAChB,IAAI,IAAA,CAAKhB,QAAT,EAAmB;QACjB3C,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBsB,EAAjB,CAAoB3C,qBAApB,EAA2C,UAAA4C,KAAK,EAAI;UAClD,IAAI,MAAI,CAACzB,OAAL,CAAaP,QAAb,IAAyBgC,KAAK,CAACuD,KAAN,KAAgBnH,cAA7C,EAA6D;EAC3D4D,UAAAA,KAAK,CAACM,cAAN,EAAA,CAAA;;EACA,UAAA,MAAI,CAAClB,IAAL,EAAA,CAAA;EACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAACb,OAAL,CAAaP,QAAd,IAA0BgC,KAAK,CAACuD,KAAN,KAAgBnH,cAA9C,EAA8D;EACnE,UAAA,MAAI,CAACqF,0BAAL,EAAA,CAAA;EACD,SAAA;SANH,CAAA,CAAA;EAQD,KATD,MASO,IAAI,CAAC,IAAA,CAAK5C,QAAV,EAAoB;EACzB3C,MAAAA,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBiC,GAAjB,CAAqBtD,qBAArB,CAAA,CAAA;EACD,KAAA;;;EAGH0C,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MAChB,IAAI,IAAA,CAAKjB,QAAT,EAAmB;QACjB3C,qBAAC,CAACiF,MAAD,CAAD,CAAUpB,EAAV,CAAa7C,YAAb,EAA2B,UAAA8C,KAAK,EAAA;EAAA,QAAA,OAAI,MAAI,CAACuB,YAAL,CAAkBvB,KAAlB,CAAJ,CAAA;SAAhC,CAAA,CAAA;EACD,KAFD,MAEO;EACL9D,MAAAA,qBAAC,CAACiF,MAAD,CAAD,CAAUT,GAAV,CAAcxD,YAAd,CAAA,CAAA;EACD,KAAA;;;EAGH8D,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACX,IAAA,IAAA,CAAKvC,QAAL,CAAcsD,KAAd,CAAoBa,OAApB,GAA8B,MAA9B,CAAA;;EACA,IAAA,IAAA,CAAKnE,QAAL,CAAcqE,YAAd,CAA2B,aAA3B,EAA0C,IAA1C,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKrE,QAAL,CAAcoE,eAAd,CAA8B,YAA9B,CAAA,CAAA;;EACA,IAAA,IAAA,CAAKpE,QAAL,CAAcoE,eAAd,CAA8B,MAA9B,CAAA,CAAA;;MACA,IAAK7D,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;MACA,IAAKoB,CAAAA,aAAL,CAAmB,YAAM;QACvBlE,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB/B,WAAjB,CAA6BnE,eAA7B,CAAA,CAAA;;EACA,MAAA,MAAI,CAACgH,iBAAL,EAAA,CAAA;;EACA,MAAA,MAAI,CAACC,eAAL,EAAA,CAAA;;QACAvH,qBAAC,CAAC,MAAI,CAACuC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBzC,YAAzB,CAAA,CAAA;OAJF,CAAA,CAAA;;;EAQF4G,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;MAChB,IAAI,IAAA,CAAK9E,SAAT,EAAoB;EAClB1C,MAAAA,qBAAC,CAAC,IAAA,CAAK0C,SAAN,CAAD,CAAkBwD,MAAlB,EAAA,CAAA;QACA,IAAKxD,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACD,KAAA;;;WAGHwB,gBAAA,SAAcuD,aAAAA,CAAAA,QAAd,EAAwB;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACtB,IAAA,IAAMC,OAAO,GAAG1H,qBAAC,CAAC,KAAKuC,QAAN,CAAD,CAAiBgB,QAAjB,CAA0BhD,eAA1B,CACdA,GAAAA,eADc,GACI,EADpB,CAAA;;EAGA,IAAA,IAAI,KAAKoC,QAAL,IAAiB,KAAKN,OAAL,CAAaR,QAAlC,EAA4C;EAC1C,MAAA,IAAA,CAAKa,SAAL,GAAiB6B,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAjB,CAAA;EACA,MAAA,IAAA,CAAKjF,SAAL,CAAekF,SAAf,GAA2BvH,mBAA3B,CAAA;;EAEA,MAAA,IAAIqH,OAAJ,EAAa;EACX,QAAA,IAAA,CAAKhF,SAAL,CAAeqD,SAAf,CAAyBC,GAAzB,CAA6B0B,OAA7B,CAAA,CAAA;EACD,OAAA;;QAED1H,qBAAC,CAAC,KAAK0C,SAAN,CAAD,CAAkBmF,QAAlB,CAA2BtD,QAAQ,CAACiC,IAApC,CAAA,CAAA;QAEAxG,qBAAC,CAAC,IAAKuC,CAAAA,QAAN,CAAD,CAAiBsB,EAAjB,CAAoB5C,mBAApB,EAAyC,UAAA6C,KAAK,EAAI;UAChD,IAAI,MAAI,CAACjB,oBAAT,EAA+B;YAC7B,MAAI,CAACA,oBAAL,GAA4B,KAA5B,CAAA;EACA,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,IAAIiB,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAACgE,aAA3B,EAA0C;EACxC,UAAA,OAAA;EACD,SAAA;;EAED,QAAA,IAAI,MAAI,CAACzF,OAAL,CAAaR,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAA,MAAI,CAAC0D,0BAAL,EAAA,CAAA;EACD,SAFD,MAEO;EACL,UAAA,MAAI,CAACrC,IAAL,EAAA,CAAA;EACD,SAAA;SAdH,CAAA,CAAA;;EAiBA,MAAA,IAAIwE,OAAJ,EAAa;EACX/C,QAAAA,wBAAI,CAACmC,MAAL,CAAY,IAAA,CAAKpE,SAAjB,CAAA,CAAA;EACD,OAAA;;EAED1C,MAAAA,qBAAC,CAAC,IAAK0C,CAAAA,SAAN,CAAD,CAAkBqE,QAAlB,CAA2BvG,eAA3B,CAAA,CAAA;;QAEA,IAAI,CAACiH,QAAL,EAAe;EACb,QAAA,OAAA;EACD,OAAA;;QAED,IAAI,CAACC,OAAL,EAAc;UACZD,QAAQ,EAAA,CAAA;EACR,QAAA,OAAA;EACD,OAAA;;QAED,IAAMM,0BAA0B,GAAGpD,wBAAI,CAACC,gCAAL,CAAsC,IAAA,CAAKlC,SAA3C,CAAnC,CAAA;EAEA1C,MAAAA,qBAAC,CAAC,IAAA,CAAK0C,SAAN,CAAD,CACGqB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4B4C,QAD5B,CAEG1C,CAAAA,oBAFH,CAEwBgD,0BAFxB,CAAA,CAAA;OA5CF,MA+CO,IAAI,CAAC,IAAA,CAAKpF,QAAN,IAAkB,IAAA,CAAKD,SAA3B,EAAsC;EAC3C1C,MAAAA,qBAAC,CAAC,IAAK0C,CAAAA,SAAN,CAAD,CAAkB+B,WAAlB,CAA8BjE,eAA9B,CAAA,CAAA;;EAEA,MAAA,IAAMwH,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACR,eAAL,EAAA,CAAA;;EACA,QAAA,IAAIC,QAAJ,EAAc;YACZA,QAAQ,EAAA,CAAA;EACT,SAAA;SAJH,CAAA;;QAOA,IAAIzH,qBAAC,CAAC,IAAA,CAAKuC,QAAN,CAAD,CAAiBgB,QAAjB,CAA0BhD,eAA1B,CAAJ,EAAgD;UAC9C,IAAMwH,2BAA0B,GAAGpD,wBAAI,CAACC,gCAAL,CAAsC,IAAA,CAAKlC,SAA3C,CAAnC,CAAA;;EAEA1C,QAAAA,qBAAC,CAAC,IAAA,CAAK0C,SAAN,CAAD,CACGqB,GADH,CACOY,wBAAI,CAACE,cADZ,EAC4BmD,cAD5B,CAEGjD,CAAAA,oBAFH,CAEwBgD,2BAFxB,CAAA,CAAA;EAGD,OAND,MAMO;UACLC,cAAc,EAAA,CAAA;EACf,OAAA;OAlBI,MAmBA,IAAIP,QAAJ,EAAc;QACnBA,QAAQ,EAAA,CAAA;EACT,KAAA;EACF;EAGD;EACA;EACA;;;EAEA/D,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;MACd,IAAM+B,kBAAkB,GAAG,IAAA,CAAKlD,QAAL,CAAcmD,YAAd,GAA6BnB,QAAQ,CAACoB,eAAT,CAAyBC,YAAjF,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKhD,kBAAN,IAA4B6C,kBAAhC,EAAoD;EAClD,MAAA,IAAA,CAAKlD,QAAL,CAAcsD,KAAd,CAAoBoC,WAApB,GAAqC,KAAKlF,eAA1C,GAAA,IAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKH,CAAAA,kBAAL,IAA2B,CAAC6C,kBAAhC,EAAoD;EAClD,MAAA,IAAA,CAAKlD,QAAL,CAAcsD,KAAd,CAAoBqC,YAApB,GAAsC,KAAKnF,eAA3C,GAAA,IAAA,CAAA;EACD,KAAA;;;EAGHuE,EAAAA,MAAAA,CAAAA,oBAAA,SAAoB,iBAAA,GAAA;EAClB,IAAA,IAAA,CAAK/E,QAAL,CAAcsD,KAAd,CAAoBoC,WAApB,GAAkC,EAAlC,CAAA;EACA,IAAA,IAAA,CAAK1F,QAAL,CAAcsD,KAAd,CAAoBqC,YAApB,GAAmC,EAAnC,CAAA;;;EAGF1E,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;EAChB,IAAA,IAAM2E,IAAI,GAAG5D,QAAQ,CAACiC,IAAT,CAAc4B,qBAAd,EAAb,CAAA;EACA,IAAA,IAAA,CAAKxF,kBAAL,GAA0ByF,IAAI,CAACC,KAAL,CAAWH,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACK,KAA5B,CAAqCvD,GAAAA,MAAM,CAACwD,UAAtE,CAAA;EACA,IAAA,IAAA,CAAK1F,eAAL,GAAuB,IAAK2F,CAAAA,kBAAL,EAAvB,CAAA;;;EAGFjF,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EAAA,IAAA,IAAA,OAAA,GAAA,IAAA,CAAA;;MACd,IAAI,IAAA,CAAKb,kBAAT,EAA6B;EAC3B;EACA;EACA,MAAA,IAAM+F,YAAY,GAAG,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0BpH,sBAA1B,CAAd,CAArB,CAAA;EACA,MAAA,IAAMqH,aAAa,GAAG,EAAGH,CAAAA,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0BnH,uBAA1B,CAAd,CAAtB,CAJ2B;;QAO3B3B,qBAAC,CAAC2I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACvC,QAAA,IAAM+G,aAAa,GAAG/G,OAAO,CAAC0D,KAAR,CAAcqC,YAApC,CAAA;UACA,IAAMiB,iBAAiB,GAAGnJ,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,eAAf,CAA1B,CAAA;UACApJ,qBAAC,CAACmC,OAAD,CAAD,CACGkH,IADH,CACQ,eADR,EACyBH,aADzB,CAAA,CAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,OAAI,CAACpG,eAFhE,GAAA,IAAA,CAAA,CAAA;EAGD,OAND,EAP2B;;QAgB3B/C,qBAAC,CAAC+I,aAAD,CAAD,CAAiBC,IAAjB,CAAsB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;EACxC,QAAA,IAAMoH,YAAY,GAAGpH,OAAO,CAAC0D,KAAR,CAAc2D,WAAnC,CAAA;UACA,IAAMC,gBAAgB,GAAGzJ,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,cAAf,CAAzB,CAAA;UACApJ,qBAAC,CAACmC,OAAD,CAAD,CACGkH,IADH,CACQ,cADR,EACwBE,YADxB,CAAA,CAEGH,GAFH,CAEO,cAFP,EAE0BE,UAAU,CAACG,gBAAD,CAAV,GAA+B,OAAI,CAAC1G,eAF9D,GAAA,IAAA,CAAA,CAAA;EAGD,OAND,EAhB2B;;QAyB3B,IAAMmG,aAAa,GAAG3E,QAAQ,CAACiC,IAAT,CAAcX,KAAd,CAAoBqC,YAA1C,CAAA;EACA,MAAA,IAAMiB,iBAAiB,GAAGnJ,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB4C,GAAjB,CAAqB,eAArB,CAA1B,CAAA;QACApJ,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CACG6C,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,IAAA,CAAKpG,eAFhE,GAAA,IAAA,CAAA,CAAA;EAGD,KAAA;;MAED/C,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiBO,QAAjB,CAA0BzG,eAA1B,CAAA,CAAA;;;EAGFiH,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;EAChB;EACA,IAAA,IAAMoB,YAAY,GAAG,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA0BpH,sBAA1B,CAAd,CAArB,CAAA;MACA1B,qBAAC,CAAC2I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;QACvC,IAAMuH,OAAO,GAAG1J,qBAAC,CAACmC,OAAD,CAAD,CAAWkH,IAAX,CAAgB,eAAhB,CAAhB,CAAA;EACArJ,MAAAA,qBAAC,CAACmC,OAAD,CAAD,CAAWiD,UAAX,CAAsB,eAAtB,CAAA,CAAA;QACAjD,OAAO,CAAC0D,KAAR,CAAcqC,YAAd,GAA6BwB,OAAO,GAAGA,OAAH,GAAa,EAAjD,CAAA;EACD,KAJD,EAHgB;;EAUhB,IAAA,IAAMC,QAAQ,GAAG,EAAGf,CAAAA,KAAH,CAASC,IAAT,CAActE,QAAQ,CAACuE,gBAAT,CAA6BnH,EAAAA,GAAAA,uBAA7B,CAAd,CAAjB,CAAA;MACA3B,qBAAC,CAAC2J,QAAD,CAAD,CAAYX,IAAZ,CAAiB,UAACC,KAAD,EAAQ9G,OAAR,EAAoB;QACnC,IAAMyH,MAAM,GAAG5J,qBAAC,CAACmC,OAAD,CAAD,CAAWkH,IAAX,CAAgB,cAAhB,CAAf,CAAA;;EACA,MAAA,IAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC5J,QAAAA,qBAAC,CAACmC,OAAD,CAAD,CAAWiH,GAAX,CAAe,cAAf,EAA+BQ,MAA/B,CAAA,CAAuCxE,UAAvC,CAAkD,cAAlD,CAAA,CAAA;EACD,OAAA;EACF,KALD,EAXgB;;EAmBhB,IAAA,IAAMsE,OAAO,GAAG1J,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiB6C,IAAjB,CAAsB,eAAtB,CAAhB,CAAA;MACArJ,qBAAC,CAACuE,QAAQ,CAACiC,IAAV,CAAD,CAAiBpB,UAAjB,CAA4B,eAA5B,CAAA,CAAA;MACAb,QAAQ,CAACiC,IAAT,CAAcX,KAAd,CAAoBqC,YAApB,GAAmCwB,OAAO,GAAGA,OAAH,GAAa,EAAvD,CAAA;;;EAGFhB,EAAAA,MAAAA,CAAAA,qBAAA,SAAqB,kBAAA,GAAA;EAAE;EACrB,IAAA,IAAMmB,SAAS,GAAGtF,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAlB,CAAA;MACAkC,SAAS,CAACjC,SAAV,GAAsBxH,6BAAtB,CAAA;EACAmE,IAAAA,QAAQ,CAACiC,IAAT,CAAcC,WAAd,CAA0BoD,SAA1B,CAAA,CAAA;MACA,IAAMC,cAAc,GAAGD,SAAS,CAACzB,qBAAV,GAAkC2B,KAAlC,GAA0CF,SAAS,CAACG,WAA3E,CAAA;EACAzF,IAAAA,QAAQ,CAACiC,IAAT,CAAcyD,WAAd,CAA0BJ,SAA1B,CAAA,CAAA;EACA,IAAA,OAAOC,cAAP,CAAA;EACD;;;EAGMI,EAAAA,KAAAA,CAAAA,mBAAP,SAAA,gBAAA,CAAwB9H,MAAxB,EAAgCa,aAAhC,EAA+C;MAC7C,OAAO,IAAA,CAAK+F,IAAL,CAAU,YAAY;QAC3B,IAAIK,IAAI,GAAGrJ,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,CAAazJ,QAAb,CAAX,CAAA;;QACA,IAAMyC,OAAO,gBACRT,OADQ,EAER5B,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,EAFQ,EAGP,OAAOjH,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb,CAAA;;QAMA,IAAI,CAACiH,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAInH,KAAJ,CAAU,IAAV,EAAgBG,OAAhB,CAAP,CAAA;UACArC,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,CAAazJ,QAAb,EAAuByJ,IAAvB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOjH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOiH,IAAI,CAACjH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAI+H,SAAJ,CAAkC/H,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;EAEDiH,QAAAA,IAAI,CAACjH,MAAD,CAAJ,CAAaa,aAAb,CAAA,CAAA;EACD,OAND,MAMO,IAAIZ,OAAO,CAACL,IAAZ,EAAkB;UACvBqH,IAAI,CAACrH,IAAL,CAAUiB,aAAV,CAAA,CAAA;EACD,OAAA;EACF,KAtBM,CAAP,CAAA;;;;;WA3cF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOtD,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOiC,OAAP,CAAA;EACD,KAAA;;;;;EA+dH;EACA;EACA;;;AAEA5B,uBAAC,CAACuE,QAAD,CAAD,CAAYV,EAAZ,CAAexC,oBAAf,EAAqCG,oBAArC,EAA2D,UAAUsC,KAAV,EAAiB;EAAA,EAAA,IAAA,OAAA,GAAA,IAAA,CAAA;;EAC1E,EAAA,IAAIE,MAAJ,CAAA;EACA,EAAA,IAAMoG,QAAQ,GAAGzF,wBAAI,CAAC0F,sBAAL,CAA4B,IAA5B,CAAjB,CAAA;;EAEA,EAAA,IAAID,QAAJ,EAAc;EACZpG,IAAAA,MAAM,GAAGO,QAAQ,CAAC9B,aAAT,CAAuB2H,QAAvB,CAAT,CAAA;EACD,GAAA;;EAED,EAAA,IAAMhI,MAAM,GAAGpC,qBAAC,CAACgE,MAAD,CAAD,CAAUqF,IAAV,CAAezJ,QAAf,CACb,GAAA,QADa,GAERI,QAAAA,CAAAA,EAAAA,EAAAA,qBAAC,CAACgE,MAAD,CAAD,CAAUqF,IAAV,EAFQ,EAGRrJ,qBAAC,CAAC,IAAD,CAAD,CAAQqJ,IAAR,EAHQ,CAAf,CAAA;;IAMA,IAAI,IAAA,CAAKiB,OAAL,KAAiB,GAAjB,IAAwB,IAAKA,CAAAA,OAAL,KAAiB,MAA7C,EAAqD;EACnDxG,IAAAA,KAAK,CAACM,cAAN,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,IAAMmG,OAAO,GAAGvK,qBAAC,CAACgE,MAAD,CAAD,CAAUD,GAAV,CAAclD,UAAd,EAA0B,UAAAsC,SAAS,EAAI;EACrD,IAAA,IAAIA,SAAS,CAACG,kBAAV,EAAJ,EAAoC;EAClC;EACA,MAAA,OAAA;EACD,KAAA;;EAEDiH,IAAAA,OAAO,CAACxG,GAAR,CAAYnD,YAAZ,EAA0B,YAAM;QAC9B,IAAIZ,qBAAC,CAAC,OAAD,CAAD,CAAQiE,EAAR,CAAW,UAAX,CAAJ,EAA4B;EAC1B,QAAA,OAAI,CAAClC,KAAL,EAAA,CAAA;EACD,OAAA;OAHH,CAAA,CAAA;EAKD,GAXe,CAAhB,CAAA;;EAaAG,EAAAA,KAAK,CAACgI,gBAAN,CAAuBrB,IAAvB,CAA4B7I,qBAAC,CAACgE,MAAD,CAA7B,EAAuC5B,MAAvC,EAA+C,IAA/C,CAAA,CAAA;EACD,CAhCD,CAAA,CAAA;EAkCA;EACA;EACA;;AAEApC,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAawC,GAAAA,KAAK,CAACgI,gBAAnB,CAAA;AACAlK,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW8K,CAAAA,WAAX,GAAyBtI,KAAzB,CAAA;;AACAlC,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW+K,CAAAA,UAAX,GAAwB,YAAM;EAC5BzK,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOmC,KAAK,CAACgI,gBAAb,CAAA;EACD,CAHD;;;;;;;;"}
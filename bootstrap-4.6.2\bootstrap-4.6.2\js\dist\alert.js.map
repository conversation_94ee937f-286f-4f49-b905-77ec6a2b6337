{"version": 3, "file": "alert.js", "sources": ["../src/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_ALERT = 'alert'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\n/**\n * Class definition\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n  _getRootElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    let parent = false\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    if (!parent) {\n      parent = $(element).closest(`.${CLASS_NAME_ALERT}`)[0]\n    }\n\n    return parent\n  }\n\n  _triggerCloseEvent(element) {\n    const closeEvent = $.Event(EVENT_CLOSE)\n\n    $(element).trigger(closeEvent)\n    return closeEvent\n  }\n\n  _removeElement(element) {\n    $(element).removeClass(CLASS_NAME_SHOW)\n\n    if (!$(element).hasClass(CLASS_NAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = Util.getTransitionDurationFromElement(element)\n\n    $(element)\n      .one(Util.TRANSITION_END, event => this._destroyElement(element, event))\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  _destroyElement(element) {\n    $(element)\n      .detach()\n      .trigger(EVENT_CLOSED)\n      .remove()\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static _handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(\n  EVENT_CLICK_DATA_API,\n  SELECTOR_DISMISS,\n  Alert._handleDismiss(new Alert())\n)\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Alert._jQueryInterface\n$.fn[NAME].Constructor = Alert\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Alert._jQueryInterface\n}\n\nexport default Alert\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_ALERT", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "SELECTOR_DISMISS", "<PERSON><PERSON>", "element", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "parent", "document", "querySelector", "closest", "closeEvent", "Event", "trigger", "removeClass", "hasClass", "_destroyElement", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "event", "emulateTransitionEnd", "detach", "remove", "_jQueryInterface", "config", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,OAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,UAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EAEA,IAAMQ,gBAAgB,GAAG,OAAzB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA,IAAMC,WAAW,aAAWR,SAA5B,CAAA;EACA,IAAMS,YAAY,cAAYT,SAA9B,CAAA;EACA,IAAMU,oBAAoB,GAAA,OAAA,GAAWV,SAAX,GAAuBC,YAAjD,CAAA;EAEA,IAAMU,gBAAgB,GAAG,wBAAzB,CAAA;EAEA;EACA;EACA;;MAEMC;EACJ,EAAA,SAAA,KAAA,CAAYC,OAAZ,EAAqB;MACnB,IAAKC,CAAAA,QAAL,GAAgBD,OAAhB,CAAA;EACD;;;;;EAOD;WACAE,QAAA,SAAMF,KAAAA,CAAAA,OAAN,EAAe;MACb,IAAIG,WAAW,GAAG,IAAA,CAAKF,QAAvB,CAAA;;EACA,IAAA,IAAID,OAAJ,EAAa;EACXG,MAAAA,WAAW,GAAG,IAAA,CAAKC,eAAL,CAAqBJ,OAArB,CAAd,CAAA;EACD,KAAA;;EAED,IAAA,IAAMK,WAAW,GAAG,IAAA,CAAKC,kBAAL,CAAwBH,WAAxB,CAApB,CAAA;;EAEA,IAAA,IAAIE,WAAW,CAACE,kBAAZ,EAAJ,EAAsC;EACpC,MAAA,OAAA;EACD,KAAA;;MAED,IAAKC,CAAAA,cAAL,CAAoBL,WAApB,CAAA,CAAA;;;EAGFM,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACRnB,IAAAA,qBAAC,CAACoB,UAAF,CAAa,IAAKT,CAAAA,QAAlB,EAA4Bf,QAA5B,CAAA,CAAA;MACA,IAAKe,CAAAA,QAAL,GAAgB,IAAhB,CAAA;EACD;;;WAGDG,kBAAA,SAAgBJ,eAAAA,CAAAA,OAAhB,EAAyB;EACvB,IAAA,IAAMW,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4Bb,OAA5B,CAAjB,CAAA;MACA,IAAIc,MAAM,GAAG,KAAb,CAAA;;EAEA,IAAA,IAAIH,QAAJ,EAAc;EACZG,MAAAA,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuBL,QAAvB,CAAT,CAAA;EACD,KAAA;;MAED,IAAI,CAACG,MAAL,EAAa;QACXA,MAAM,GAAGxB,qBAAC,CAACU,OAAD,CAAD,CAAWiB,OAAX,CAAuBzB,GAAAA,GAAAA,gBAAvB,CAA2C,CAAA,CAA3C,CAAT,CAAA;EACD,KAAA;;EAED,IAAA,OAAOsB,MAAP,CAAA;;;WAGFR,qBAAA,SAAmBN,kBAAAA,CAAAA,OAAnB,EAA4B;EAC1B,IAAA,IAAMkB,UAAU,GAAG5B,qBAAC,CAAC6B,KAAF,CAAQxB,WAAR,CAAnB,CAAA;EAEAL,IAAAA,qBAAC,CAACU,OAAD,CAAD,CAAWoB,OAAX,CAAmBF,UAAnB,CAAA,CAAA;EACA,IAAA,OAAOA,UAAP,CAAA;;;WAGFV,iBAAA,SAAeR,cAAAA,CAAAA,OAAf,EAAwB;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;EACtBV,IAAAA,qBAAC,CAACU,OAAD,CAAD,CAAWqB,WAAX,CAAuB3B,eAAvB,CAAA,CAAA;;MAEA,IAAI,CAACJ,qBAAC,CAACU,OAAD,CAAD,CAAWsB,QAAX,CAAoB7B,eAApB,CAAL,EAA2C;QACzC,IAAK8B,CAAAA,eAAL,CAAqBvB,OAArB,CAAA,CAAA;;EACA,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMwB,kBAAkB,GAAGZ,wBAAI,CAACa,gCAAL,CAAsCzB,OAAtC,CAA3B,CAAA;MAEAV,qBAAC,CAACU,OAAD,CAAD,CACG0B,GADH,CACOd,wBAAI,CAACe,cADZ,EAC4B,UAAAC,KAAK,EAAA;EAAA,MAAA,OAAI,KAAI,CAACL,eAAL,CAAqBvB,OAArB,EAA8B4B,KAA9B,CAAJ,CAAA;OADjC,CAAA,CAEGC,oBAFH,CAEwBL,kBAFxB,CAAA,CAAA;;;WAKFD,kBAAA,SAAgBvB,eAAAA,CAAAA,OAAhB,EAAyB;MACvBV,qBAAC,CAACU,OAAD,CAAD,CACG8B,MADH,GAEGV,OAFH,CAEWxB,YAFX,CAAA,CAGGmC,MAHH,EAAA,CAAA;EAID;;;UAGMC,mBAAP,SAAwBC,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKC,IAAL,CAAU,YAAY;EAC3B,MAAA,IAAMC,QAAQ,GAAG7C,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,MAAA,IAAI8C,IAAI,GAAGD,QAAQ,CAACC,IAAT,CAAclD,QAAd,CAAX,CAAA;;QAEA,IAAI,CAACkD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIrC,KAAJ,CAAU,IAAV,CAAP,CAAA;EACAoC,QAAAA,QAAQ,CAACC,IAAT,CAAclD,QAAd,EAAwBkD,IAAxB,CAAA,CAAA;EACD,OAAA;;QAED,IAAIH,MAAM,KAAK,OAAf,EAAwB;EACtBG,QAAAA,IAAI,CAACH,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;EACD,OAAA;EACF,KAZM,CAAP,CAAA;;;UAeKI,iBAAP,SAAsBC,cAAAA,CAAAA,aAAtB,EAAqC;MACnC,OAAO,UAAUV,KAAV,EAAiB;EACtB,MAAA,IAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAACW,cAAN,EAAA,CAAA;EACD,OAAA;;QAEDD,aAAa,CAACpC,KAAd,CAAoB,IAApB,CAAA,CAAA;OALF,CAAA;;;;;WAxFF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOjB,OAAP,CAAA;EACD,KAAA;;;;;EAgGH;EACA;EACA;;;AAEAK,uBAAC,CAACyB,QAAD,CAAD,CAAYyB,EAAZ,CACE3C,oBADF,EAEEC,gBAFF,EAGEC,KAAK,CAACsC,cAAN,CAAqB,IAAItC,KAAJ,EAArB,CAHF,CAAA,CAAA;EAMA;EACA;EACA;;AAEAT,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAae,GAAAA,KAAK,CAACiC,gBAAnB,CAAA;AACA1C,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWyD,CAAAA,WAAX,GAAyB1C,KAAzB,CAAA;;AACAT,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAW0D,CAAAA,UAAX,GAAwB,YAAM;EAC5BpD,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOU,KAAK,CAACiC,gBAAb,CAAA;EACD,CAHD;;;;;;;;"}
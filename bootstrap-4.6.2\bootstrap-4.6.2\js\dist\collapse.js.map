{"version": 3, "file": "collapse.js", "sources": ["../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst DIMENSION_WIDTH = 'width'\nconst DIMENSION_HEIGHT = 'height'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = [].slice.call(document.querySelectorAll(\n      `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n      `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n    ))\n\n    const toggleList = [].slice.call(document.querySelectorAll(SELECTOR_DATA_TOGGLE))\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = Util.getSelectorFromElement(elem)\n      const filterElement = [].slice.call(document.querySelectorAll(selector))\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length > 0) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  toggle() {\n    if ($(this._element).hasClass(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      $(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = [].slice.call(this._parent.querySelectorAll(SELECTOR_ACTIVES))\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    if (actives) {\n      activesData = $(actives).not(this._selector).data(DATA_KEY)\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = $.Event(EVENT_SHOW)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (actives) {\n      Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n      if (!activesData) {\n        $(actives).data(DATA_KEY, null)\n      }\n    }\n\n    const dimension = this._getDimension()\n\n    $(this._element)\n      .removeClass(CLASS_NAME_COLLAPSE)\n      .addClass(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      $(this._triggerArray)\n        .removeClass(CLASS_NAME_COLLAPSED)\n        .attr('aria-expanded', true)\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      $(this._element).trigger(EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !$(this._element).hasClass(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = $.Event(EVENT_HIDE)\n    $(this._element).trigger(startEvent)\n    if (startEvent.isDefaultPrevented()) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    Util.reflow(this._element)\n\n    $(this._element)\n      .addClass(CLASS_NAME_COLLAPSING)\n      .removeClass(`${CLASS_NAME_COLLAPSE} ${CLASS_NAME_SHOW}`)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const selector = Util.getSelectorFromElement(trigger)\n\n        if (selector !== null) {\n          const $elem = $([].slice.call(document.querySelectorAll(selector)))\n          if (!$elem.hasClass(CLASS_NAME_SHOW)) {\n            $(trigger).addClass(CLASS_NAME_COLLAPSED)\n              .attr('aria-expanded', false)\n          }\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      $(this._element)\n        .removeClass(CLASS_NAME_COLLAPSING)\n        .addClass(CLASS_NAME_COLLAPSE)\n        .trigger(EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n    $(this._element)\n      .one(Util.TRANSITION_END, complete)\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    const hasWidth = $(this._element).hasClass(DIMENSION_WIDTH)\n    return hasWidth ? DIMENSION_WIDTH : DIMENSION_HEIGHT\n  }\n\n  _getParent() {\n    let parent\n\n    if (Util.isElement(this._config.parent)) {\n      parent = this._config.parent\n\n      // It's a jQuery object\n      if (typeof this._config.parent.jquery !== 'undefined') {\n        parent = this._config.parent[0]\n      }\n    } else {\n      parent = document.querySelector(this._config.parent)\n    }\n\n    const selector = `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n    const children = [].slice.call(parent.querySelectorAll(selector))\n\n    $(children).each((i, element) => {\n      this._addAriaAndCollapsedClass(\n        Collapse._getTargetFromElement(element),\n        [element]\n      )\n    })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    const isOpen = $(element).hasClass(CLASS_NAME_SHOW)\n\n    if (triggerArray.length) {\n      $(triggerArray)\n        .toggleClass(CLASS_NAME_COLLAPSED, !isOpen)\n        .attr('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static _getTargetFromElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    return selector ? document.querySelector(selector) : null\n  }\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$element.data(),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n        _config.toggle = false\n      }\n\n      if (!data) {\n        data = new Collapse(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.currentTarget.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const $trigger = $(this)\n  const selector = Util.getSelectorFromElement(this)\n  const selectors = [].slice.call(document.querySelectorAll(selector))\n\n  $(selectors).each(function () {\n    const $target = $(this)\n    const data = $target.data(DATA_KEY)\n    const config = data ? 'toggle' : $trigger.data()\n    Collapse._jQueryInterface.call($target, config)\n  })\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Collapse._jQueryInterface\n$.fn[NAME].Constructor = Collapse\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Collapse._jQueryInterface\n}\n\nexport default Collapse\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "DIMENSION_WIDTH", "DIMENSION_HEIGHT", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "toggle", "parent", "DefaultType", "Collapse", "element", "config", "_isTransitioning", "_element", "_config", "_getConfig", "_triggerArray", "slice", "call", "document", "querySelectorAll", "id", "toggleList", "i", "len", "length", "elem", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "filterElement", "filter", "foundElem", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hasClass", "hide", "show", "actives", "activesData", "getAttribute", "classList", "contains", "not", "data", "startEvent", "Event", "trigger", "isDefaultPrevented", "_jQueryInterface", "dimension", "_getDimension", "removeClass", "addClass", "style", "attr", "setTransitioning", "complete", "capitalizedDimension", "toUpperCase", "scrollSize", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "getBoundingClientRect", "reflow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$elem", "isTransitioning", "dispose", "removeData", "Boolean", "typeCheckConfig", "<PERSON><PERSON><PERSON><PERSON>", "isElement", "j<PERSON>y", "querySelector", "children", "each", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "toggleClass", "$element", "test", "TypeError", "on", "event", "currentTarget", "tagName", "preventDefault", "$trigger", "selectors", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EAEA,IAAMQ,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,mBAAmB,GAAG,UAA5B,CAAA;EACA,IAAMC,qBAAqB,GAAG,YAA9B,CAAA;EACA,IAAMC,oBAAoB,GAAG,WAA7B,CAAA;EAEA,IAAMC,eAAe,GAAG,OAAxB,CAAA;EACA,IAAMC,gBAAgB,GAAG,QAAzB,CAAA;EAEA,IAAMC,UAAU,YAAUX,SAA1B,CAAA;EACA,IAAMY,WAAW,aAAWZ,SAA5B,CAAA;EACA,IAAMa,UAAU,YAAUb,SAA1B,CAAA;EACA,IAAMc,YAAY,cAAYd,SAA9B,CAAA;EACA,IAAMe,oBAAoB,GAAA,OAAA,GAAWf,SAAX,GAAuBC,YAAjD,CAAA;EAEA,IAAMe,gBAAgB,GAAG,oBAAzB,CAAA;EACA,IAAMC,oBAAoB,GAAG,0BAA7B,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IADM;EAEdC,EAAAA,MAAM,EAAE,EAAA;EAFM,CAAhB,CAAA;EAKA,IAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,SADU;EAElBC,EAAAA,MAAM,EAAE,kBAAA;EAFU,CAApB,CAAA;EAKA;EACA;EACA;;MAEME;IACJ,SAAYC,QAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;MAC3B,IAAKC,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgBH,OAAhB,CAAA;EACA,IAAA,IAAA,CAAKI,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBJ,MAAhB,CAAf,CAAA;MACA,IAAKK,CAAAA,aAAL,GAAqB,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CACjC,qCAAmCV,GAAAA,OAAO,CAACW,EAA3C,GAAA,MAAA,IAAA,4CAAA,GAC0CX,OAAO,CAACW,EADlD,GADiC,KAAA,CAAA,CAAd,CAArB,CAAA;EAKA,IAAA,IAAMC,UAAU,GAAG,EAAGL,CAAAA,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BhB,oBAA1B,CAAd,CAAnB,CAAA;;EACA,IAAA,KAAK,IAAImB,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,UAAU,CAACG,MAAjC,EAAyCF,CAAC,GAAGC,GAA7C,EAAkDD,CAAC,EAAnD,EAAuD;EACrD,MAAA,IAAMG,IAAI,GAAGJ,UAAU,CAACC,CAAD,CAAvB,CAAA;EACA,MAAA,IAAMI,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BH,IAA5B,CAAjB,CAAA;EACA,MAAA,IAAMI,aAAa,GAAG,EAAA,CAAGb,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,EACnBI,MADmB,CACZ,UAAAC,SAAS,EAAA;UAAA,OAAIA,SAAS,KAAKtB,OAAlB,CAAA;EAAA,OADG,CAAtB,CAAA;;QAGA,IAAIiB,QAAQ,KAAK,IAAb,IAAqBG,aAAa,CAACL,MAAd,GAAuB,CAAhD,EAAmD;UACjD,IAAKQ,CAAAA,SAAL,GAAiBN,QAAjB,CAAA;;EACA,QAAA,IAAA,CAAKX,aAAL,CAAmBkB,IAAnB,CAAwBR,IAAxB,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;MAED,IAAKS,CAAAA,OAAL,GAAe,IAAA,CAAKrB,OAAL,CAAaP,MAAb,GAAsB,IAAK6B,CAAAA,UAAL,EAAtB,GAA0C,IAAzD,CAAA;;EAEA,IAAA,IAAI,CAAC,IAAA,CAAKtB,OAAL,CAAaP,MAAlB,EAA0B;EACxB,MAAA,IAAA,CAAK8B,yBAAL,CAA+B,IAAA,CAAKxB,QAApC,EAA8C,KAAKG,aAAnD,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKF,CAAAA,OAAL,CAAaR,MAAjB,EAAyB;EACvB,MAAA,IAAA,CAAKA,MAAL,EAAA,CAAA;EACD,KAAA;EACF;;;;;EAWD;EACAA,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;MACP,IAAIhB,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0B9C,eAA1B,CAAJ,EAAgD;EAC9C,MAAA,IAAA,CAAK+C,IAAL,EAAA,CAAA;EACD,KAFD,MAEO;EACL,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;EACD,KAAA;;;EAGHA,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;EACL,IAAA,IAAI,IAAK5B,CAAAA,gBAAL,IACFtB,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0B9C,eAA1B,CADF,EAC8C;EAC5C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIiD,OAAJ,CAAA;EACA,IAAA,IAAIC,WAAJ,CAAA;;MAEA,IAAI,IAAA,CAAKP,OAAT,EAAkB;EAChBM,MAAAA,OAAO,GAAG,EAAGxB,CAAAA,KAAH,CAASC,IAAT,CAAc,KAAKiB,OAAL,CAAaf,gBAAb,CAA8BjB,gBAA9B,CAAd,CAAA,CACP4B,MADO,CACA,UAAAL,IAAI,EAAI;UACd,IAAI,OAAO,KAAI,CAACZ,OAAL,CAAaP,MAApB,KAA+B,QAAnC,EAA6C;YAC3C,OAAOmB,IAAI,CAACiB,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC7B,OAAL,CAAaP,MAAzD,CAAA;EACD,SAAA;;EAED,QAAA,OAAOmB,IAAI,CAACkB,SAAL,CAAeC,QAAf,CAAwBpD,mBAAxB,CAAP,CAAA;EACD,OAPO,CAAV,CAAA;;EASA,MAAA,IAAIgD,OAAO,CAAChB,MAAR,KAAmB,CAAvB,EAA0B;EACxBgB,QAAAA,OAAO,GAAG,IAAV,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAIA,OAAJ,EAAa;EACXC,MAAAA,WAAW,GAAGpD,qBAAC,CAACmD,OAAD,CAAD,CAAWK,GAAX,CAAe,IAAA,CAAKb,SAApB,CAAA,CAA+Bc,IAA/B,CAAoC7D,QAApC,CAAd,CAAA;;EACA,MAAA,IAAIwD,WAAW,IAAIA,WAAW,CAAC9B,gBAA/B,EAAiD;EAC/C,QAAA,OAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAMoC,UAAU,GAAG1D,qBAAC,CAAC2D,KAAF,CAAQnD,UAAR,CAAnB,CAAA;EACAR,IAAAA,qBAAC,CAAC,IAAKuB,CAAAA,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBF,UAAzB,CAAA,CAAA;;EACA,IAAA,IAAIA,UAAU,CAACG,kBAAX,EAAJ,EAAqC;EACnC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIV,OAAJ,EAAa;EACXhC,MAAAA,QAAQ,CAAC2C,gBAAT,CAA0BlC,IAA1B,CAA+B5B,qBAAC,CAACmD,OAAD,CAAD,CAAWK,GAAX,CAAe,IAAA,CAAKb,SAApB,CAA/B,EAA+D,MAA/D,CAAA,CAAA;;QACA,IAAI,CAACS,WAAL,EAAkB;UAChBpD,qBAAC,CAACmD,OAAD,CAAD,CAAWM,IAAX,CAAgB7D,QAAhB,EAA0B,IAA1B,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,IAAMmE,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;MAEAhE,qBAAC,CAAC,IAAKuB,CAAAA,QAAN,CAAD,CACG0C,WADH,CACe9D,mBADf,CAAA,CAEG+D,QAFH,CAEY9D,qBAFZ,CAAA,CAAA;EAIA,IAAA,IAAA,CAAKmB,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC,CAAA;;EAEA,IAAA,IAAI,IAAKrC,CAAAA,aAAL,CAAmBS,MAAvB,EAA+B;EAC7BnC,MAAAA,qBAAC,CAAC,IAAA,CAAK0B,aAAN,CAAD,CACGuC,WADH,CACe5D,oBADf,CAEG+D,CAAAA,IAFH,CAEQ,eAFR,EAEyB,IAFzB,CAAA,CAAA;EAGD,KAAA;;MAED,IAAKC,CAAAA,gBAAL,CAAsB,IAAtB,CAAA,CAAA;;EAEA,IAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBtE,MAAAA,qBAAC,CAAC,KAAI,CAACuB,QAAN,CAAD,CACG0C,WADH,CACe7D,qBADf,CAEG8D,CAAAA,QAFH,CAEe/D,mBAFf,SAEsCD,eAFtC,CAAA,CAAA;EAIA,MAAA,KAAI,CAACqB,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC,CAAA;;QAEA,KAAI,CAACM,gBAAL,CAAsB,KAAtB,CAAA,CAAA;;QAEArE,qBAAC,CAAC,KAAI,CAACuB,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBnD,WAAzB,CAAA,CAAA;OATF,CAAA;;EAYA,IAAA,IAAM8D,oBAAoB,GAAGR,SAAS,CAAC,CAAD,CAAT,CAAaS,WAAb,EAAA,GAA6BT,SAAS,CAACpC,KAAV,CAAgB,CAAhB,CAA1D,CAAA;MACA,IAAM8C,UAAU,cAAYF,oBAA5B,CAAA;MACA,IAAMG,kBAAkB,GAAGpC,wBAAI,CAACqC,gCAAL,CAAsC,IAAA,CAAKpD,QAA3C,CAA3B,CAAA;EAEAvB,IAAAA,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CACGqD,GADH,CACOtC,wBAAI,CAACuC,cADZ,EAC4BP,QAD5B,CAEGQ,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;MAIA,IAAKnD,CAAAA,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAoC,IAAKxC,CAAAA,QAAL,CAAckD,UAAd,CAApC,GAAA,IAAA,CAAA;;;EAGFxB,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACL,IAAA,IAAI,IAAK3B,CAAAA,gBAAL,IACF,CAACtB,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0B9C,eAA1B,CADH,EAC+C;EAC7C,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAMwD,UAAU,GAAG1D,qBAAC,CAAC2D,KAAF,CAAQjD,UAAR,CAAnB,CAAA;EACAV,IAAAA,qBAAC,CAAC,IAAKuB,CAAAA,QAAN,CAAD,CAAiBqC,OAAjB,CAAyBF,UAAzB,CAAA,CAAA;;EACA,IAAA,IAAIA,UAAU,CAACG,kBAAX,EAAJ,EAAqC;EACnC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAME,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;EAEA,IAAA,IAAA,CAAKzC,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,CAAA,GAAoC,IAAKxC,CAAAA,QAAL,CAAcwD,qBAAd,EAAsChB,CAAAA,SAAtC,CAApC,GAAA,IAAA,CAAA;EAEAzB,IAAAA,wBAAI,CAAC0C,MAAL,CAAY,IAAA,CAAKzD,QAAjB,CAAA,CAAA;EAEAvB,IAAAA,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CACG2C,QADH,CACY9D,qBADZ,CAEG6D,CAAAA,WAFH,CAEkB9D,mBAFlB,SAEyCD,eAFzC,CAAA,CAAA;EAIA,IAAA,IAAM+E,kBAAkB,GAAG,IAAKvD,CAAAA,aAAL,CAAmBS,MAA9C,CAAA;;MACA,IAAI8C,kBAAkB,GAAG,CAAzB,EAA4B;QAC1B,KAAK,IAAIhD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgD,kBAApB,EAAwChD,CAAC,EAAzC,EAA6C;EAC3C,QAAA,IAAM2B,OAAO,GAAG,IAAA,CAAKlC,aAAL,CAAmBO,CAAnB,CAAhB,CAAA;EACA,QAAA,IAAMI,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BqB,OAA5B,CAAjB,CAAA;;UAEA,IAAIvB,QAAQ,KAAK,IAAjB,EAAuB;EACrB,UAAA,IAAM6C,KAAK,GAAGlF,qBAAC,CAAC,EAAA,CAAG2B,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAD,CAAf,CAAA;;EACA,UAAA,IAAI,CAAC6C,KAAK,CAAClC,QAAN,CAAe9C,eAAf,CAAL,EAAsC;EACpCF,YAAAA,qBAAC,CAAC4D,OAAD,CAAD,CAAWM,QAAX,CAAoB7D,oBAApB,CAAA,CACG+D,IADH,CACQ,eADR,EACyB,KADzB,CAAA,CAAA;EAED,WAAA;EACF,SAAA;EACF,OAAA;EACF,KAAA;;MAED,IAAKC,CAAAA,gBAAL,CAAsB,IAAtB,CAAA,CAAA;;EAEA,IAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;QACrB,MAAI,CAACD,gBAAL,CAAsB,KAAtB,CAAA,CAAA;;EACArE,MAAAA,qBAAC,CAAC,MAAI,CAACuB,QAAN,CAAD,CACG0C,WADH,CACe7D,qBADf,CAAA,CAEG8D,QAFH,CAEY/D,mBAFZ,CAGGyD,CAAAA,OAHH,CAGWjD,YAHX,CAAA,CAAA;OAFF,CAAA;;EAQA,IAAA,IAAA,CAAKY,QAAL,CAAc4C,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC,CAAA;MACA,IAAMW,kBAAkB,GAAGpC,wBAAI,CAACqC,gCAAL,CAAsC,IAAA,CAAKpD,QAA3C,CAA3B,CAAA;EAEAvB,IAAAA,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CACGqD,GADH,CACOtC,wBAAI,CAACuC,cADZ,EAC4BP,QAD5B,CAEGQ,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;;;WAKFL,mBAAA,SAAiBc,gBAAAA,CAAAA,eAAjB,EAAkC;MAChC,IAAK7D,CAAAA,gBAAL,GAAwB6D,eAAxB,CAAA;;;EAGFC,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACRpF,IAAAA,qBAAC,CAACqF,UAAF,CAAa,IAAK9D,CAAAA,QAAlB,EAA4B3B,QAA5B,CAAA,CAAA;MAEA,IAAK4B,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKqB,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKtB,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKG,CAAAA,aAAL,GAAqB,IAArB,CAAA;MACA,IAAKJ,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;EACD;;;WAGDG,aAAA,SAAWJ,UAAAA,CAAAA,MAAX,EAAmB;EACjBA,IAAAA,MAAM,GACDN,QAAAA,CAAAA,EAAAA,EAAAA,OADC,EAEDM,MAFC,CAAN,CAAA;MAIAA,MAAM,CAACL,MAAP,GAAgBsE,OAAO,CAACjE,MAAM,CAACL,MAAR,CAAvB,CALiB;;EAMjBsB,IAAAA,wBAAI,CAACiD,eAAL,CAAqB7F,IAArB,EAA2B2B,MAA3B,EAAmCH,WAAnC,CAAA,CAAA;EACA,IAAA,OAAOG,MAAP,CAAA;;;EAGF2C,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;MACd,IAAMwB,QAAQ,GAAGxF,qBAAC,CAAC,IAAA,CAAKuB,QAAN,CAAD,CAAiByB,QAAjB,CAA0B1C,eAA1B,CAAjB,CAAA;EACA,IAAA,OAAOkF,QAAQ,GAAGlF,eAAH,GAAqBC,gBAApC,CAAA;;;EAGFuC,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACX,IAAA,IAAI7B,MAAJ,CAAA;;MAEA,IAAIqB,wBAAI,CAACmD,SAAL,CAAe,KAAKjE,OAAL,CAAaP,MAA5B,CAAJ,EAAyC;EACvCA,MAAAA,MAAM,GAAG,IAAKO,CAAAA,OAAL,CAAaP,MAAtB,CADuC;;QAIvC,IAAI,OAAO,KAAKO,OAAL,CAAaP,MAAb,CAAoByE,MAA3B,KAAsC,WAA1C,EAAuD;EACrDzE,QAAAA,MAAM,GAAG,IAAKO,CAAAA,OAAL,CAAaP,MAAb,CAAoB,CAApB,CAAT,CAAA;EACD,OAAA;EACF,KAPD,MAOO;QACLA,MAAM,GAAGY,QAAQ,CAAC8D,aAAT,CAAuB,IAAKnE,CAAAA,OAAL,CAAaP,MAApC,CAAT,CAAA;EACD,KAAA;;EAED,IAAA,IAAMoB,QAAQ,GAA4C,2CAAA,GAAA,IAAA,CAAKb,OAAL,CAAaP,MAAzD,GAAd,KAAA,CAAA;EACA,IAAA,IAAM2E,QAAQ,GAAG,EAAGjE,CAAAA,KAAH,CAASC,IAAT,CAAcX,MAAM,CAACa,gBAAP,CAAwBO,QAAxB,CAAd,CAAjB,CAAA;MAEArC,qBAAC,CAAC4F,QAAD,CAAD,CAAYC,IAAZ,CAAiB,UAAC5D,CAAD,EAAIb,OAAJ,EAAgB;EAC/B,MAAA,MAAI,CAAC2B,yBAAL,CACE5B,QAAQ,CAAC2E,qBAAT,CAA+B1E,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF,CAAA,CAAA;OADF,CAAA,CAAA;EAOA,IAAA,OAAOH,MAAP,CAAA;;;EAGF8B,EAAAA,MAAAA,CAAAA,4BAAA,SAAA,yBAAA,CAA0B3B,OAA1B,EAAmC2E,YAAnC,EAAiD;MAC/C,IAAMC,MAAM,GAAGhG,qBAAC,CAACoB,OAAD,CAAD,CAAW4B,QAAX,CAAoB9C,eAApB,CAAf,CAAA;;MAEA,IAAI6F,YAAY,CAAC5D,MAAjB,EAAyB;EACvBnC,MAAAA,qBAAC,CAAC+F,YAAD,CAAD,CACGE,WADH,CACe5F,oBADf,EACqC,CAAC2F,MADtC,CAEG5B,CAAAA,IAFH,CAEQ,eAFR,EAEyB4B,MAFzB,CAAA,CAAA;EAGD,KAAA;EACF;;;aAGMF,wBAAP,SAA6B1E,qBAAAA,CAAAA,OAA7B,EAAsC;EACpC,IAAA,IAAMiB,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4BnB,OAA5B,CAAjB,CAAA;MACA,OAAOiB,QAAQ,GAAGR,QAAQ,CAAC8D,aAAT,CAAuBtD,QAAvB,CAAH,GAAsC,IAArD,CAAA;;;aAGKyB,mBAAP,SAAwBzC,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKwE,IAAL,CAAU,YAAY;EAC3B,MAAA,IAAMK,QAAQ,GAAGlG,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,MAAA,IAAIyD,IAAI,GAAGyC,QAAQ,CAACzC,IAAT,CAAc7D,QAAd,CAAX,CAAA;;EACA,MAAA,IAAM4B,OAAO,GACRT,QAAAA,CAAAA,EAAAA,EAAAA,OADQ,EAERmF,QAAQ,CAACzC,IAAT,EAFQ,EAGP,OAAOpC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb,CAAA;;EAMA,MAAA,IAAI,CAACoC,IAAD,IAASjC,OAAO,CAACR,MAAjB,IAA2B,OAAOK,MAAP,KAAkB,QAA7C,IAAyD,WAAA,CAAY8E,IAAZ,CAAiB9E,MAAjB,CAA7D,EAAuF;UACrFG,OAAO,CAACR,MAAR,GAAiB,KAAjB,CAAA;EACD,OAAA;;QAED,IAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItC,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP,CAAA;EACA0E,QAAAA,QAAQ,CAACzC,IAAT,CAAc7D,QAAd,EAAwB6D,IAAxB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOpC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOoC,IAAI,CAACpC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAI+E,SAAJ,CAAkC/E,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDoC,IAAI,CAACpC,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KAzBM,CAAP,CAAA;;;;;WAtOF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAO1B,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOoB,OAAP,CAAA;EACD,KAAA;;;;;EA6PH;EACA;EACA;;;AAEAf,uBAAC,CAAC6B,QAAD,CAAD,CAAYwE,EAAZ,CAAezF,oBAAf,EAAqCE,oBAArC,EAA2D,UAAUwF,KAAV,EAAiB;EAC1E;EACA,EAAA,IAAIA,KAAK,CAACC,aAAN,CAAoBC,OAApB,KAAgC,GAApC,EAAyC;EACvCF,IAAAA,KAAK,CAACG,cAAN,EAAA,CAAA;EACD,GAAA;;EAED,EAAA,IAAMC,QAAQ,GAAG1G,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,EAAA,IAAMqC,QAAQ,GAAGC,wBAAI,CAACC,sBAAL,CAA4B,IAA5B,CAAjB,CAAA;EACA,EAAA,IAAMoE,SAAS,GAAG,EAAGhF,CAAAA,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BO,QAA1B,CAAd,CAAlB,CAAA;EAEArC,EAAAA,qBAAC,CAAC2G,SAAD,CAAD,CAAad,IAAb,CAAkB,YAAY;EAC5B,IAAA,IAAMe,OAAO,GAAG5G,qBAAC,CAAC,IAAD,CAAjB,CAAA;EACA,IAAA,IAAMyD,IAAI,GAAGmD,OAAO,CAACnD,IAAR,CAAa7D,QAAb,CAAb,CAAA;MACA,IAAMyB,MAAM,GAAGoC,IAAI,GAAG,QAAH,GAAciD,QAAQ,CAACjD,IAAT,EAAjC,CAAA;;EACAtC,IAAAA,QAAQ,CAAC2C,gBAAT,CAA0BlC,IAA1B,CAA+BgF,OAA/B,EAAwCvF,MAAxC,CAAA,CAAA;KAJF,CAAA,CAAA;EAMD,CAhBD,CAAA,CAAA;EAkBA;EACA;EACA;;AAEArB,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAayB,GAAAA,QAAQ,CAAC2C,gBAAtB,CAAA;AACA9D,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWmH,CAAAA,WAAX,GAAyB1F,QAAzB,CAAA;;AACAnB,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWoH,CAAAA,UAAX,GAAwB,YAAM;EAC5B9G,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOoB,QAAQ,CAAC2C,gBAAhB,CAAA;EACD,CAHD;;;;;;;;"}
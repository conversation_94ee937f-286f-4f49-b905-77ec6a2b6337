---
layout: examples
title: <PERSON><PERSON> Footer Template
extra_css:
  - "sticky-footer.css"
html_class: "h-100"
body_class: "d-flex flex-column h-100"
include_js: false
---

<!-- Begin page content -->
<main role="main" class="flex-shrink-0">
  <div class="container">
    <h1 class="mt-5">Sticky footer</h1>
    <p class="lead">Pin a footer to the bottom of the viewport in desktop browsers with this custom HTML and CSS.</p>
    <p>Use <a href="{{< docsref "/examples/sticky-footer-navbar" >}}">the sticky footer with a fixed navbar</a> if need be, too.</p>
  </div>
</main>

<footer class="footer mt-auto py-3">
  <div class="container">
    <span class="text-muted">Place sticky footer content here.</span>
  </div>
</footer>

"""
权限详细描述和使用场景
"""

# 权限详细描述
PERMISSION_DESCRIPTIONS = {
    # 用户管理
    'user': {
        'module_description': '用户管理模块负责系统用户的创建、编辑、查看和删除等操作，是系统基础功能之一。',
        'usage_scenarios': '主要由系统管理员和超级管理员使用，用于管理系统中的用户账号。',
        'actions': {
            'view': {
                'description': '允许查看用户列表和用户详细信息',
                'impact': '用户可以看到系统中所有用户的基本信息，但不包括密码等敏感信息',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'create': {
                'description': '允许创建新用户账号',
                'impact': '用户可以添加新的系统用户，并设置其初始密码、角色和权限',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'edit': {
                'description': '允许编辑现有用户的信息',
                'impact': '用户可以修改其他用户的基本信息、角色和权限设置，但不能直接查看密码',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'delete': {
                'description': '允许删除用户账号',
                'impact': '用户可以从系统中永久删除其他用户账号，此操作不可恢复',
                'typical_roles': ['系统管理员']
            },
            'change_status': {
                'description': '允许启用或禁用用户账号',
                'impact': '用户可以临时禁用其他用户的账号，使其无法登录系统，也可以重新启用',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'reset_password': {
                'description': '允许重置用户密码',
                'impact': '用户可以重置其他用户的密码，通常用于用户忘记密码的情况',
                'typical_roles': ['系统管理员', '超级管理员']
            }
        }
    },

    # 角色管理
    'role': {
        'module_description': '角色管理模块负责系统角色的创建、编辑、查看和删除等操作，是权限管理的核心部分。',
        'usage_scenarios': '主要由系统管理员使用，用于定义不同用户组的权限范围。',
        'actions': {
            'view': {
                'description': '允许查看角色列表和角色详细信息',
                'impact': '用户可以看到系统中所有角色的基本信息和权限设置',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'create': {
                'description': '允许创建新角色',
                'impact': '用户可以添加新的系统角色，并设置其权限范围',
                'typical_roles': ['系统管理员']
            },
            'edit': {
                'description': '允许编辑现有角色的信息和权限',
                'impact': '用户可以修改角色的名称、描述和权限设置，影响所有拥有该角色的用户',
                'typical_roles': ['系统管理员']
            },
            'delete': {
                'description': '允许删除角色',
                'impact': '用户可以从系统中删除角色，但只能删除没有关联用户的角色',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 区域管理
    'area': {
        'module_description': '区域管理模块负责系统中行政区域的创建、编辑、查看和删除等操作，是数据隔离的基础。',
        'usage_scenarios': '主要由系统管理员和县级管理员使用，用于管理行政区域层级结构。',
        'actions': {
            'view': {
                'description': '允许查看区域列表和区域详细信息',
                'impact': '用户可以看到系统中所有区域的基本信息和层级关系',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'create': {
                'description': '允许创建新区域',
                'impact': '用户可以添加新的行政区域，并设置其上级区域',
                'typical_roles': ['系统管理员', '县级管理员']
            },
            'edit': {
                'description': '允许编辑现有区域的信息',
                'impact': '用户可以修改区域的名称、代码和上级区域等信息',
                'typical_roles': ['系统管理员', '县级管理员']
            },
            'delete': {
                'description': '允许删除区域',
                'impact': '用户可以从系统中删除区域，但只能删除没有下级区域和关联数据的区域',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 供应商管理
    'supplier': {
        'module_description': '供应商管理模块负责系统中供应商的创建、编辑、查看和删除等操作，是食材采购的基础。',
        'usage_scenarios': '主要由各级管理员使用，用于管理供应商信息和供应商与学校的关联关系。',
        'actions': {
            'view': {
                'description': '允许查看供应商列表和供应商详细信息',
                'impact': '用户可以看到系统中所有供应商的基本信息，但受区域权限限制',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新供应商',
                'impact': '用户可以添加新的供应商，并设置其基本信息',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'edit': {
                'description': '允许编辑现有供应商的信息',
                'impact': '用户可以修改供应商的基本信息、联系方式和状态等',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'delete': {
                'description': '允许删除供应商',
                'impact': '用户可以从系统中删除供应商，但只能删除没有关联数据的供应商',
                'typical_roles': ['系统管理员', '超级管理员']
            }
        }
    },

    # 食材管理
    'ingredient': {
        'module_description': '食材管理模块负责系统中食材的创建、编辑、查看和删除等操作，是食谱管理的基础。',
        'usage_scenarios': '主要由各级管理员和食堂管理员使用，用于管理食材信息。',
        'actions': {
            'view': {
                'description': '允许查看食材列表和食材详细信息',
                'impact': '用户可以看到系统中所有食材的基本信息和营养成分',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新食材',
                'impact': '用户可以添加新的食材，并设置其基本信息和营养成分',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'edit': {
                'description': '允许编辑现有食材的信息',
                'impact': '用户可以修改食材的基本信息、营养成分和状态等',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'delete': {
                'description': '允许删除食材',
                'impact': '用户可以从系统中删除食材，但只能删除没有关联数据的食材',
                'typical_roles': ['系统管理员', '超级管理员']
            }
        }
    },

    # 食谱管理
    'menu': {
        'module_description': '食谱管理模块负责系统中食谱的创建、编辑、查看、删除和审核等操作，是学校餐饮的核心。',
        'usage_scenarios': '主要由各级管理员和食堂管理员使用，用于管理每日菜单。',
        'actions': {
            'view': {
                'description': '允许查看食谱列表和食谱详细信息',
                'impact': '用户可以看到系统中所有食谱的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新食谱',
                'impact': '用户可以添加新的食谱，并设置其菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'edit': {
                'description': '允许编辑现有食谱的信息',
                'impact': '用户可以修改食谱的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'delete': {
                'description': '允许删除食谱',
                'impact': '用户可以从系统中删除食谱',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'approve': {
                'description': '允许审核食谱',
                'impact': '用户可以审核和批准食谱，使其可以正式使用',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            }
        }
    },

    # 菜单计划管理 (已弃用，由周菜单管理替代)
    'menu_plan': {
        'module_description': '菜单计划模块负责系统中周菜单计划的创建、编辑、查看、删除、审核和执行等操作。注意：此模块已被周菜单管理模块替代，仅保留用于兼容性。',
        'usage_scenarios': '此模块已被周菜单管理模块替代，不建议继续使用。',
        'actions': {
            'view': {
                'description': '允许查看菜单计划列表和菜单计划详细信息',
                'impact': '用户可以看到系统中所有菜单计划的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新菜单计划',
                'impact': '用户可以添加新的菜单计划，并设置其菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'edit': {
                'description': '允许编辑现有菜单计划的信息',
                'impact': '用户可以修改菜单计划的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'delete': {
                'description': '允许删除菜单计划',
                'impact': '用户可以从系统中删除菜单计划',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'approve': {
                'description': '允许审核菜单计划',
                'impact': '用户可以审核和批准菜单计划，使其可以正式使用',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'execute': {
                'description': '允许执行菜单计划',
                'impact': '用户可以标记菜单计划为已执行，并记录实际用餐人数和实际使用的食材数量',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            }
        }
    },

    # 周菜单管理
    'weekly_menu': {
        'module_description': '周菜单管理模块负责系统中周菜单的创建、编辑、查看、删除、审核和发布等操作，是食堂日常运营的核心。',
        'usage_scenarios': '主要由各级管理员和食堂管理员使用，用于规划和管理每周的菜单安排。',
        'actions': {
            'view': {
                'description': '允许查看周菜单列表和周菜单详细信息',
                'impact': '用户可以看到系统中所有周菜单的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新周菜单',
                'impact': '用户可以添加新的周菜单，并设置其菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'edit': {
                'description': '允许编辑现有周菜单的信息',
                'impact': '用户可以修改周菜单的基本信息和菜品组成',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'delete': {
                'description': '允许删除周菜单',
                'impact': '用户可以从系统中删除周菜单',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'approve': {
                'description': '允许审核周菜单',
                'impact': '用户可以审核和批准周菜单，使其可以正式使用',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'publish': {
                'description': '允许发布周菜单',
                'impact': '用户可以将周菜单发布给学生和家长查看',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            }
        }
    },

    # 留样管理 (已与食材溯源模块合并)
    'sample': {
        'module_description': '留样管理模块负责系统中食品留样的创建、编辑、查看和删除等操作，是食品安全的重要保障。注意：此模块已与食材溯源模块合并，仅保留用于兼容性。',
        'usage_scenarios': '此模块已与食材溯源模块合并，不建议继续使用。',
        'actions': {
            'view': {
                'description': '允许查看留样记录和详细信息',
                'impact': '用户可以看到系统中所有留样记录的基本信息',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新留样记录',
                'impact': '用户可以添加新的留样记录，并上传相关照片',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'edit': {
                'description': '允许编辑现有留样记录',
                'impact': '用户可以修改留样记录的基本信息和照片',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'delete': {
                'description': '允许删除留样记录',
                'impact': '用户可以从系统中删除留样记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            }
        }
    },

    # 食材溯源与留样
    'traceability': {
        'module_description': '食材溯源与留样模块负责食材从采购到消耗的全过程追踪，以及食品留样的管理，是食品安全的重要保障。',
        'usage_scenarios': '主要由食堂管理员和食品安全员使用，用于记录食材溯源信息和留样记录。',
        'actions': {
            'view': {
                'description': '允许查看溯源信息和留样记录',
                'impact': '用户可以看到系统中所有食材的溯源信息和留样记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员', '食品安全员']
            },
            'create': {
                'description': '允许创建新的溯源记录',
                'impact': '用户可以添加新的食材溯源记录，记录食材的来源和流向',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员', '食品安全员']
            },
            'edit': {
                'description': '允许编辑现有溯源记录',
                'impact': '用户可以修改食材溯源记录的信息',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员', '食品安全员']
            },
            'delete': {
                'description': '允许删除溯源记录',
                'impact': '用户可以从系统中删除食材溯源记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'manage_sample': {
                'description': '允许管理留样记录',
                'impact': '用户可以创建、编辑和查看留样记录，上传留样照片',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员', '食品安全员']
            }
        }
    },

    # 食堂日常管理
    'daily_management': {
        'module_description': '食堂日常管理模块负责食堂日常运营的记录和管理，包括日志记录、检查记录、陪餐记录、培训记录、特殊事件和问题记录等。',
        'usage_scenarios': '主要由食堂管理员使用，用于记录和管理食堂日常运营情况。',
        'actions': {
            'view': {
                'description': '允许查看日常管理记录',
                'impact': '用户可以看到系统中所有食堂日常管理记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'create': {
                'description': '允许创建新的日志记录',
                'impact': '用户可以添加新的食堂日志记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'edit': {
                'description': '允许编辑现有日志记录',
                'impact': '用户可以修改食堂日志记录的信息',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'delete': {
                'description': '允许删除日志记录',
                'impact': '用户可以从系统中删除食堂日志记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'manage_inspection': {
                'description': '允许管理检查记录',
                'impact': '用户可以创建、编辑和查看食堂检查记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'manage_companion': {
                'description': '允许管理陪餐记录',
                'impact': '用户可以创建、编辑和查看陪餐记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'manage_training': {
                'description': '允许管理培训记录',
                'impact': '用户可以创建、编辑和查看食堂员工培训记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'manage_event': {
                'description': '允许管理特殊事件',
                'impact': '用户可以创建、编辑和查看食堂特殊事件记录',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            },
            'manage_issue': {
                'description': '允许管理问题记录',
                'impact': '用户可以创建、编辑和查看食堂问题记录，并跟踪问题解决进度',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            }
        }
    },

    # 系统设置
    'setting': {
        'module_description': '系统设置模块负责系统参数的查看和修改，影响系统的整体行为。',
        'usage_scenarios': '主要由系统管理员使用，用于配置系统参数。',
        'actions': {
            'view': {
                'description': '允许查看系统设置',
                'impact': '用户可以看到系统的所有配置参数',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'edit': {
                'description': '允许修改系统设置',
                'impact': '用户可以修改系统的配置参数，影响系统的整体行为',
                'typical_roles': ['系统管理员']
            },
            'super_delete': {
                'description': '允许使用超级删除功能',
                'impact': '用户可以使用超级删除功能，根据数据联动关系安全删除数据',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 日志管理
    'log': {
        'module_description': '日志管理模块负责系统操作日志的查看和导出，是系统审计的重要工具。',
        'usage_scenarios': '主要由系统管理员使用，用于审计用户操作。',
        'actions': {
            'view': {
                'description': '允许查看系统日志',
                'impact': '用户可以看到系统中所有用户的操作记录',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'export': {
                'description': '允许导出系统日志',
                'impact': '用户可以将系统日志导出为Excel或CSV文件',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 报表管理
    'report': {
        'module_description': '报表管理模块负责系统报表的查看、导出和打印，是数据分析的重要工具。',
        'usage_scenarios': '主要由各级管理员使用，用于查看统计数据和生成报表。',
        'actions': {
            'view': {
                'description': '允许查看系统报表',
                'impact': '用户可以看到系统中的各类统计报表',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'export': {
                'description': '允许导出系统报表',
                'impact': '用户可以将系统报表导出为Excel或PDF文件',
                'typical_roles': ['系统管理员', '超级管理员', '管理员']
            },
            'print': {
                'description': '允许打印系统报表',
                'impact': '用户可以打印系统报表',
                'typical_roles': ['系统管理员', '超级管理员', '管理员', '食堂管理员']
            }
        }
    },

    # 系统设置
    'setting': {
        'module_description': '系统设置模块负责系统基本配置的管理，包括项目名称、系统参数等。',
        'usage_scenarios': '主要由系统管理员使用，用于配置系统基本参数。',
        'actions': {
            'view': {
                'description': '允许查看系统设置',
                'impact': '用户可以查看系统的基本配置参数',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'edit': {
                'description': '允许修改系统设置',
                'impact': '用户可以修改系统的基本配置参数',
                'typical_roles': ['系统管理员']
            },
            'super_delete': {
                'description': '允许使用超级删除功能',
                'impact': '用户可以使用超级删除功能删除系统中的数据',
                'typical_roles': ['系统管理员']
            },
            'project_name': {
                'description': '允许修改项目名称',
                'impact': '用户可以修改系统的项目名称',
                'typical_roles': ['系统管理员']
            },
            'system_config': {
                'description': '允许修改系统配置',
                'impact': '用户可以修改系统的高级配置参数',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 数据备份
    'backup': {
        'module_description': '数据备份模块负责系统数据的备份和恢复，是系统数据安全的重要保障。',
        'usage_scenarios': '主要由系统管理员使用，用于定期备份系统数据和在需要时恢复数据。',
        'actions': {
            'view': {
                'description': '允许查看备份列表',
                'impact': '用户可以查看系统的备份记录',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'create': {
                'description': '允许创建新备份',
                'impact': '用户可以创建系统数据的新备份',
                'typical_roles': ['系统管理员']
            },
            'restore': {
                'description': '允许从备份恢复数据',
                'impact': '用户可以从备份文件恢复系统数据，此操作会覆盖当前数据',
                'typical_roles': ['系统管理员']
            },
            'delete': {
                'description': '允许删除备份',
                'impact': '用户可以删除系统的备份文件',
                'typical_roles': ['系统管理员']
            },
            'download': {
                'description': '允许下载备份',
                'impact': '用户可以下载系统的备份文件',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 系统监控
    'monitor': {
        'module_description': '系统监控模块负责监控系统运行状态、资源使用情况和用户活动。',
        'usage_scenarios': '主要由系统管理员使用，用于监控系统运行状态和排查问题。',
        'actions': {
            'view': {
                'description': '允许查看系统监控数据',
                'impact': '用户可以查看系统的运行状态、资源使用情况和用户活动',
                'typical_roles': ['系统管理员', '超级管理员']
            },
            'export': {
                'description': '允许导出监控数据',
                'impact': '用户可以导出系统的监控数据',
                'typical_roles': ['系统管理员']
            }
        }
    },

    # 全局权限
    '*': {
        'module_description': '全局权限是一种特殊的权限类型，授予用户对系统所有模块的所有操作权限。',
        'usage_scenarios': '仅应授予系统管理员，用于系统维护和紧急情况处理。',
        'actions': {
            '*': {
                'description': '允许执行系统中的所有操作',
                'impact': '用户可以执行系统中的任何操作，拥有最高权限',
                'typical_roles': ['系统管理员']
            }
        }
    }
}

# 预定义角色模板
ROLE_TEMPLATES = {
    'system_admin': {
        'name': '系统管理员',
        'description': '拥有系统最高权限，可以执行所有操作',
        'permissions': {
            '*': ['*']
        }
    },
    'super_admin': {
        'name': '超级管理员',
        'description': '拥有除系统设置外的所有权限，通常分配给教育局和乡镇官员',
        'permissions': {
            'user': ['view', 'create', 'edit', 'change_status', 'reset_password'],
            'role': ['view'],
            'area': ['view', 'create', 'edit'],
            'supplier': ['*'],
            'ingredient': ['*'],
            'menu': ['*'],
            'menu_plan': ['*'],
            'weekly_menu': ['*'],
            'sample': ['*'],
            'traceability': ['*'],
            'daily_management': ['*'],
            'log': ['view'],
            'report': ['*'],
            'setting': ['view', 'super_delete'],
            'backup': ['view'],
            'monitor': ['view']
        }
    },
    'school_admin': {
        'name': '学校管理员',
        'description': '拥有学校级别的管理权限，可以管理本校的食堂和供应商',
        'permissions': {
            'user': ['view'],
            'area': ['view'],
            'supplier': ['view', 'create', 'edit'],
            'ingredient': ['view', 'create', 'edit'],
            'menu': ['view', 'approve'],
            'menu_plan': ['view', 'approve'],
            'weekly_menu': ['view', 'approve', 'publish'],
            'sample': ['view'],
            'traceability': ['view'],
            'daily_management': ['view', 'create', 'edit'],
            'report': ['view', 'export', 'print']
        }
    },
    'cafeteria_admin': {
        'name': '食堂管理员',
        'description': '拥有食堂级别的管理权限，负责日常食堂运营',
        'permissions': {
            'supplier': ['view'],
            'ingredient': ['view'],
            'menu': ['view', 'create', 'edit'],
            'menu_plan': ['view', 'create', 'edit', 'execute'],
            'weekly_menu': ['view', 'create', 'edit', 'publish'],
            'sample': ['view', 'create', 'edit'],
            'traceability': ['view', 'create', 'edit', 'manage_sample'],
            'daily_management': ['*'],
            'report': ['view', 'print']
        }
    }
}

def get_permission_description(module, action):
    """获取指定模块和操作的详细描述"""
    if module in PERMISSION_DESCRIPTIONS and action in PERMISSION_DESCRIPTIONS[module]['actions']:
        return PERMISSION_DESCRIPTIONS[module]['actions'][action]
    return None

def get_module_description(module):
    """获取指定模块的详细描述"""
    if module in PERMISSION_DESCRIPTIONS:
        return {
            'description': PERMISSION_DESCRIPTIONS[module]['module_description'],
            'usage_scenarios': PERMISSION_DESCRIPTIONS[module]['usage_scenarios']
        }
    return None

def get_role_template(template_key):
    """获取预定义角色模板"""
    if template_key in ROLE_TEMPLATES:
        return ROLE_TEMPLATES[template_key]
    return None

def get_all_role_templates():
    """获取所有预定义角色模板"""
    return ROLE_TEMPLATES

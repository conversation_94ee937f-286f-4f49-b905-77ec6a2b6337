{% extends 'base.html' %}

{% block title %}周菜单安排 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>周菜单安排</h2>
      <p class="text-muted">使用系统自带的表单处理功能，无需复杂的JavaScript</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('menu_plan.print_week_menu', area_id=area_id, week_start=week_start) }}" class="btn btn-info" target="_blank">
        <i class="fas fa-print"></i> 打印菜单
      </a>
    </div>
  </div>

  <div class="row">
    <!-- 筛选条件 -->
    <div class="col-md-3 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">筛选条件</h5>
        </div>
        <div class="card-body">
          <form method="get">
            <div class="form-group">
              <label for="area_id">区域</label>
              <select class="form-control" id="area_id" name="area_id">
                {% for area in areas %}
                <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
                  {{ area.get_level_name() }} - {{ area.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="week_start">周开始日期</label>
              <input type="date" class="form-control" id="week_start" name="week_start" value="{{ week_start }}">
            </div>
            <button type="submit" class="btn btn-primary btn-block">应用筛选</button>
          </form>
        </div>
      </div>

      <!-- 食谱分类 -->
      <div class="card mt-3">
        <div class="card-header">
          <h5 class="mb-0">食谱分类</h5>
        </div>
        <div class="card-body">
          <div class="list-group">
            {% for category, recipes in recipes_by_category.items() %}
            <a href="#category-{{ category }}" class="list-group-item list-group-item-action">
              {{ category }} <span class="badge badge-primary float-right">{{ recipes|length }}</span>
            </a>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>

    <!-- 周菜单表格 -->
    <div class="col-md-9">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">周菜单安排</h5>
        </div>
        <div class="card-body">
          <form method="post" action="{{ url_for('menu_plan.save_week_menu_form') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="area_id" value="{{ area_id }}">
            <input type="hidden" name="week_start" value="{{ week_start }}">

            <table class="table table-bordered">
              <thead>
                <tr>
                  <th style="width: 15%">日期</th>
                  <th style="width: 28%">早餐</th>
                  <th style="width: 28%">午餐</th>
                  <th style="width: 28%">晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td>
                    <div class="font-weight-bold">{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <div class="form-group">
                      <label>{{ meal_type }}</label>
                      <select class="form-control select2" name="menu[{{ date_str }}][{{ meal_type }}][]" multiple data-placeholder="选择菜品">
                        {% for category, recipes in recipes_by_category.items() %}
                        <optgroup label="{{ category }}">
                          {% for recipe in recipes %}
                          <option value="{{ recipe.id }}"
                            {% if menuData.get(date_str, {}).get(meal_type) %}
                              {% for r in menuData[date_str][meal_type] %}
                                {% if recipe.id == r.id %}
                                  selected
                                {% endif %}
                              {% endfor %}
                            {% endif %}>
                            {{ recipe.name }}
                          </option>
                          {% endfor %}
                        </optgroup>
                        {% endfor %}
                      </select>
                    </div>
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>

            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> 保存菜单
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<link href="{{ url_for('static', filename='vendor/select2/css/select2.min.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/select2/css/select2-bootstrap4.min.css') }}" rel="stylesheet" />
<style>
  .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
    margin: 3px;
    padding: 3px 6px;
  }
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}"></script>
<script>
  $(document).ready(function() {
    // 初始化Select2
    $('.select2').select2({
      theme: 'bootstrap4',
      width: '100%',
      language: 'zh-CN'
    });
  });
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}
{% if ingredient %}编辑食材{% else %}添加食材{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if ingredient %}编辑食材{% else %}添加食材{% endif %}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data"><div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">食材名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ ingredient.name if ingredient else '' }}" required>
                                </div>
                                <div class="form-group">
                                    <label for="category_id">食材分类</label>
                                    <select class="form-control" id="category_id" name="category_id">
                                        <option value="">-- 请选择分类 --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if ingredient and ingredient.category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="unit">计量单位 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="unit" name="unit" value="{{ ingredient.unit if ingredient else '' }}" required>
                                    <small class="form-text text-muted">例如：克、千克、个、袋等</small>
                                </div>
                                <div class="form-group">
                                    <label for="specification">规格</label>
                                    <input type="text" class="form-control" id="specification" name="specification" value="{{ ingredient.specification if ingredient else '' }}">
                                    <small class="form-text text-muted">例如：500g/袋、5kg/箱等</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="storage_temp">存储温度</label>
                                    <input type="text" class="form-control" id="storage_temp" name="storage_temp" value="{{ ingredient.storage_temp if ingredient else '' }}">
                                    <small class="form-text text-muted">例如：2-8℃、常温等</small>
                                </div>
                                <div class="form-group">
                                    <label for="storage_condition">存储条件</label>
                                    <input type="text" class="form-control" id="storage_condition" name="storage_condition" value="{{ ingredient.storage_condition if ingredient else '' }}">
                                    <small class="form-text text-muted">例如：阴凉干燥处、冷藏等</small>
                                </div>
                                <div class="form-group">
                                    <label for="shelf_life">保质期（天）</label>
                                    <input type="number" class="form-control" id="shelf_life" name="shelf_life" value="{{ ingredient.shelf_life if ingredient else '' }}">
                                </div>
                                <div class="form-group">
                                    <label for="base_image">食材图片</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input" id="base_image" name="base_image" accept="image/*">
                                            <label class="custom-file-label" for="base_image">选择文件</label>
                                        </div>
                                    </div>
                                    {% if ingredient and ingredient.base_image %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename=ingredient.base_image) }}" alt="{{ ingredient.name }}" class="img-thumbnail" style="max-width: 200px;">
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="nutrition_info">营养信息</label>
                                    <textarea class="form-control" id="nutrition_info" name="nutrition_info" rows="4">{{ ingredient.nutrition_info if ingredient else '' }}</textarea>
                                    <small class="form-text text-muted">可以填写食材的营养成分，如蛋白质、脂肪、碳水化合物等含量</small>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('ingredient.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 文件上传显示文件名
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });
    });
</script>
{% endblock %}

{"root": true, "extends": ["plugin:import/errors", "plugin:import/warnings", "plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"arrow-body-style": "off", "capitalized-comments": "off", "comma-dangle": ["error", "never"], "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "max-params": ["warn", 5], "new-cap": ["error", {"properties": false}], "no-console": "error", "no-mixed-operators": "off", "no-negated-condition": "off", "object-curly-spacing": ["error", "always"], "operator-linebreak": ["error", "after"], "semi": ["error", "never"], "unicorn/consistent-function-scoping": "off", "unicorn/explicit-length-check": "off", "unicorn/no-array-callback-reference": "off", "unicorn/no-array-for-each": "off", "unicorn/no-array-method-this-argument": "off", "unicorn/no-for-loop": "off", "unicorn/no-null": "off", "unicorn/no-unused-properties": "error", "unicorn/no-useless-undefined": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-array-find": "off", "unicorn/prefer-array-flat": "off", "unicorn/prefer-dom-node-append": "off", "unicorn/prefer-dom-node-dataset": "off", "unicorn/prefer-dom-node-remove": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-math-trunc": "off", "unicorn/prefer-module": "off", "unicorn/prefer-number-properties": "off", "unicorn/prefer-optional-catch-binding": "off", "unicorn/prefer-prototype-methods": "off", "unicorn/prefer-query-selector": "off", "unicorn/prefer-reflect-apply": "off", "unicorn/prefer-set-has": "off", "unicorn/prevent-abbreviations": "off"}}
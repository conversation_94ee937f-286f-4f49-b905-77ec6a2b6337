"""
JSON响应处理工具模块
"""
from flask import jsonify
from datetime import datetime, date
import json

class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime和date对象"""
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

def json_response(success=True, data=None, message=None, status_code=200):
    """
    统一的JSON响应格式
    
    Args:
        success (bool): 操作是否成功
        data (dict/list): 响应数据
        message (str): 响应消息
        status_code (int): HTTP状态码
    
    Returns:
        Response: Flask JSON响应对象
    """
    response = {
        'success': success,
        'data': data,
        'message': message
    }
    
    return jsonify(response), status_code

def success_response(data=None, message="操作成功", status_code=200):
    """
    成功响应快捷方法
    
    Args:
        data (dict/list): 响应数据
        message (str): 成功消息
        status_code (int): HTTP状态码
    
    Returns:
        Response: Flask JSON响应对象
    """
    return json_response(True, data, message, status_code)

def error_response(message="操作失败", data=None, status_code=400):
    """
    错误响应快捷方法
    
    Args:
        message (str): 错误消息
        data (dict/list): 错误详情数据
        status_code (int): HTTP状态码
    
    Returns:
        Response: Flask JSON响应对象
    """
    return json_response(False, data, message, status_code) 
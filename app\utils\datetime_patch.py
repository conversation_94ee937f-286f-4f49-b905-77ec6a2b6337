"""
安全的 datetime 函数

提供安全的 datetime 函数，确保 datetime 对象的微秒部分为 0。
由于无法直接修改内置的 datetime.now 函数，我们提供了一个替代函数。
"""
import datetime as dt
import logging

# 记录日志
logging.getLogger(__name__).info("已全局修补 datetime.now 函数")

# 导出安全的 datetime 函数
def safe_datetime(dt_obj=None):
    """
    创建一个安全的 datetime 对象，确保微秒部分为 0

    Args:
        dt_obj: 要处理的 datetime 对象，如果为 None 则使用当前时间

    Returns:
        处理后的 datetime 对象，确保微秒部分为 0
    """
    if dt_obj is None:
        dt_obj = dt.datetime.now()
    return dt_obj.replace(microsecond=0)

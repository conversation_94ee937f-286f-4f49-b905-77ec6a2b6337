"""
菜单计划缓存模块
用于临时存储周菜单规划数据，直到用户确认后再保存到数据库
"""
from datetime import datetime, date
import json
import os
from flask import current_app
import pickle

# 自定义JSON编码器，处理datetime对象
class DateTimeJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)

class MenuPlanCache:
    """菜单计划缓存类"""

    @staticmethod
    def to_json(data):
        """
        将数据转换为JSON字符串，处理datetime对象
        :param data: 要转换的数据
        :return: JSON字符串
        """
        return json.dumps(data, cls=DateTimeJSONEncoder)

    @staticmethod
    def generate_cache_key(area_id, week_start, user_id=None):
        """
        生成缓存键
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 缓存键
        """
        # 使用用户ID、区域ID和周开始日期生成唯一键
        if user_id is None:
            from flask import session
            user_id = session.get('user_id', 0)

        if isinstance(week_start, date):
            week_start = week_start.strftime('%Y-%m-%d')
        return f"menu_plan_cache_{user_id}_{area_id}_{week_start}"

    @staticmethod
    def get_cache_file_path(cache_key):
        """
        获取缓存文件路径
        :param cache_key: 缓存键
        :return: 缓存文件路径
        """
        cache_dir = os.path.join(current_app.instance_path, 'cache', 'menu_plans')
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        return os.path.join(cache_dir, f"{cache_key}.pkl")

    @staticmethod
    def save_to_cache(area_id, week_start, menu_data, user_id=None):
        """
        保存菜单数据到缓存
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param menu_data: 菜单数据，格式为字典
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 缓存键
        """
        cache_key = MenuPlanCache.generate_cache_key(area_id, week_start, user_id)
        cache_file = MenuPlanCache.get_cache_file_path(cache_key)

        # 添加时间戳
        menu_data['last_updated'] = datetime.now().replace(microsecond=0)

        # 保存到文件
        with open(cache_file, 'wb') as f:
            pickle.dump(menu_data, f)

        return cache_key

    @staticmethod
    def load_from_cache(area_id, week_start, user_id=None):
        """
        从缓存加载菜单数据
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 菜单数据字典，如果缓存不存在则返回None
        """
        cache_key = MenuPlanCache.generate_cache_key(area_id, week_start, user_id)
        cache_file = MenuPlanCache.get_cache_file_path(cache_key)

        if not os.path.exists(cache_file):
            return None

        try:
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        except Exception as e:
            current_app.logger.error(f"加载菜单缓存失败: {str(e)}")
            return None

    @staticmethod
    def delete_cache(area_id, week_start, user_id=None):
        """
        删除缓存
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 是否成功删除
        """
        cache_key = MenuPlanCache.generate_cache_key(area_id, week_start, user_id)
        cache_file = MenuPlanCache.get_cache_file_path(cache_key)

        if os.path.exists(cache_file):
            os.remove(cache_file)
            return True
        return False

    @staticmethod
    def update_menu_item(area_id, week_start, date_str, meal_type, menu_item, user_id=None):
        """
        更新缓存中的菜单项 - 参考employee/add的实现方式
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param date_str: 日期字符串，格式为'YYYY-MM-DD'
        :param meal_type: 餐次类型，如'早餐'、'午餐'、'晚餐'
        :param menu_item: 菜单项数据
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 更新后的菜单数据
        """
        menu_data = MenuPlanCache.load_from_cache(area_id, week_start, user_id) or {
            'area_id': area_id,
            'week_start': week_start
        }

        # 确保日期键存在
        if date_str not in menu_data:
            menu_data[date_str] = {'早餐': None, '午餐': None, '晚餐': None}

        # 更新菜单项 - 直接使用日期和餐次作为键，不再嵌套days和meals
        menu_data[date_str][meal_type] = menu_item

        # 保存更新后的数据
        MenuPlanCache.save_to_cache(area_id, week_start, menu_data, user_id)

        return menu_data

    @staticmethod
    def move_menu_item(area_id, week_start, source_date, source_meal, target_date, target_meal, user_id=None):
        """
        移动菜单项 - 参考employee/add的实现方式
        :param area_id: 区域ID
        :param week_start: 周开始日期，格式为'YYYY-MM-DD'
        :param source_date: 源日期字符串，格式为'YYYY-MM-DD'
        :param source_meal: 源餐次类型
        :param target_date: 目标日期字符串，格式为'YYYY-MM-DD'
        :param target_meal: 目标餐次类型
        :param user_id: 用户ID，如果为None则尝试从session获取
        :return: 更新后的菜单数据
        """
        menu_data = MenuPlanCache.load_from_cache(area_id, week_start, user_id)
        if not menu_data:
            return None

        # 检查源菜单项是否存在
        if (source_date not in menu_data or
            source_meal not in menu_data[source_date] or
            menu_data[source_date][source_meal] is None):
            return menu_data

        # 获取源菜单项 - 直接使用日期和餐次作为键
        source_item = menu_data[source_date][source_meal]

        # 确保目标日期和餐次存在
        if target_date not in menu_data:
            menu_data[target_date] = {'早餐': None, '午餐': None, '晚餐': None}

        # 保存目标位置原有的菜单项（如果有）
        target_item = menu_data[target_date].get(target_meal)

        # 移动菜单项 - 直接使用日期和餐次作为键
        menu_data[target_date][target_meal] = source_item
        menu_data[source_date][source_meal] = target_item

        # 保存更新后的数据
        MenuPlanCache.save_to_cache(area_id, week_start, menu_data, user_id)

        return menu_data

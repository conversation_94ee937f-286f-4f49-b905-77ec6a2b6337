{"version": 3, "file": "tooltip.js", "sources": ["../src/tools/sanitizer.js", "../src/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): tools/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultWhitelist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nfunction allowedAttribute(attr, allowedAttributeList) {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(SAFE_URL_PATTERN.test(attr.nodeValue) || DATA_URL_PATTERN.test(attr.nodeValue))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (regExp[i].test(attrName)) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function sanitizeHtml(unsafeHtml, whiteList, sanitizeFn) {\n  if (unsafeHtml.length === 0) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const whitelistKeys = Object.keys(whiteList)\n  const elements = [].slice.call(createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (whitelistKeys.indexOf(el.nodeName.toLowerCase()) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].slice.call(el.attributes)\n    // eslint-disable-next-line unicorn/prefer-spread\n    const whitelistedAttributes = [].concat(whiteList['*'] || [], whiteList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, whitelistedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultWhitelist, sanitizeHtml } from './tools/sanitizer'\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'whiteList', 'sanitizeFn']\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_ARROW = '.arrow'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  whiteList: DefaultWhitelist,\n  popperConfig: null\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  whiteList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if ($(this.getTipElement()).hasClass(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    $.removeData(this.element, this.constructor.DATA_KEY)\n\n    $(this.element).off(this.constructor.EVENT_KEY)\n    $(this.element).closest('.modal').off('hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      $(this.tip).remove()\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if ($(this.element).css('display') === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    const showEvent = $.Event(this.constructor.Event.SHOW)\n    if (this.isWithContent() && this._isEnabled) {\n      $(this.element).trigger(showEvent)\n\n      const shadowRoot = Util.findShadowRoot(this.element)\n      const isInTheDom = $.contains(\n        shadowRoot !== null ? shadowRoot : this.element.ownerDocument.documentElement,\n        this.element\n      )\n\n      if (showEvent.isDefaultPrevented() || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = Util.getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        $(tip).addClass(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this.addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      $(tip).data(this.constructor.DATA_KEY, this)\n\n      if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n        $(tip).appendTo(container)\n      }\n\n      $(this.element).trigger(this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      $(tip).addClass(CLASS_NAME_SHOW)\n      $(tip).addClass(this.config.customClass)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().on('mouseover', null, $.noop)\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        $(this.element).trigger(this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n        const transitionDuration = Util.getTransitionDurationFromElement(this.tip)\n\n        $(this.tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide(callback) {\n    const tip = this.getTipElement()\n    const hideEvent = $.Event(this.constructor.Event.HIDE)\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      $(this.element).trigger(this.constructor.Event.HIDDEN)\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    $(this.element).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      $(document.body).children().off('mouseover', null, $.noop)\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if ($(this.tip).hasClass(CLASS_NAME_FADE)) {\n      const transitionDuration = Util.getTransitionDurationFromElement(tip)\n\n      $(tip)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  addAttachmentClass(attachment) {\n    $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  getTipElement() {\n    this.tip = this.tip || $(this.config.template)[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent($(tip.querySelectorAll(SELECTOR_TOOLTIP_INNER)), this.getTitle())\n    $(tip).removeClass(`${CLASS_NAME_FADE} ${CLASS_NAME_SHOW}`)\n  }\n\n  setElementContent($element, content) {\n    if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n      // Content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (!$(content).parent().is($element)) {\n          $element.empty().append(content)\n        }\n      } else {\n        $element.text($(content).text())\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.whiteList, this.config.sanitizeFn)\n      }\n\n      $element.html(content)\n    } else {\n      $element.text(content)\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: SELECTOR_ARROW\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...this.config.offset(data.offsets, this.element)\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (Util.isElement(this.config.container)) {\n      return $(this.config.container)\n    }\n\n    return $(document).find(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        $(this.element).on(\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        $(this.element)\n          .on(eventIn, this.config.selector, event => this._enter(event))\n          .on(eventOut, this.config.selector, event => this._leave(event))\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    $(this.element).closest('.modal').on('hide.bs.modal', this._hideModalHandler)\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if ($(context.getTipElement()).hasClass(CLASS_NAME_SHOW) || context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || $(event.currentTarget).data(dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.currentTarget,\n        this._getDelegateConfig()\n      )\n      $(event.currentTarget).data(dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = $(this.element).data()\n\n    Object.keys(dataAttributes)\n      .forEach(dataAttr => {\n        if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n          delete dataAttributes[dataAttr]\n        }\n      })\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.whiteList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const $tip = $(this.getTipElement())\n    const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length) {\n      $tip.removeClass(tabClass.join(''))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this.addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    $(tip).removeClass(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data = $element.data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Tooltip._jQueryInterface\n$.fn[NAME].Constructor = Tooltip\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tooltip._jQueryInterface\n}\n\nexport default Tooltip\n"], "names": ["uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "toLowerCase", "indexOf", "Boolean", "test", "nodeValue", "regExp", "filter", "attrRegex", "RegExp", "len", "length", "sanitizeHtml", "unsafeHtml", "whiteList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "whitelist<PERSON><PERSON>s", "Object", "keys", "elements", "slice", "call", "body", "querySelectorAll", "el", "el<PERSON>ame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "attributeList", "attributes", "whitelistedAttributes", "concat", "for<PERSON>ach", "removeAttribute", "innerHTML", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "SELECTOR_ARROW", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "animation", "template", "trigger", "title", "delay", "html", "selector", "placement", "offset", "container", "fallbackPlacement", "boundary", "customClass", "sanitize", "popperConfig", "DefaultType", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "_popper", "_getConfig", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "toggle", "event", "dataKey", "constructor", "context", "currentTarget", "data", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "hasClass", "dispose", "clearTimeout", "removeData", "off", "closest", "_hideModalHandler", "remove", "destroy", "show", "css", "Error", "showEvent", "isWithContent", "shadowRoot", "<PERSON><PERSON>", "findShadowRoot", "isInTheDom", "contains", "ownerDocument", "documentElement", "isDefaultPrevented", "tipId", "getUID", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "addClass", "attachment", "_getAttachment", "addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "appendTo", "_getPopperConfig", "document", "children", "on", "noop", "complete", "_fixTransition", "prevHoverState", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "hide", "callback", "hideEvent", "_cleanTipClass", "removeClass", "update", "scheduleUpdate", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$element", "content", "nodeType", "j<PERSON>y", "parent", "is", "empty", "append", "text", "getAttribute", "defaultBsConfig", "modifiers", "_getOffset", "flip", "behavior", "arrow", "preventOverflow", "boundariesElement", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "offsets", "isElement", "find", "toUpperCase", "triggers", "split", "eventIn", "eventOut", "_fixTitle", "titleType", "type", "setTimeout", "dataAttributes", "dataAttr", "toString", "typeCheckConfig", "key", "$tip", "tabClass", "match", "join", "popperData", "instance", "popper", "initConfigAnimation", "_jQueryInterface", "each", "_config", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,IAAMA,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB,CAAA;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B,CAAA;EAEO,IAAMC,gBAAgB,GAAG;EAC9B;EACA,EAAA,GAAA,EAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCD,sBAAvC,CAFyB;IAG9BE,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9BC,EAAAA,CAAC,EAAE,EAlB2B;EAmB9BC,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE,EAAA;EA/B0B,CAAzB,CAAA;EAkCP;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,gEAAzB,CAAA;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,oIAAzB,CAAA;;EAEA,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,oBAAhC,EAAsD;EACpD,EAAA,IAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcC,WAAd,EAAjB,CAAA;;IAEA,IAAIH,oBAAoB,CAACI,OAArB,CAA6BH,QAA7B,CAA2C,KAAA,CAAC,CAAhD,EAAmD;MACjD,IAAIrC,QAAQ,CAACwC,OAAT,CAAiBH,QAAjB,CAA+B,KAAA,CAAC,CAApC,EAAuC;EACrC,MAAA,OAAOI,OAAO,CAACT,gBAAgB,CAACU,IAAjB,CAAsBP,IAAI,CAACQ,SAA3B,CAAyCV,IAAAA,gBAAgB,CAACS,IAAjB,CAAsBP,IAAI,CAACQ,SAA3B,CAA1C,CAAd,CAAA;EACD,KAAA;;EAED,IAAA,OAAO,IAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAMC,MAAM,GAAGR,oBAAoB,CAACS,MAArB,CAA4B,UAAAC,SAAS,EAAA;MAAA,OAAIA,SAAS,YAAYC,MAAzB,CAAA;KAArC,CAAf,CAXoD;;EAcpD,EAAA,KAAK,IAAI7B,CAAC,GAAG,CAAR,EAAW8B,GAAG,GAAGJ,MAAM,CAACK,MAA7B,EAAqC/B,CAAC,GAAG8B,GAAzC,EAA8C9B,CAAC,EAA/C,EAAmD;MACjD,IAAI0B,MAAM,CAAC1B,CAAD,CAAN,CAAUwB,IAAV,CAAeL,QAAf,CAAJ,EAA8B;EAC5B,MAAA,OAAO,IAAP,CAAA;EACD,KAAA;EACF,GAAA;;EAED,EAAA,OAAO,KAAP,CAAA;EACD,CAAA;;EAEM,SAASa,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAC9D,EAAA,IAAIF,UAAU,CAACF,MAAX,KAAsB,CAA1B,EAA6B;EAC3B,IAAA,OAAOE,UAAP,CAAA;EACD,GAAA;;EAED,EAAA,IAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;MAClD,OAAOA,UAAU,CAACF,UAAD,CAAjB,CAAA;EACD,GAAA;;EAED,EAAA,IAAMG,SAAS,GAAG,IAAIC,MAAM,CAACC,SAAX,EAAlB,CAAA;IACA,IAAMC,eAAe,GAAGH,SAAS,CAACI,eAAV,CAA0BP,UAA1B,EAAsC,WAAtC,CAAxB,CAAA;EACA,EAAA,IAAMQ,aAAa,GAAGC,MAAM,CAACC,IAAP,CAAYT,SAAZ,CAAtB,CAAA;EACA,EAAA,IAAMU,QAAQ,GAAG,EAAGC,CAAAA,KAAH,CAASC,IAAT,CAAcP,eAAe,CAACQ,IAAhB,CAAqBC,gBAArB,CAAsC,GAAtC,CAAd,CAAjB,CAAA;;IAZ8D,IAcrDhD,KAAAA,GAAAA,SAAAA,KAAAA,CAAAA,CAdqD,EAc9C8B,GAd8C,EAAA;EAe5D,IAAA,IAAMmB,EAAE,GAAGL,QAAQ,CAAC5C,CAAD,CAAnB,CAAA;EACA,IAAA,IAAMkD,MAAM,GAAGD,EAAE,CAAC7B,QAAH,CAAYC,WAAZ,EAAf,CAAA;;EAEA,IAAA,IAAIoB,aAAa,CAACnB,OAAd,CAAsB2B,EAAE,CAAC7B,QAAH,CAAYC,WAAZ,EAAtB,CAAqD,KAAA,CAAC,CAA1D,EAA6D;EAC3D4B,MAAAA,EAAE,CAACE,UAAH,CAAcC,WAAd,CAA0BH,EAA1B,CAAA,CAAA;EAEA,MAAA,OAAA,UAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAMI,aAAa,GAAG,EAAGR,CAAAA,KAAH,CAASC,IAAT,CAAcG,EAAE,CAACK,UAAjB,CAAtB,CAxB4D;;EA0B5D,IAAA,IAAMC,qBAAqB,GAAG,EAAA,CAAGC,MAAH,CAAUtB,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACgB,MAAD,CAAT,IAAqB,EAArD,CAA9B,CAAA;EAEAG,IAAAA,aAAa,CAACI,OAAd,CAAsB,UAAAxC,IAAI,EAAI;EAC5B,MAAA,IAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOsC,qBAAP,CAArB,EAAoD;EAClDN,QAAAA,EAAE,CAACS,eAAH,CAAmBzC,IAAI,CAACG,QAAxB,CAAA,CAAA;EACD,OAAA;OAHH,CAAA,CAAA;EA5B4D,GAAA,CAAA;;EAc9D,EAAA,KAAK,IAAIpB,CAAC,GAAG,CAAR,EAAW8B,GAAG,GAAGc,QAAQ,CAACb,MAA/B,EAAuC/B,CAAC,GAAG8B,GAA3C,EAAgD9B,CAAC,EAAjD,EAAqD;MAAA,IAA5CA,IAAAA,GAAAA,KAAAA,CAAAA,CAA4C,CAAA,CAAA;;MAAA,IAOjD,IAAA,KAAA,UAAA,EAAA,SAAA;EAYH,GAAA;;EAED,EAAA,OAAOuC,eAAe,CAACQ,IAAhB,CAAqBY,SAA5B,CAAA;EACD;;ECnHD;EACA;EACA;;EAEA,IAAMC,IAAI,GAAG,SAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,YAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B,CAAA;EACA,IAAMO,YAAY,GAAG,YAArB,CAAA;EACA,IAAMC,kBAAkB,GAAG,IAAIvC,MAAJ,aAAqBsC,YAArB,GAAA,MAAA,EAAyC,GAAzC,CAA3B,CAAA;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B,CAAA;EAEA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,MAAxB,CAAA;EAEA,IAAMC,gBAAgB,GAAG,MAAzB,CAAA;EACA,IAAMC,eAAe,GAAG,KAAxB,CAAA;EAEA,IAAMC,sBAAsB,GAAG,gBAA/B,CAAA;EACA,IAAMC,cAAc,GAAG,QAAvB,CAAA;EAEA,IAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,IAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,IAAMC,aAAa,GAAG,OAAtB,CAAA;EACA,IAAMC,cAAc,GAAG,QAAvB,CAAA;EAEA,IAAMC,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAE,OAHa;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE,MAAA;EALc,CAAtB,CAAA;EAQA,IAAMC,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,sCAAA,GACQ,2BADR,GAEQ,yCAJJ;EAKdC,EAAAA,OAAO,EAAE,aALK;EAMdC,EAAAA,KAAK,EAAE,EANO;EAOdC,EAAAA,KAAK,EAAE,CAPO;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASdC,EAAAA,QAAQ,EAAE,KATI;EAUdC,EAAAA,SAAS,EAAE,KAVG;EAWdC,EAAAA,MAAM,EAAE,CAXM;EAYdC,EAAAA,SAAS,EAAE,KAZG;EAadC,EAAAA,iBAAiB,EAAE,MAbL;EAcdC,EAAAA,QAAQ,EAAE,cAdI;EAedC,EAAAA,WAAW,EAAE,EAfC;EAgBdC,EAAAA,QAAQ,EAAE,IAhBI;EAiBdjE,EAAAA,UAAU,EAAE,IAjBE;EAkBdD,EAAAA,SAAS,EAAElD,gBAlBG;EAmBdqH,EAAAA,YAAY,EAAE,IAAA;EAnBA,CAAhB,CAAA;EAsBA,IAAMC,WAAW,GAAG;EAClBf,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBE,EAAAA,KAAK,EAAE,2BAHW;EAIlBD,EAAAA,OAAO,EAAE,QAJS;EAKlBE,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBC,EAAAA,QAAQ,EAAE,kBAPQ;EAQlBC,EAAAA,SAAS,EAAE,mBARO;EASlBC,EAAAA,MAAM,EAAE,0BATU;EAUlBC,EAAAA,SAAS,EAAE,0BAVO;EAWlBC,EAAAA,iBAAiB,EAAE,gBAXD;EAYlBC,EAAAA,QAAQ,EAAE,kBAZQ;EAalBC,EAAAA,WAAW,EAAE,mBAbK;EAclBC,EAAAA,QAAQ,EAAE,SAdQ;EAelBjE,EAAAA,UAAU,EAAE,iBAfM;EAgBlBD,EAAAA,SAAS,EAAE,QAhBO;EAiBlBmE,EAAAA,YAAY,EAAE,eAAA;EAjBI,CAApB,CAAA;EAoBA,IAAME,KAAK,GAAG;EACZC,EAAAA,IAAI,WAASzC,SADD;EAEZ0C,EAAAA,MAAM,aAAW1C,SAFL;EAGZ2C,EAAAA,IAAI,WAAS3C,SAHD;EAIZ4C,EAAAA,KAAK,YAAU5C,SAJH;EAKZ6C,EAAAA,QAAQ,eAAa7C,SALT;EAMZ8C,EAAAA,KAAK,YAAU9C,SANH;EAOZ+C,EAAAA,OAAO,cAAY/C,SAPP;EAQZgD,EAAAA,QAAQ,eAAahD,SART;EASZiD,EAAAA,UAAU,iBAAejD,SATb;EAUZkD,EAAAA,UAAU,EAAelD,YAAAA,GAAAA,SAAAA;EAVb,CAAd,CAAA;EAaA;EACA;EACA;;MAEMmD;IACJ,SAAYC,OAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,IAAA,IAAI,OAAOC,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,MAAA,MAAM,IAAIC,SAAJ,CAAc,8DAAd,CAAN,CAAA;EACD,KAH0B;;;MAM3B,IAAKC,CAAAA,UAAL,GAAkB,IAAlB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,CAAhB,CAAA;MACA,IAAKC,CAAAA,WAAL,GAAmB,EAAnB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,EAAtB,CAAA;EACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAf,CAV2B;;MAa3B,IAAKR,CAAAA,OAAL,GAAeA,OAAf,CAAA;EACA,IAAA,IAAA,CAAKC,MAAL,GAAc,IAAA,CAAKQ,UAAL,CAAgBR,MAAhB,CAAd,CAAA;MACA,IAAKS,CAAAA,GAAL,GAAW,IAAX,CAAA;;EAEA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;EACD;;;;;EA+BD;EACAC,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;MACP,IAAKR,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;;EAGFS,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;MACR,IAAKT,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;;EAGFU,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,IAAA,CAAKV,UAAL,GAAkB,CAAC,IAAA,CAAKA,UAAxB,CAAA;;;WAGFW,SAAA,SAAOC,MAAAA,CAAAA,KAAP,EAAc;MACZ,IAAI,CAAC,IAAKZ,CAAAA,UAAV,EAAsB;EACpB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAIY,KAAJ,EAAW;EACT,MAAA,IAAMC,OAAO,GAAG,IAAKC,CAAAA,WAAL,CAAiBvE,QAAjC,CAAA;EACA,MAAA,IAAIwE,OAAO,GAAGrE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAAd,CAAA;;QAEA,IAAI,CAACE,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,IAAKD,CAAAA,WAAT,CACRF,KAAK,CAACI,aADE,EAER,IAAA,CAAKE,kBAAL,EAFQ,CAAV,CAAA;UAIAxE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC,CAAA,CAAA;EACD,OAAA;;QAEDA,OAAO,CAACZ,cAAR,CAAuBgB,KAAvB,GAA+B,CAACJ,OAAO,CAACZ,cAAR,CAAuBgB,KAAvD,CAAA;;EAEA,MAAA,IAAIJ,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClCL,QAAAA,OAAO,CAACM,MAAR,CAAe,IAAf,EAAqBN,OAArB,CAAA,CAAA;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACO,MAAR,CAAe,IAAf,EAAqBP,OAArB,CAAA,CAAA;EACD,OAAA;EACF,KAnBD,MAmBO;QACL,IAAIrE,qBAAC,CAAC,IAAA,CAAK6E,aAAL,EAAD,CAAD,CAAwBC,QAAxB,CAAiCxE,eAAjC,CAAJ,EAAuD;EACrD,QAAA,IAAA,CAAKsE,MAAL,CAAY,IAAZ,EAAkB,IAAlB,CAAA,CAAA;;EACA,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAA,CAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB,CAAA,CAAA;EACD,KAAA;;;EAGHI,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;MACRC,YAAY,CAAC,IAAKzB,CAAAA,QAAN,CAAZ,CAAA;MAEAvD,qBAAC,CAACiF,UAAF,CAAa,IAAA,CAAK/B,OAAlB,EAA2B,IAAA,CAAKkB,WAAL,CAAiBvE,QAA5C,CAAA,CAAA;MAEAG,qBAAC,CAAC,IAAKkD,CAAAA,OAAN,CAAD,CAAgBgC,GAAhB,CAAoB,IAAA,CAAKd,WAAL,CAAiBtE,SAArC,CAAA,CAAA;EACAE,IAAAA,qBAAC,CAAC,IAAA,CAAKkD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,CAAA,CAAkCD,GAAlC,CAAsC,eAAtC,EAAuD,KAAKE,iBAA5D,CAAA,CAAA;;MAEA,IAAI,IAAA,CAAKxB,GAAT,EAAc;EACZ5D,MAAAA,qBAAC,CAAC,IAAA,CAAK4D,GAAN,CAAD,CAAYyB,MAAZ,EAAA,CAAA;EACD,KAAA;;MAED,IAAK/B,CAAAA,UAAL,GAAkB,IAAlB,CAAA;MACA,IAAKC,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKC,CAAAA,WAAL,GAAmB,IAAnB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,IAAtB,CAAA;;MACA,IAAI,IAAA,CAAKC,OAAT,EAAkB;QAChB,IAAKA,CAAAA,OAAL,CAAa4B,OAAb,EAAA,CAAA;EACD,KAAA;;MAED,IAAK5B,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKR,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKC,CAAAA,MAAL,GAAc,IAAd,CAAA;MACA,IAAKS,CAAAA,GAAL,GAAW,IAAX,CAAA;;;EAGF2B,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;MACL,IAAIvF,qBAAC,CAAC,IAAA,CAAKkD,OAAN,CAAD,CAAgBsC,GAAhB,CAAoB,SAApB,CAAmC,KAAA,MAAvC,EAA+C;EAC7C,MAAA,MAAM,IAAIC,KAAJ,CAAU,qCAAV,CAAN,CAAA;EACD,KAAA;;EAED,IAAA,IAAMC,SAAS,GAAG1F,qBAAC,CAACsC,KAAF,CAAQ,IAAK8B,CAAAA,WAAL,CAAiB9B,KAAjB,CAAuBG,IAA/B,CAAlB,CAAA;;EACA,IAAA,IAAI,IAAKkD,CAAAA,aAAL,EAAwB,IAAA,IAAA,CAAKrC,UAAjC,EAA6C;EAC3CtD,MAAAA,qBAAC,CAAC,IAAKkD,CAAAA,OAAN,CAAD,CAAgB1B,OAAhB,CAAwBkE,SAAxB,CAAA,CAAA;QAEA,IAAME,UAAU,GAAGC,wBAAI,CAACC,cAAL,CAAoB,IAAA,CAAK5C,OAAzB,CAAnB,CAAA;QACA,IAAM6C,UAAU,GAAG/F,qBAAC,CAACgG,QAAF,CACjBJ,UAAU,KAAK,IAAf,GAAsBA,UAAtB,GAAmC,IAAA,CAAK1C,OAAL,CAAa+C,aAAb,CAA2BC,eAD7C,EAEjB,IAAKhD,CAAAA,OAFY,CAAnB,CAAA;;EAKA,MAAA,IAAIwC,SAAS,CAACS,kBAAV,EAAkC,IAAA,CAACJ,UAAvC,EAAmD;EACjD,QAAA,OAAA;EACD,OAAA;;EAED,MAAA,IAAMnC,GAAG,GAAG,IAAKiB,CAAAA,aAAL,EAAZ,CAAA;QACA,IAAMuB,KAAK,GAAGP,wBAAI,CAACQ,MAAL,CAAY,IAAKjC,CAAAA,WAAL,CAAiBzE,IAA7B,CAAd,CAAA;EAEAiE,MAAAA,GAAG,CAAC0C,YAAJ,CAAiB,IAAjB,EAAuBF,KAAvB,CAAA,CAAA;EACA,MAAA,IAAA,CAAKlD,OAAL,CAAaoD,YAAb,CAA0B,kBAA1B,EAA8CF,KAA9C,CAAA,CAAA;EAEA,MAAA,IAAA,CAAKG,UAAL,EAAA,CAAA;;EAEA,MAAA,IAAI,IAAKpD,CAAAA,MAAL,CAAY7B,SAAhB,EAA2B;EACzBtB,QAAAA,qBAAC,CAAC4D,GAAD,CAAD,CAAO4C,QAAP,CAAgBnG,eAAhB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAMwB,SAAS,GAAG,OAAO,IAAA,CAAKsB,MAAL,CAAYtB,SAAnB,KAAiC,UAAjC,GAChB,IAAKsB,CAAAA,MAAL,CAAYtB,SAAZ,CAAsBhD,IAAtB,CAA2B,IAA3B,EAAiC+E,GAAjC,EAAsC,IAAA,CAAKV,OAA3C,CADgB,GAEhB,IAAA,CAAKC,MAAL,CAAYtB,SAFd,CAAA;;EAIA,MAAA,IAAM4E,UAAU,GAAG,IAAA,CAAKC,cAAL,CAAoB7E,SAApB,CAAnB,CAAA;;QACA,IAAK8E,CAAAA,kBAAL,CAAwBF,UAAxB,CAAA,CAAA;;EAEA,MAAA,IAAM1E,SAAS,GAAG,IAAK6E,CAAAA,aAAL,EAAlB,CAAA;;QACA5G,qBAAC,CAAC4D,GAAD,CAAD,CAAOW,IAAP,CAAY,IAAA,CAAKH,WAAL,CAAiBvE,QAA7B,EAAuC,IAAvC,CAAA,CAAA;;EAEA,MAAA,IAAI,CAACG,qBAAC,CAACgG,QAAF,CAAW,IAAK9C,CAAAA,OAAL,CAAa+C,aAAb,CAA2BC,eAAtC,EAAuD,IAAKtC,CAAAA,GAA5D,CAAL,EAAuE;EACrE5D,QAAAA,qBAAC,CAAC4D,GAAD,CAAD,CAAOiD,QAAP,CAAgB9E,SAAhB,CAAA,CAAA;EACD,OAAA;;EAED/B,MAAAA,qBAAC,CAAC,IAAA,CAAKkD,OAAN,CAAD,CAAgB1B,OAAhB,CAAwB,IAAA,CAAK4C,WAAL,CAAiB9B,KAAjB,CAAuBK,QAA/C,CAAA,CAAA;EAEA,MAAA,IAAA,CAAKe,OAAL,GAAe,IAAIN,0BAAJ,CAAW,IAAKF,CAAAA,OAAhB,EAAyBU,GAAzB,EAA8B,IAAKkD,CAAAA,gBAAL,CAAsBL,UAAtB,CAA9B,CAAf,CAAA;EAEAzG,MAAAA,qBAAC,CAAC4D,GAAD,CAAD,CAAO4C,QAAP,CAAgBlG,eAAhB,CAAA,CAAA;QACAN,qBAAC,CAAC4D,GAAD,CAAD,CAAO4C,QAAP,CAAgB,IAAA,CAAKrD,MAAL,CAAYjB,WAA5B,CAAA,CA5C2C;EA+C3C;EACA;EACA;;EACA,MAAA,IAAI,cAAkB6E,IAAAA,QAAQ,CAACb,eAA/B,EAAgD;EAC9ClG,QAAAA,qBAAC,CAAC+G,QAAQ,CAACjI,IAAV,CAAD,CAAiBkI,QAAjB,EAAA,CAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDjH,qBAAC,CAACkH,IAApD,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,QAAA,IAAI,KAAI,CAAChE,MAAL,CAAY7B,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAAC8F,cAAL,EAAA,CAAA;EACD,SAAA;;EAED,QAAA,IAAMC,cAAc,GAAG,KAAI,CAAC7D,WAA5B,CAAA;UACA,KAAI,CAACA,WAAL,GAAmB,IAAnB,CAAA;EAEAxD,QAAAA,qBAAC,CAAC,KAAI,CAACkD,OAAN,CAAD,CAAgB1B,OAAhB,CAAwB,KAAI,CAAC4C,WAAL,CAAiB9B,KAAjB,CAAuBI,KAA/C,CAAA,CAAA;;UAEA,IAAI2E,cAAc,KAAK7G,eAAvB,EAAwC;EACtC,UAAA,KAAI,CAACoE,MAAL,CAAY,IAAZ,EAAkB,KAAlB,CAAA,CAAA;EACD,SAAA;SAZH,CAAA;;QAeA,IAAI5E,qBAAC,CAAC,IAAA,CAAK4D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBzE,eAArB,CAAJ,EAA2C;UACzC,IAAMiH,kBAAkB,GAAGzB,wBAAI,CAAC0B,gCAAL,CAAsC,IAAA,CAAK3D,GAA3C,CAA3B,CAAA;EAEA5D,QAAAA,qBAAC,CAAC,IAAA,CAAK4D,GAAN,CAAD,CACG4D,GADH,CACO3B,wBAAI,CAAC4B,cADZ,EAC4BN,QAD5B,CAEGO,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;EAGD,OAND,MAMO;UACLH,QAAQ,EAAA,CAAA;EACT,OAAA;EACF,KAAA;;;WAGHQ,OAAA,SAAKC,IAAAA,CAAAA,QAAL,EAAe;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACb,IAAA,IAAMhE,GAAG,GAAG,IAAKiB,CAAAA,aAAL,EAAZ,CAAA;EACA,IAAA,IAAMgD,SAAS,GAAG7H,qBAAC,CAACsC,KAAF,CAAQ,IAAK8B,CAAAA,WAAL,CAAiB9B,KAAjB,CAAuBC,IAA/B,CAAlB,CAAA;;EACA,IAAA,IAAM4E,QAAQ,GAAG,SAAXA,QAAW,GAAM;QACrB,IAAI,MAAI,CAAC3D,WAAL,KAAqBjD,gBAArB,IAAyCqD,GAAG,CAAC1E,UAAjD,EAA6D;EAC3D0E,QAAAA,GAAG,CAAC1E,UAAJ,CAAeC,WAAf,CAA2ByE,GAA3B,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,MAAI,CAACkE,cAAL,EAAA,CAAA;;EACA,MAAA,MAAI,CAAC5E,OAAL,CAAazD,eAAb,CAA6B,kBAA7B,CAAA,CAAA;;EACAO,MAAAA,qBAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CAAgB1B,OAAhB,CAAwB,MAAI,CAAC4C,WAAL,CAAiB9B,KAAjB,CAAuBE,MAA/C,CAAA,CAAA;;EACA,MAAA,IAAI,MAAI,CAACkB,OAAL,KAAiB,IAArB,EAA2B;UACzB,MAAI,CAACA,OAAL,CAAa4B,OAAb,EAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAIsC,QAAJ,EAAc;UACZA,QAAQ,EAAA,CAAA;EACT,OAAA;OAdH,CAAA;;EAiBA5H,IAAAA,qBAAC,CAAC,IAAKkD,CAAAA,OAAN,CAAD,CAAgB1B,OAAhB,CAAwBqG,SAAxB,CAAA,CAAA;;EAEA,IAAA,IAAIA,SAAS,CAAC1B,kBAAV,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;MAEDnG,qBAAC,CAAC4D,GAAD,CAAD,CAAOmE,WAAP,CAAmBzH,eAAnB,EA1Ba;EA6Bb;;EACA,IAAA,IAAI,cAAkByG,IAAAA,QAAQ,CAACb,eAA/B,EAAgD;EAC9ClG,MAAAA,qBAAC,CAAC+G,QAAQ,CAACjI,IAAV,CAAD,CAAiBkI,QAAjB,EAAA,CAA4B9B,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmDlF,qBAAC,CAACkH,IAArD,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAA,CAAKzD,cAAL,CAAoB5C,aAApB,CAAA,GAAqC,KAArC,CAAA;EACA,IAAA,IAAA,CAAK4C,cAAL,CAAoB7C,aAApB,CAAA,GAAqC,KAArC,CAAA;EACA,IAAA,IAAA,CAAK6C,cAAL,CAAoB9C,aAApB,CAAA,GAAqC,KAArC,CAAA;;MAEA,IAAIX,qBAAC,CAAC,IAAA,CAAK4D,GAAN,CAAD,CAAYkB,QAAZ,CAAqBzE,eAArB,CAAJ,EAA2C;EACzC,MAAA,IAAMiH,kBAAkB,GAAGzB,wBAAI,CAAC0B,gCAAL,CAAsC3D,GAAtC,CAA3B,CAAA;EAEA5D,MAAAA,qBAAC,CAAC4D,GAAD,CAAD,CACG4D,GADH,CACO3B,wBAAI,CAAC4B,cADZ,EAC4BN,QAD5B,CAEGO,CAAAA,oBAFH,CAEwBJ,kBAFxB,CAAA,CAAA;EAGD,KAND,MAMO;QACLH,QAAQ,EAAA,CAAA;EACT,KAAA;;MAED,IAAK3D,CAAAA,WAAL,GAAmB,EAAnB,CAAA;;;EAGFwE,EAAAA,MAAAA,CAAAA,SAAA,SAAS,MAAA,GAAA;EACP,IAAA,IAAI,IAAKtE,CAAAA,OAAL,KAAiB,IAArB,EAA2B;QACzB,IAAKA,CAAAA,OAAL,CAAauE,cAAb,EAAA,CAAA;EACD,KAAA;EACF;;;EAGDtC,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,OAAOrI,OAAO,CAAC,IAAK4K,CAAAA,QAAL,EAAD,CAAd,CAAA;;;WAGFvB,qBAAA,SAAmBF,kBAAAA,CAAAA,UAAnB,EAA+B;MAC7BzG,qBAAC,CAAC,IAAK6E,CAAAA,aAAL,EAAD,CAAD,CAAwB2B,QAAxB,CAAoCtG,YAApC,GAAA,GAAA,GAAoDuG,UAApD,CAAA,CAAA;;;EAGF5B,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,IAAA,CAAKjB,GAAL,GAAW,IAAKA,CAAAA,GAAL,IAAY5D,qBAAC,CAAC,IAAKmD,CAAAA,MAAL,CAAY5B,QAAb,CAAD,CAAwB,CAAxB,CAAvB,CAAA;EACA,IAAA,OAAO,KAAKqC,GAAZ,CAAA;;;EAGF2C,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;EACX,IAAA,IAAM3C,GAAG,GAAG,IAAKiB,CAAAA,aAAL,EAAZ,CAAA;EACA,IAAA,IAAA,CAAKsD,iBAAL,CAAuBnI,qBAAC,CAAC4D,GAAG,CAAC7E,gBAAJ,CAAqB0B,sBAArB,CAAD,CAAxB,EAAwE,IAAA,CAAKyH,QAAL,EAAxE,CAAA,CAAA;MACAlI,qBAAC,CAAC4D,GAAD,CAAD,CAAOmE,WAAP,CAAsB1H,eAAtB,SAAyCC,eAAzC,CAAA,CAAA;;;EAGF6H,EAAAA,MAAAA,CAAAA,oBAAA,SAAA,iBAAA,CAAkBC,QAAlB,EAA4BC,OAA5B,EAAqC;EACnC,IAAA,IAAI,OAAOA,OAAP,KAAmB,QAAnB,KAAgCA,OAAO,CAACC,QAAR,IAAoBD,OAAO,CAACE,MAA5D,CAAJ,EAAyE;EACvE;EACA,MAAA,IAAI,IAAKpF,CAAAA,MAAL,CAAYxB,IAAhB,EAAsB;EACpB,QAAA,IAAI,CAAC3B,qBAAC,CAACqI,OAAD,CAAD,CAAWG,MAAX,EAAA,CAAoBC,EAApB,CAAuBL,QAAvB,CAAL,EAAuC;EACrCA,UAAAA,QAAQ,CAACM,KAAT,EAAiBC,CAAAA,MAAjB,CAAwBN,OAAxB,CAAA,CAAA;EACD,SAAA;EACF,OAJD,MAIO;UACLD,QAAQ,CAACQ,IAAT,CAAc5I,qBAAC,CAACqI,OAAD,CAAD,CAAWO,IAAX,EAAd,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,IAAKzF,CAAAA,MAAL,CAAYxB,IAAhB,EAAsB;EACpB,MAAA,IAAI,IAAKwB,CAAAA,MAAL,CAAYhB,QAAhB,EAA0B;EACxBkG,QAAAA,OAAO,GAAGtK,YAAY,CAACsK,OAAD,EAAU,IAAKlF,CAAAA,MAAL,CAAYlF,SAAtB,EAAiC,IAAA,CAAKkF,MAAL,CAAYjF,UAA7C,CAAtB,CAAA;EACD,OAAA;;QAEDkK,QAAQ,CAACzG,IAAT,CAAc0G,OAAd,CAAA,CAAA;EACD,KAND,MAMO;QACLD,QAAQ,CAACQ,IAAT,CAAcP,OAAd,CAAA,CAAA;EACD,KAAA;;;EAGHH,EAAAA,MAAAA,CAAAA,WAAA,SAAW,QAAA,GAAA;MACT,IAAIzG,KAAK,GAAG,IAAKyB,CAAAA,OAAL,CAAa2F,YAAb,CAA0B,qBAA1B,CAAZ,CAAA;;MAEA,IAAI,CAACpH,KAAL,EAAY;QACVA,KAAK,GAAG,OAAO,IAAK0B,CAAAA,MAAL,CAAY1B,KAAnB,KAA6B,UAA7B,GACN,IAAK0B,CAAAA,MAAL,CAAY1B,KAAZ,CAAkB5C,IAAlB,CAAuB,IAAKqE,CAAAA,OAA5B,CADM,GAEN,IAAA,CAAKC,MAAL,CAAY1B,KAFd,CAAA;EAGD,KAAA;;EAED,IAAA,OAAOA,KAAP,CAAA;EACD;;;WAGDqF,mBAAA,SAAiBL,gBAAAA,CAAAA,UAAjB,EAA6B;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EAC3B,IAAA,IAAMqC,eAAe,GAAG;EACtBjH,MAAAA,SAAS,EAAE4E,UADW;EAEtBsC,MAAAA,SAAS,EAAE;UACTjH,MAAM,EAAE,IAAKkH,CAAAA,UAAL,EADC;EAETC,QAAAA,IAAI,EAAE;YACJC,QAAQ,EAAE,IAAK/F,CAAAA,MAAL,CAAYnB,iBAAAA;WAHf;EAKTmH,QAAAA,KAAK,EAAE;EACLjG,UAAAA,OAAO,EAAExC,cAAAA;WANF;EAQT0I,QAAAA,eAAe,EAAE;YACfC,iBAAiB,EAAE,IAAKlG,CAAAA,MAAL,CAAYlB,QAAAA;EADhB,SAAA;SAVG;QActBqH,QAAQ,EAAE,SAAA/E,QAAAA,CAAAA,IAAI,EAAI;EAChB,QAAA,IAAIA,IAAI,CAACgF,iBAAL,KAA2BhF,IAAI,CAAC1C,SAApC,EAA+C;YAC7C,MAAI,CAAC2H,4BAAL,CAAkCjF,IAAlC,CAAA,CAAA;EACD,SAAA;SAjBmB;QAmBtBkF,QAAQ,EAAE,kBAAAlF,IAAI,EAAA;EAAA,QAAA,OAAI,MAAI,CAACiF,4BAAL,CAAkCjF,IAAlC,CAAJ,CAAA;EAAA,OAAA;OAnBhB,CAAA;EAsBA,IAAA,OAAA,QAAA,CAAA,EAAA,EACKuE,eADL,EAEK,IAAK3F,CAAAA,MAAL,CAAYf,YAFjB,CAAA,CAAA;;;EAMF4G,EAAAA,MAAAA,CAAAA,aAAA,SAAa,UAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACX,IAAMlH,MAAM,GAAG,EAAf,CAAA;;EAEA,IAAA,IAAI,OAAO,IAAKqB,CAAAA,MAAL,CAAYrB,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAAC7B,EAAP,GAAY,UAAAsE,IAAI,EAAI;UAClBA,IAAI,CAACmF,OAAL,GACKnF,QAAAA,CAAAA,EAAAA,EAAAA,IAAI,CAACmF,OADV,EAEK,MAAI,CAACvG,MAAL,CAAYrB,MAAZ,CAAmByC,IAAI,CAACmF,OAAxB,EAAiC,MAAI,CAACxG,OAAtC,CAFL,CAAA,CAAA;EAKA,QAAA,OAAOqB,IAAP,CAAA;SANF,CAAA;EAQD,KATD,MASO;EACLzC,MAAAA,MAAM,CAACA,MAAP,GAAgB,IAAKqB,CAAAA,MAAL,CAAYrB,MAA5B,CAAA;EACD,KAAA;;EAED,IAAA,OAAOA,MAAP,CAAA;;;EAGF8E,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EACd,IAAA,IAAI,KAAKzD,MAAL,CAAYpB,SAAZ,KAA0B,KAA9B,EAAqC;QACnC,OAAOgF,QAAQ,CAACjI,IAAhB,CAAA;EACD,KAAA;;MAED,IAAI+G,wBAAI,CAAC8D,SAAL,CAAe,KAAKxG,MAAL,CAAYpB,SAA3B,CAAJ,EAA2C;EACzC,MAAA,OAAO/B,qBAAC,CAAC,IAAA,CAAKmD,MAAL,CAAYpB,SAAb,CAAR,CAAA;EACD,KAAA;;MAED,OAAO/B,qBAAC,CAAC+G,QAAD,CAAD,CAAY6C,IAAZ,CAAiB,IAAKzG,CAAAA,MAAL,CAAYpB,SAA7B,CAAP,CAAA;;;WAGF2E,iBAAA,SAAe7E,cAAAA,CAAAA,SAAf,EAA0B;EACxB,IAAA,OAAOd,aAAa,CAACc,SAAS,CAACgI,WAAV,EAAD,CAApB,CAAA;;;EAGFhG,EAAAA,MAAAA,CAAAA,gBAAA,SAAgB,aAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACd,IAAMiG,QAAQ,GAAG,IAAA,CAAK3G,MAAL,CAAY3B,OAAZ,CAAoBuI,KAApB,CAA0B,GAA1B,CAAjB,CAAA;EAEAD,IAAAA,QAAQ,CAACtK,OAAT,CAAiB,UAAAgC,OAAO,EAAI;QAC1B,IAAIA,OAAO,KAAK,OAAhB,EAAyB;UACvBxB,qBAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CAAgB+D,EAAhB,CACE,MAAI,CAAC7C,WAAL,CAAiB9B,KAAjB,CAAuBM,KADzB,EAEE,MAAI,CAACO,MAAL,CAAYvB,QAFd,EAGE,UAAAsC,KAAK,EAAA;EAAA,UAAA,OAAI,MAAI,CAACD,MAAL,CAAYC,KAAZ,CAAJ,CAAA;WAHP,CAAA,CAAA;EAKD,OAND,MAMO,IAAI1C,OAAO,KAAKV,cAAhB,EAAgC;UACrC,IAAMkJ,OAAO,GAAGxI,OAAO,KAAKb,aAAZ,GACd,MAAI,CAACyD,WAAL,CAAiB9B,KAAjB,CAAuBS,UADT,GAEd,MAAI,CAACqB,WAAL,CAAiB9B,KAAjB,CAAuBO,OAFzB,CAAA;UAGA,IAAMoH,QAAQ,GAAGzI,OAAO,KAAKb,aAAZ,GACf,MAAI,CAACyD,WAAL,CAAiB9B,KAAjB,CAAuBU,UADR,GAEf,MAAI,CAACoB,WAAL,CAAiB9B,KAAjB,CAAuBQ,QAFzB,CAAA;EAIA9C,QAAAA,qBAAC,CAAC,MAAI,CAACkD,OAAN,CAAD,CACG+D,EADH,CACM+C,OADN,EACe,MAAI,CAAC7G,MAAL,CAAYvB,QAD3B,EACqC,UAAAsC,KAAK,EAAA;EAAA,UAAA,OAAI,MAAI,CAACS,MAAL,CAAYT,KAAZ,CAAJ,CAAA;EAAA,SAD1C,CAEG+C,CAAAA,EAFH,CAEMgD,QAFN,EAEgB,MAAI,CAAC9G,MAAL,CAAYvB,QAF5B,EAEsC,UAAAsC,KAAK,EAAA;EAAA,UAAA,OAAI,MAAI,CAACU,MAAL,CAAYV,KAAZ,CAAJ,CAAA;WAF3C,CAAA,CAAA;EAGD,OAAA;OAlBH,CAAA,CAAA;;MAqBA,IAAKkB,CAAAA,iBAAL,GAAyB,YAAM;QAC7B,IAAI,MAAI,CAAClC,OAAT,EAAkB;EAChB,QAAA,MAAI,CAACyE,IAAL,EAAA,CAAA;EACD,OAAA;OAHH,CAAA;;EAMA3H,IAAAA,qBAAC,CAAC,IAAA,CAAKkD,OAAN,CAAD,CAAgBiC,OAAhB,CAAwB,QAAxB,CAAA,CAAkC8B,EAAlC,CAAqC,eAArC,EAAsD,KAAK7B,iBAA3D,CAAA,CAAA;;EAEA,IAAA,IAAI,IAAKjC,CAAAA,MAAL,CAAYvB,QAAhB,EAA0B;QACxB,IAAKuB,CAAAA,MAAL,GACK,QAAA,CAAA,EAAA,EAAA,IAAA,CAAKA,MADV,EAAA;EAEE3B,QAAAA,OAAO,EAAE,QAFX;EAGEI,QAAAA,QAAQ,EAAE,EAAA;EAHZ,OAAA,CAAA,CAAA;EAKD,KAND,MAMO;EACL,MAAA,IAAA,CAAKsI,SAAL,EAAA,CAAA;EACD,KAAA;;;EAGHA,EAAAA,MAAAA,CAAAA,YAAA,SAAY,SAAA,GAAA;MACV,IAAMC,SAAS,GAAG,OAAO,IAAA,CAAKjH,OAAL,CAAa2F,YAAb,CAA0B,qBAA1B,CAAzB,CAAA;;MAEA,IAAI,IAAA,CAAK3F,OAAL,CAAa2F,YAAb,CAA0B,OAA1B,CAAsCsB,IAAAA,SAAS,KAAK,QAAxD,EAAkE;EAChE,MAAA,IAAA,CAAKjH,OAAL,CAAaoD,YAAb,CACE,qBADF,EAEE,IAAKpD,CAAAA,OAAL,CAAa2F,YAAb,CAA0B,OAA1B,KAAsC,EAFxC,CAAA,CAAA;EAKA,MAAA,IAAA,CAAK3F,OAAL,CAAaoD,YAAb,CAA0B,OAA1B,EAAmC,EAAnC,CAAA,CAAA;EACD,KAAA;;;EAGH3B,EAAAA,MAAAA,CAAAA,SAAA,SAAA,MAAA,CAAOT,KAAP,EAAcG,OAAd,EAAuB;EACrB,IAAA,IAAMF,OAAO,GAAG,IAAKC,CAAAA,WAAL,CAAiBvE,QAAjC,CAAA;EACAwE,IAAAA,OAAO,GAAGA,OAAO,IAAIrE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB,CAAA;;MAEA,IAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,IAAKD,CAAAA,WAAT,CACRF,KAAK,CAACI,aADE,EAER,IAAA,CAAKE,kBAAL,EAFQ,CAAV,CAAA;QAIAxE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAACkG,IAAN,KAAe,SAAf,GAA2BxJ,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ,CAAA;EAGD,KAAA;;EAED,IAAA,IAAIX,qBAAC,CAACqE,OAAO,CAACQ,aAAR,EAAD,CAAD,CAA2BC,QAA3B,CAAoCxE,eAApC,CAAwD+D,IAAAA,OAAO,CAACb,WAAR,KAAwBjD,gBAApF,EAAsG;QACpG8D,OAAO,CAACb,WAAR,GAAsBjD,gBAAtB,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAEDyE,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ,CAAA;MAEAc,OAAO,CAACb,WAAR,GAAsBjD,gBAAtB,CAAA;;EAEA,IAAA,IAAI,CAAC8D,OAAO,CAAClB,MAAR,CAAezB,KAAhB,IAAyB,CAAC2C,OAAO,CAAClB,MAAR,CAAezB,KAAf,CAAqB6D,IAAnD,EAAyD;EACvDlB,MAAAA,OAAO,CAACkB,IAAR,EAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAEDlB,IAAAA,OAAO,CAACd,QAAR,GAAmB8G,UAAU,CAAC,YAAM;EAClC,MAAA,IAAIhG,OAAO,CAACb,WAAR,KAAwBjD,gBAA5B,EAA8C;EAC5C8D,QAAAA,OAAO,CAACkB,IAAR,EAAA,CAAA;EACD,OAAA;OAH0B,EAI1BlB,OAAO,CAAClB,MAAR,CAAezB,KAAf,CAAqB6D,IAJK,CAA7B,CAAA;;;EAOFX,EAAAA,MAAAA,CAAAA,SAAA,SAAA,MAAA,CAAOV,KAAP,EAAcG,OAAd,EAAuB;EACrB,IAAA,IAAMF,OAAO,GAAG,IAAKC,CAAAA,WAAL,CAAiBvE,QAAjC,CAAA;EACAwE,IAAAA,OAAO,GAAGA,OAAO,IAAIrE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,CAArB,CAAA;;MAEA,IAAI,CAACE,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,IAAKD,CAAAA,WAAT,CACRF,KAAK,CAACI,aADE,EAER,IAAA,CAAKE,kBAAL,EAFQ,CAAV,CAAA;QAIAxE,qBAAC,CAACkE,KAAK,CAACI,aAAP,CAAD,CAAuBC,IAAvB,CAA4BJ,OAA5B,EAAqCE,OAArC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIH,KAAJ,EAAW;EACTG,MAAAA,OAAO,CAACZ,cAAR,CACES,KAAK,CAACkG,IAAN,KAAe,UAAf,GAA4BxJ,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ,CAAA;EAGD,KAAA;;EAED,IAAA,IAAI0D,OAAO,CAACK,oBAAR,EAAJ,EAAoC;EAClC,MAAA,OAAA;EACD,KAAA;;EAEDM,IAAAA,YAAY,CAACX,OAAO,CAACd,QAAT,CAAZ,CAAA;MAEAc,OAAO,CAACb,WAAR,GAAsBhD,eAAtB,CAAA;;EAEA,IAAA,IAAI,CAAC6D,OAAO,CAAClB,MAAR,CAAezB,KAAhB,IAAyB,CAAC2C,OAAO,CAAClB,MAAR,CAAezB,KAAf,CAAqBiG,IAAnD,EAAyD;EACvDtD,MAAAA,OAAO,CAACsD,IAAR,EAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;EAEDtD,IAAAA,OAAO,CAACd,QAAR,GAAmB8G,UAAU,CAAC,YAAM;EAClC,MAAA,IAAIhG,OAAO,CAACb,WAAR,KAAwBhD,eAA5B,EAA6C;EAC3C6D,QAAAA,OAAO,CAACsD,IAAR,EAAA,CAAA;EACD,OAAA;OAH0B,EAI1BtD,OAAO,CAAClB,MAAR,CAAezB,KAAf,CAAqBiG,IAJK,CAA7B,CAAA;;;EAOFjD,EAAAA,MAAAA,CAAAA,uBAAA,SAAuB,oBAAA,GAAA;EACrB,IAAA,KAAK,IAAMlD,OAAX,IAAsB,IAAA,CAAKiC,cAA3B,EAA2C;EACzC,MAAA,IAAI,IAAKA,CAAAA,cAAL,CAAoBjC,OAApB,CAAJ,EAAkC;EAChC,QAAA,OAAO,IAAP,CAAA;EACD,OAAA;EACF,KAAA;;EAED,IAAA,OAAO,KAAP,CAAA;;;WAGFmC,aAAA,SAAWR,UAAAA,CAAAA,MAAX,EAAmB;MACjB,IAAMmH,cAAc,GAAGtK,qBAAC,CAAC,KAAKkD,OAAN,CAAD,CAAgBqB,IAAhB,EAAvB,CAAA;MAEA9F,MAAM,CAACC,IAAP,CAAY4L,cAAZ,EACG9K,OADH,CACW,UAAA+K,QAAQ,EAAI;QACnB,IAAInK,qBAAqB,CAAC/C,OAAtB,CAA8BkN,QAA9B,CAA4C,KAAA,CAAC,CAAjD,EAAoD;UAClD,OAAOD,cAAc,CAACC,QAAD,CAArB,CAAA;EACD,OAAA;OAJL,CAAA,CAAA;EAOApH,IAAAA,MAAM,gBACD,IAAKiB,CAAAA,WAAL,CAAiB/C,OADhB,EAEDiJ,cAFC,EAGA,OAAOnH,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN,CAAA;;EAMA,IAAA,IAAI,OAAOA,MAAM,CAACzB,KAAd,KAAwB,QAA5B,EAAsC;QACpCyB,MAAM,CAACzB,KAAP,GAAe;UACb6D,IAAI,EAAEpC,MAAM,CAACzB,KADA;UAEbiG,IAAI,EAAExE,MAAM,CAACzB,KAAAA;SAFf,CAAA;EAID,KAAA;;EAED,IAAA,IAAI,OAAOyB,MAAM,CAAC1B,KAAd,KAAwB,QAA5B,EAAsC;QACpC0B,MAAM,CAAC1B,KAAP,GAAe0B,MAAM,CAAC1B,KAAP,CAAa+I,QAAb,EAAf,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,OAAOrH,MAAM,CAACkF,OAAd,KAA0B,QAA9B,EAAwC;QACtClF,MAAM,CAACkF,OAAP,GAAiBlF,MAAM,CAACkF,OAAP,CAAemC,QAAf,EAAjB,CAAA;EACD,KAAA;;MAED3E,wBAAI,CAAC4E,eAAL,CACE9K,IADF,EAEEwD,MAFF,EAGE,IAAA,CAAKiB,WAAL,CAAiB/B,WAHnB,CAAA,CAAA;;MAMA,IAAIc,MAAM,CAAChB,QAAX,EAAqB;EACnBgB,MAAAA,MAAM,CAAC5B,QAAP,GAAkBxD,YAAY,CAACoF,MAAM,CAAC5B,QAAR,EAAkB4B,MAAM,CAAClF,SAAzB,EAAoCkF,MAAM,CAACjF,UAA3C,CAA9B,CAAA;EACD,KAAA;;EAED,IAAA,OAAOiF,MAAP,CAAA;;;EAGFqB,EAAAA,MAAAA,CAAAA,qBAAA,SAAqB,kBAAA,GAAA;MACnB,IAAMrB,MAAM,GAAG,EAAf,CAAA;;MAEA,IAAI,IAAA,CAAKA,MAAT,EAAiB;EACf,MAAA,KAAK,IAAMuH,GAAX,IAAkB,IAAA,CAAKvH,MAAvB,EAA+B;EAC7B,QAAA,IAAI,IAAKiB,CAAAA,WAAL,CAAiB/C,OAAjB,CAAyBqJ,GAAzB,CAAkC,KAAA,IAAA,CAAKvH,MAAL,CAAYuH,GAAZ,CAAtC,EAAwD;YACtDvH,MAAM,CAACuH,GAAD,CAAN,GAAc,KAAKvH,MAAL,CAAYuH,GAAZ,CAAd,CAAA;EACD,SAAA;EACF,OAAA;EACF,KAAA;;EAED,IAAA,OAAOvH,MAAP,CAAA;;;EAGF2E,EAAAA,MAAAA,CAAAA,iBAAA,SAAiB,cAAA,GAAA;EACf,IAAA,IAAM6C,IAAI,GAAG3K,qBAAC,CAAC,IAAK6E,CAAAA,aAAL,EAAD,CAAd,CAAA;MACA,IAAM+F,QAAQ,GAAGD,IAAI,CAAC3N,IAAL,CAAU,OAAV,CAAmB6N,CAAAA,KAAnB,CAAyB1K,kBAAzB,CAAjB,CAAA;;EACA,IAAA,IAAIyK,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC9M,MAAlC,EAA0C;QACxC6M,IAAI,CAAC5C,WAAL,CAAiB6C,QAAQ,CAACE,IAAT,CAAc,EAAd,CAAjB,CAAA,CAAA;EACD,KAAA;;;WAGHtB,+BAAA,SAA6BuB,4BAAAA,CAAAA,UAA7B,EAAyC;EACvC,IAAA,IAAA,CAAKnH,GAAL,GAAWmH,UAAU,CAACC,QAAX,CAAoBC,MAA/B,CAAA;;EACA,IAAA,IAAA,CAAKnD,cAAL,EAAA,CAAA;;MACA,IAAKnB,CAAAA,kBAAL,CAAwB,IAAKD,CAAAA,cAAL,CAAoBqE,UAAU,CAAClJ,SAA/B,CAAxB,CAAA,CAAA;;;EAGFuF,EAAAA,MAAAA,CAAAA,iBAAA,SAAiB,cAAA,GAAA;EACf,IAAA,IAAMxD,GAAG,GAAG,IAAKiB,CAAAA,aAAL,EAAZ,CAAA;EACA,IAAA,IAAMqG,mBAAmB,GAAG,IAAK/H,CAAAA,MAAL,CAAY7B,SAAxC,CAAA;;EAEA,IAAA,IAAIsC,GAAG,CAACiF,YAAJ,CAAiB,aAAjB,CAAA,KAAoC,IAAxC,EAA8C;EAC5C,MAAA,OAAA;EACD,KAAA;;EAED7I,IAAAA,qBAAC,CAAC4D,GAAD,CAAD,CAAOmE,WAAP,CAAmB1H,eAAnB,CAAA,CAAA;EACA,IAAA,IAAA,CAAK8C,MAAL,CAAY7B,SAAZ,GAAwB,KAAxB,CAAA;EACA,IAAA,IAAA,CAAKqG,IAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKpC,IAAL,EAAA,CAAA;EACA,IAAA,IAAA,CAAKpC,MAAL,CAAY7B,SAAZ,GAAwB4J,mBAAxB,CAAA;EACD;;;YAGMC,mBAAP,SAAwBhI,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAKiI,IAAL,CAAU,YAAY;EAC3B,MAAA,IAAMhD,QAAQ,GAAGpI,qBAAC,CAAC,IAAD,CAAlB,CAAA;EACA,MAAA,IAAIuE,IAAI,GAAG6D,QAAQ,CAAC7D,IAAT,CAAc1E,QAAd,CAAX,CAAA;;EACA,MAAA,IAAMwL,OAAO,GAAG,OAAOlI,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C,CAAA;;QAEA,IAAI,CAACoB,IAAD,IAAS,cAAA,CAAehH,IAAf,CAAoB4F,MAApB,CAAb,EAA0C;EACxC,QAAA,OAAA;EACD,OAAA;;QAED,IAAI,CAACoB,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAItB,OAAJ,CAAY,IAAZ,EAAkBoI,OAAlB,CAAP,CAAA;EACAjD,QAAAA,QAAQ,CAAC7D,IAAT,CAAc1E,QAAd,EAAwB0E,IAAxB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAOpB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,QAAA,IAAI,OAAOoB,IAAI,CAACpB,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIE,SAAJ,CAAkCF,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDoB,IAAI,CAACpB,MAAD,CAAJ,EAAA,CAAA;EACD,OAAA;EACF,KArBM,CAAP,CAAA;;;;;WAvlBF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOvD,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOyB,OAAP,CAAA;EACD,KAAA;;;WAED,SAAkB,GAAA,GAAA;EAChB,MAAA,OAAO1B,IAAP,CAAA;EACD,KAAA;;;WAED,SAAsB,GAAA,GAAA;EACpB,MAAA,OAAOE,QAAP,CAAA;EACD,KAAA;;;WAED,SAAmB,GAAA,GAAA;EACjB,MAAA,OAAOyC,KAAP,CAAA;EACD,KAAA;;;WAED,SAAuB,GAAA,GAAA;EACrB,MAAA,OAAOxC,SAAP,CAAA;EACD,KAAA;;;WAED,SAAyB,GAAA,GAAA;EACvB,MAAA,OAAOuC,WAAP,CAAA;EACD,KAAA;;;;;EAslBH;EACA;EACA;;;AAEArC,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAasD,GAAAA,OAAO,CAACkI,gBAArB,CAAA;AACAnL,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAW2L,CAAAA,WAAX,GAAyBrI,OAAzB,CAAA;;AACAjD,uBAAC,CAACC,EAAF,CAAKN,IAAL,CAAW4L,CAAAA,UAAX,GAAwB,YAAM;EAC5BvL,EAAAA,qBAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb,CAAA;IACA,OAAOkD,OAAO,CAACkI,gBAAf,CAAA;EACD,CAHD;;;;;;;;"}
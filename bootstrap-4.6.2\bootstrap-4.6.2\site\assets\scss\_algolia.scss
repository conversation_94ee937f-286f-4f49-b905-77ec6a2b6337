// stylelint-disable declaration-no-important, selector-class-pattern

// Docsearch overrides
//
// `!important` indicates overridden properties.
.algolia-autocomplete {
  display: block !important;
  flex: 1;

  // Menu container
  .ds-dropdown-menu {
    width: 100%;
    min-width: 0 !important;
    max-width: none !important;
    padding: .75rem 0 !important;
    background-color: $white;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .1);
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .175);

    @include media-breakpoint-up(md) {
      width: 175%;
    }

    // Caret
    &::before {
      display: none !important;
    }

    [class^="ds-dataset-"] {
      padding: 0 !important;
      overflow: visible !important;
      background-color: transparent !important;
      border: 0 !important;
    }

    .ds-suggestions {
      margin-top: 0 !important;
    }
  }

  .algolia-docsearch-suggestion {
    padding: 0 !important;
    overflow: visible !important;
  }

  .algolia-docsearch-suggestion--category-header {
    padding: .125rem 1rem !important;
    margin-top: 0 !important;
    @include font-size(.875rem, true);
    font-weight: 600 !important;
    color: $bd-purple-bright !important;
    border-bottom: 0 !important;
  }

  .algolia-docsearch-suggestion--wrapper {
    float: none !important;
    padding-top: 0 !important;
  }

  // Section header
  .algolia-docsearch-suggestion--subcategory-column {
    float: none !important;
    width: auto !important;
    padding: 0 !important;
    text-align: left !important;
  }

  .algolia-docsearch-suggestion--subcategory-inline {
    display: block !important;
    @include font-size(.875rem);
    color: $gray-700;

    &::after {
      padding: 0 .25rem;
      content: "/";
    }
  }

  .algolia-docsearch-suggestion--content {
    display: flex;
    flex-wrap: wrap;
    float: none !important;
    width: 100% !important;
    padding: .25rem 1rem !important;

    // Vertical divider between column header and content
    &::before {
      display: none !important;
    }
  }

  .ds-suggestion {
    &:not(:first-child) {
      .algolia-docsearch-suggestion--category-header {
        padding-top: .75rem !important;
        margin-top: .75rem !important;
        border-top: 1px solid rgba(0, 0, 0, .1);
      }
    }

    .algolia-docsearch-suggestion--subcategory-column {
      display: none !important;
    }
  }

  .algolia-docsearch-suggestion--title {
    display: block;
    margin-bottom: 0 !important;
    @include font-size(.875rem, true);
    font-weight: 400 !important;
  }

  .algolia-docsearch-suggestion--text {
    flex: 0 0 100%;
    max-width: 100%;
    padding: .2rem 0;
    @include font-size(.8125rem, true);
    font-weight: 400;
    line-height: 1.25 !important;
    color: $gray-600;
  }

  .algolia-docsearch-footer {
    float: none !important;
    width: auto !important;
    height: auto !important;
    padding: .75rem 1rem 0;
    @include font-size(.75rem, true);
    line-height: 1 !important;
    color: #767676 !important;
    border-top: 1px solid rgba(0, 0, 0, .1);
  }

  .algolia-docsearch-footer--logo {
    display: inline !important;
    overflow: visible !important;
    color: inherit !important;
    text-indent: 0 !important;
    background: none !important;
  }

  .algolia-docsearch-suggestion--highlight {
    color: #5f2dab;
    background-color: rgba(154, 132, 187, .12);
  }

  .algolia-docsearch-suggestion--text .algolia-docsearch-suggestion--highlight {
    box-shadow: inset 0 -2px 0 0 rgba(95, 45, 171, .5) !important;
  }

  .ds-suggestion.ds-cursor .algolia-docsearch-suggestion--content {
    background-color: rgba(208, 189, 236, .15) !important;
  }
}

{"version": 3, "file": "carousel.js", "sources": ["../src/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.6.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst VERSION = '4.6.2'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ARROW_LEFT_KEYCODE = 37 // KeyboardEvent.which value for left arrow key\nconst ARROW_RIGHT_KEYCODE = 39 // KeyboardEvent.which value for right arrow key\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = this._element.querySelector(SELECTOR_INDICATORS)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent || window.MSPointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    const $element = $(this._element)\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden &&\n      ($element.is(':visible') && $element.css('visibility') !== 'hidden')) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (this._element.querySelector(SELECTOR_NEXT_PREV)) {\n      Util.triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      $(this._element).one(EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    $(this._element).off(EVENT_KEY)\n    $.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      $(this._element).on(EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      $(this._element)\n        .on(EVENT_MOUSEENTER, event => this.pause(event))\n        .on(EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    if (!this._touchSupported) {\n      return\n    }\n\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchStartX = event.originalEvent.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.originalEvent.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      this.touchDeltaX = event.originalEvent.touches && event.originalEvent.touches.length > 1 ?\n        0 :\n        event.originalEvent.touches[0].clientX - this.touchStartX\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.originalEvent.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    $(this._element.querySelectorAll(SELECTOR_ITEM_IMG))\n      .on(EVENT_DRAG_START, e => e.preventDefault())\n\n    if (this._pointerEvent) {\n      $(this._element).on(EVENT_POINTERDOWN, event => start(event))\n      $(this._element).on(EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      $(this._element).on(EVENT_TOUCHSTART, event => start(event))\n      $(this._element).on(EVENT_TOUCHMOVE, event => move(event))\n      $(this._element).on(EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.which) {\n      case ARROW_LEFT_KEYCODE:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEYCODE:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      [].slice.call(element.parentNode.querySelectorAll(SELECTOR_ITEM)) :\n      []\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = isPrevDirection && activeIndex === 0 ||\n                            isNextDirection && activeIndex === lastItemIndex\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] : this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(this._element.querySelector(SELECTOR_ACTIVE_ITEM))\n    const slideEvent = $.Event(EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n\n    $(this._element).trigger(slideEvent)\n\n    return slideEvent\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = [].slice.call(this._indicatorsElement.querySelectorAll(SELECTOR_ACTIVE))\n      $(indicators).removeClass(CLASS_NAME_ACTIVE)\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        $(nextIndicator).addClass(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = this._element.querySelector(SELECTOR_ACTIVE_ITEM)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || activeElement &&\n      this._getItemByDirection(direction, activeElement)\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && $(nextElement).hasClass(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    const slidEvent = $.Event(EVENT_SLID, {\n      relatedTarget: nextElement,\n      direction: eventDirectionName,\n      from: activeElementIndex,\n      to: nextElementIndex\n    })\n\n    if ($(this._element).hasClass(CLASS_NAME_SLIDE)) {\n      $(nextElement).addClass(orderClassName)\n\n      Util.reflow(nextElement)\n\n      $(activeElement).addClass(directionalClassName)\n      $(nextElement).addClass(directionalClassName)\n\n      const transitionDuration = Util.getTransitionDurationFromElement(activeElement)\n\n      $(activeElement)\n        .one(Util.TRANSITION_END, () => {\n          $(nextElement)\n            .removeClass(`${directionalClassName} ${orderClassName}`)\n            .addClass(CLASS_NAME_ACTIVE)\n\n          $(activeElement).removeClass(`${CLASS_NAME_ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n          this._isSliding = false\n\n          setTimeout(() => $(this._element).trigger(slidEvent), 0)\n        })\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      $(activeElement).removeClass(CLASS_NAME_ACTIVE)\n      $(nextElement).addClass(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      $(this._element).trigger(slidEvent)\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      let _config = {\n        ...Default,\n        ...$(this).data()\n      }\n\n      if (typeof config === 'object') {\n        _config = {\n          ..._config,\n          ...config\n        }\n      }\n\n      const action = typeof config === 'string' ? config : _config.slide\n\n      if (!data) {\n        data = new Carousel(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'number') {\n        data.to(config)\n      } else if (typeof action === 'string') {\n        if (typeof data[action] === 'undefined') {\n          throw new TypeError(`No method named \"${action}\"`)\n        }\n\n        data[action]()\n      } else if (_config.interval && _config.ride) {\n        data.pause()\n        data.cycle()\n      }\n    })\n  }\n\n  static _dataApiClickHandler(event) {\n    const selector = Util.getSelectorFromElement(this)\n\n    if (!selector) {\n      return\n    }\n\n    const target = $(selector)[0]\n\n    if (!target || !$(target).hasClass(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel._jQueryInterface.call($(target), config)\n\n    if (slideIndex) {\n      $(target).data(DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * Data API implementation\n */\n\n$(document).on(EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel._dataApiClickHandler)\n\n$(window).on(EVENT_LOAD_DATA_API, () => {\n  const carousels = [].slice.call(document.querySelectorAll(SELECTOR_DATA_RIDE))\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    const $carousel = $(carousels[i])\n    Carousel._jQueryInterface.call($carousel, $carousel.data())\n  }\n})\n\n/**\n * jQuery\n */\n\n$.fn[NAME] = Carousel._jQueryInterface\n$.fn[NAME].Constructor = Carousel\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Carousel._jQueryInterface\n}\n\nexport default Carousel\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ARROW_LEFT_KEYCODE", "ARROW_RIGHT_KEYCODE", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "element", "config", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_element", "_indicatorsElement", "querySelector", "_touchSupported", "document", "documentElement", "navigator", "maxTouchPoints", "_pointerEvent", "Boolean", "window", "PointerEvent", "MSPointerEvent", "_addEventListeners", "next", "_slide", "nextWhenVisible", "$element", "hidden", "is", "css", "prev", "event", "<PERSON><PERSON>", "triggerTransitionEnd", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "length", "one", "direction", "dispose", "off", "removeData", "typeCheckConfig", "_handleSwipe", "absDeltax", "Math", "abs", "on", "_keydown", "_addTouchEventListeners", "start", "originalEvent", "pointerType", "toUpperCase", "clientX", "touches", "move", "end", "clearTimeout", "setTimeout", "querySelectorAll", "e", "preventDefault", "classList", "add", "test", "target", "tagName", "which", "parentNode", "slice", "call", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "Event", "from", "trigger", "_setActiveIndicatorElement", "indicators", "removeClass", "nextIndicator", "children", "addClass", "elementInterval", "parseInt", "getAttribute", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "hasClass", "isDefaultPrevented", "slidEvent", "reflow", "transitionDuration", "getTransitionDurationFromElement", "TRANSITION_END", "emulateTransitionEnd", "_jQueryInterface", "each", "data", "action", "TypeError", "ride", "_dataApiClickHandler", "selector", "getSelectorFromElement", "slideIndex", "carousels", "i", "len", "$carousel", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;EACA;EACA;;EAEA,IAAMA,IAAI,GAAG,UAAb,CAAA;EACA,IAAMC,OAAO,GAAG,OAAhB,CAAA;EACA,IAAMC,QAAQ,GAAG,aAAjB,CAAA;EACA,IAAMC,SAAS,SAAOD,QAAtB,CAAA;EACA,IAAME,YAAY,GAAG,WAArB,CAAA;EACA,IAAMC,kBAAkB,GAAGC,qBAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B,CAAA;EACA,IAAMQ,kBAAkB,GAAG,EAA3B;;EACA,IAAMC,mBAAmB,GAAG,EAA5B;;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAG,EAAxB,CAAA;EAEA,IAAMC,mBAAmB,GAAG,UAA5B,CAAA;EACA,IAAMC,iBAAiB,GAAG,QAA1B,CAAA;EACA,IAAMC,gBAAgB,GAAG,OAAzB,CAAA;EACA,IAAMC,gBAAgB,GAAG,qBAAzB,CAAA;EACA,IAAMC,eAAe,GAAG,oBAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,oBAAxB,CAAA;EACA,IAAMC,eAAe,GAAG,oBAAxB,CAAA;EACA,IAAMC,wBAAwB,GAAG,eAAjC,CAAA;EAEA,IAAMC,cAAc,GAAG,MAAvB,CAAA;EACA,IAAMC,cAAc,GAAG,MAAvB,CAAA;EACA,IAAMC,cAAc,GAAG,MAAvB,CAAA;EACA,IAAMC,eAAe,GAAG,OAAxB,CAAA;EAEA,IAAMC,WAAW,aAAWrB,SAA5B,CAAA;EACA,IAAMsB,UAAU,YAAUtB,SAA1B,CAAA;EACA,IAAMuB,aAAa,eAAavB,SAAhC,CAAA;EACA,IAAMwB,gBAAgB,kBAAgBxB,SAAtC,CAAA;EACA,IAAMyB,gBAAgB,kBAAgBzB,SAAtC,CAAA;EACA,IAAM0B,gBAAgB,kBAAgB1B,SAAtC,CAAA;EACA,IAAM2B,eAAe,iBAAe3B,SAApC,CAAA;EACA,IAAM4B,cAAc,gBAAc5B,SAAlC,CAAA;EACA,IAAM6B,iBAAiB,mBAAiB7B,SAAxC,CAAA;EACA,IAAM8B,eAAe,iBAAe9B,SAApC,CAAA;EACA,IAAM+B,gBAAgB,iBAAe/B,SAArC,CAAA;EACA,IAAMgC,mBAAmB,GAAA,MAAA,GAAUhC,SAAV,GAAsBC,YAA/C,CAAA;EACA,IAAMgC,oBAAoB,GAAA,OAAA,GAAWjC,SAAX,GAAuBC,YAAjD,CAAA;EAEA,IAAMiC,eAAe,GAAG,SAAxB,CAAA;EACA,IAAMC,oBAAoB,GAAG,uBAA7B,CAAA;EACA,IAAMC,aAAa,GAAG,gBAAtB,CAAA;EACA,IAAMC,iBAAiB,GAAG,oBAA1B,CAAA;EACA,IAAMC,kBAAkB,GAAG,0CAA3B,CAAA;EACA,IAAMC,mBAAmB,GAAG,sBAA5B,CAAA;EACA,IAAMC,mBAAmB,GAAG,+BAA5B,CAAA;EACA,IAAMC,kBAAkB,GAAG,wBAA3B,CAAA;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE,IAAA;EANO,CAAhB,CAAA;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE,SAAA;EANW,CAApB,CAAA;EASA,IAAME,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAE,OADW;EAElBC,EAAAA,GAAG,EAAE,KAAA;EAFa,CAApB,CAAA;EAKA;EACA;EACA;;MAEMC;IACJ,SAAYC,QAAAA,CAAAA,OAAZ,EAAqBC,MAArB,EAA6B;MAC3B,IAAKC,CAAAA,MAAL,GAAc,IAAd,CAAA;MACA,IAAKC,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,cAAL,GAAsB,IAAtB,CAAA;MACA,IAAKC,CAAAA,SAAL,GAAiB,KAAjB,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;MACA,IAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;MACA,IAAKC,CAAAA,WAAL,GAAmB,CAAnB,CAAA;MACA,IAAKC,CAAAA,WAAL,GAAmB,CAAnB,CAAA;EAEA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKC,UAAL,CAAgBV,MAAhB,CAAf,CAAA;MACA,IAAKW,CAAAA,QAAL,GAAgBZ,OAAhB,CAAA;MACA,IAAKa,CAAAA,kBAAL,GAA0B,IAAKD,CAAAA,QAAL,CAAcE,aAAd,CAA4B7B,mBAA5B,CAA1B,CAAA;MACA,IAAK8B,CAAAA,eAAL,GAAuB,cAAA,IAAkBC,QAAQ,CAACC,eAA3B,IAA8CC,SAAS,CAACC,cAAV,GAA2B,CAAhG,CAAA;MACA,IAAKC,CAAAA,aAAL,GAAqBC,OAAO,CAACC,MAAM,CAACC,YAAP,IAAuBD,MAAM,CAACE,cAA/B,CAA5B,CAAA;;EAEA,IAAA,IAAA,CAAKC,kBAAL,EAAA,CAAA;EACD;;;;;EAWD;EACAC,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;MACL,IAAI,CAAC,IAAKpB,CAAAA,UAAV,EAAsB;QACpB,IAAKqB,CAAAA,MAAL,CAAYhE,cAAZ,CAAA,CAAA;EACD,KAAA;;;EAGHiE,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;MAChB,IAAMC,QAAQ,GAAGhF,qBAAC,CAAC,KAAK+D,QAAN,CAAlB,CADgB;EAGhB;;EACA,IAAA,IAAI,CAACI,QAAQ,CAACc,MAAV,IACDD,QAAQ,CAACE,EAAT,CAAY,UAAZ,CAAA,IAA2BF,QAAQ,CAACG,GAAT,CAAa,YAAb,CAAA,KAA+B,QAD7D,EACwE;EACtE,MAAA,IAAA,CAAKN,IAAL,EAAA,CAAA;EACD,KAAA;;;EAGHO,EAAAA,MAAAA,CAAAA,OAAA,SAAO,IAAA,GAAA;MACL,IAAI,CAAC,IAAK3B,CAAAA,UAAV,EAAsB;QACpB,IAAKqB,CAAAA,MAAL,CAAY/D,cAAZ,CAAA,CAAA;EACD,KAAA;;;WAGH4B,QAAA,SAAM0C,KAAAA,CAAAA,KAAN,EAAa;MACX,IAAI,CAACA,KAAL,EAAY;QACV,IAAK7B,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,KAAKO,QAAL,CAAcE,aAAd,CAA4B9B,kBAA5B,CAAJ,EAAqD;EACnDmD,MAAAA,wBAAI,CAACC,oBAAL,CAA0B,IAAA,CAAKxB,QAA/B,CAAA,CAAA;QACA,IAAKyB,CAAAA,KAAL,CAAW,IAAX,CAAA,CAAA;EACD,KAAA;;MAEDC,aAAa,CAAC,IAAKnC,CAAAA,SAAN,CAAb,CAAA;MACA,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;;;WAGFkC,QAAA,SAAMH,KAAAA,CAAAA,KAAN,EAAa;MACX,IAAI,CAACA,KAAL,EAAY;QACV,IAAK7B,CAAAA,SAAL,GAAiB,KAAjB,CAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKF,SAAT,EAAoB;QAClBmC,aAAa,CAAC,IAAKnC,CAAAA,SAAN,CAAb,CAAA;QACA,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKO,OAAL,CAAarB,QAAb,IAAyB,CAAC,IAAA,CAAKgB,SAAnC,EAA8C;EAC5C,MAAA,IAAA,CAAKkC,eAAL,EAAA,CAAA;;QAEA,IAAKpC,CAAAA,SAAL,GAAiBqC,WAAW,CAC1B,CAACxB,QAAQ,CAACyB,eAAT,GAA2B,IAAA,CAAKb,eAAhC,GAAkD,KAAKF,IAAxD,EAA8DgB,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,IAAKhC,CAAAA,OAAL,CAAarB,QAFa,CAA5B,CAAA;EAID,KAAA;;;WAGHsD,KAAA,SAAGC,EAAAA,CAAAA,KAAH,EAAU;EAAA,IAAA,IAAA,KAAA,GAAA,IAAA,CAAA;;MACR,IAAKxC,CAAAA,cAAL,GAAsB,IAAKQ,CAAAA,QAAL,CAAcE,aAAd,CAA4BjC,oBAA5B,CAAtB,CAAA;;EAEA,IAAA,IAAMgE,WAAW,GAAG,IAAA,CAAKC,aAAL,CAAmB,IAAA,CAAK1C,cAAxB,CAApB,CAAA;;EAEA,IAAA,IAAIwC,KAAK,GAAG,IAAK1C,CAAAA,MAAL,CAAY6C,MAAZ,GAAqB,CAA7B,IAAkCH,KAAK,GAAG,CAA9C,EAAiD;EAC/C,MAAA,OAAA;EACD,KAAA;;MAED,IAAI,IAAA,CAAKtC,UAAT,EAAqB;QACnBzD,qBAAC,CAAC,KAAK+D,QAAN,CAAD,CAAiBoC,GAAjB,CAAqBhF,UAArB,EAAiC,YAAA;EAAA,QAAA,OAAM,KAAI,CAAC2E,EAAL,CAAQC,KAAR,CAAN,CAAA;SAAjC,CAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,MAAA,IAAA,CAAKpD,KAAL,EAAA,CAAA;EACA,MAAA,IAAA,CAAK6C,KAAL,EAAA,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAMY,SAAS,GAAGL,KAAK,GAAGC,WAAR,GAChBlF,cADgB,GAEhBC,cAFF,CAAA;;MAIA,IAAK+D,CAAAA,MAAL,CAAYsB,SAAZ,EAAuB,KAAK/C,MAAL,CAAY0C,KAAZ,CAAvB,CAAA,CAAA;;;EAGFM,EAAAA,MAAAA,CAAAA,UAAA,SAAU,OAAA,GAAA;EACRrG,IAAAA,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiBuC,GAAjB,CAAqBzG,SAArB,CAAA,CAAA;EACAG,IAAAA,qBAAC,CAACuG,UAAF,CAAa,IAAKxC,CAAAA,QAAlB,EAA4BnE,QAA5B,CAAA,CAAA;MAEA,IAAKyD,CAAAA,MAAL,GAAc,IAAd,CAAA;MACA,IAAKQ,CAAAA,OAAL,GAAe,IAAf,CAAA;MACA,IAAKE,CAAAA,QAAL,GAAgB,IAAhB,CAAA;MACA,IAAKT,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKE,CAAAA,SAAL,GAAiB,IAAjB,CAAA;MACA,IAAKC,CAAAA,UAAL,GAAkB,IAAlB,CAAA;MACA,IAAKF,CAAAA,cAAL,GAAsB,IAAtB,CAAA;MACA,IAAKS,CAAAA,kBAAL,GAA0B,IAA1B,CAAA;EACD;;;WAGDF,aAAA,SAAWV,UAAAA,CAAAA,MAAX,EAAmB;EACjBA,IAAAA,MAAM,GACDb,QAAAA,CAAAA,EAAAA,EAAAA,OADC,EAEDa,MAFC,CAAN,CAAA;EAIAkC,IAAAA,wBAAI,CAACkB,eAAL,CAAqB9G,IAArB,EAA2B0D,MAA3B,EAAmCN,WAAnC,CAAA,CAAA;EACA,IAAA,OAAOM,MAAP,CAAA;;;EAGFqD,EAAAA,MAAAA,CAAAA,eAAA,SAAe,YAAA,GAAA;MACb,IAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,IAAA,CAAKhD,WAAd,CAAlB,CAAA;;MAEA,IAAI8C,SAAS,IAAIrG,eAAjB,EAAkC;EAChC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAM+F,SAAS,GAAGM,SAAS,GAAG,KAAK9C,WAAnC,CAAA;EAEA,IAAA,IAAA,CAAKA,WAAL,GAAmB,CAAnB,CATa;;MAYb,IAAIwC,SAAS,GAAG,CAAhB,EAAmB;EACjB,MAAA,IAAA,CAAKhB,IAAL,EAAA,CAAA;EACD,KAdY;;;MAiBb,IAAIgB,SAAS,GAAG,CAAhB,EAAmB;EACjB,MAAA,IAAA,CAAKvB,IAAL,EAAA,CAAA;EACD,KAAA;;;EAGHD,EAAAA,MAAAA,CAAAA,qBAAA,SAAqB,kBAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;EACnB,IAAA,IAAI,IAAKf,CAAAA,OAAL,CAAapB,QAAjB,EAA2B;QACzBzC,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBzF,aAApB,EAAmC,UAAAiE,KAAK,EAAA;EAAA,QAAA,OAAI,MAAI,CAACyB,QAAL,CAAczB,KAAd,CAAJ,CAAA;SAAxC,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAI,KAAKxB,OAAL,CAAalB,KAAb,KAAuB,OAA3B,EAAoC;QAClC3C,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CACG8C,EADH,CACMxF,gBADN,EACwB,UAAAgE,KAAK,EAAA;EAAA,QAAA,OAAI,MAAI,CAAC1C,KAAL,CAAW0C,KAAX,CAAJ,CAAA;EAAA,OAD7B,EAEGwB,EAFH,CAEMvF,gBAFN,EAEwB,UAAA+D,KAAK,EAAA;EAAA,QAAA,OAAI,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAJ,CAAA;SAF7B,CAAA,CAAA;EAGD,KAAA;;EAED,IAAA,IAAI,IAAKxB,CAAAA,OAAL,CAAahB,KAAjB,EAAwB;EACtB,MAAA,IAAA,CAAKkE,uBAAL,EAAA,CAAA;EACD,KAAA;;;EAGHA,EAAAA,MAAAA,CAAAA,0BAAA,SAA0B,uBAAA,GAAA;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACxB,IAAI,CAAC,IAAK7C,CAAAA,eAAV,EAA2B;EACzB,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAM8C,KAAK,GAAG,SAARA,KAAQ,CAAA3B,KAAK,EAAI;EACrB,MAAA,IAAI,MAAI,CAACd,aAAL,IAAsBxB,WAAW,CAACsC,KAAK,CAAC4B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;EACpF,QAAA,MAAI,CAACxD,WAAL,GAAmB0B,KAAK,CAAC4B,aAAN,CAAoBG,OAAvC,CAAA;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAAC7C,aAAV,EAAyB;UAC9B,MAAI,CAACZ,WAAL,GAAmB0B,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,CAAA,CAA+BD,OAAlD,CAAA;EACD,OAAA;OALH,CAAA;;EAQA,IAAA,IAAME,IAAI,GAAG,SAAPA,IAAO,CAAAjC,KAAK,EAAI;EACpB;EACA,MAAA,MAAI,CAACzB,WAAL,GAAmByB,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,IAA+BhC,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4BnB,MAA5B,GAAqC,CAApE,GACjB,CADiB,GAEjBb,KAAK,CAAC4B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,CAA+BD,CAAAA,OAA/B,GAAyC,MAAI,CAACzD,WAFhD,CAAA;OAFF,CAAA;;EAOA,IAAA,IAAM4D,GAAG,GAAG,SAANA,GAAM,CAAAlC,KAAK,EAAI;EACnB,MAAA,IAAI,MAAI,CAACd,aAAL,IAAsBxB,WAAW,CAACsC,KAAK,CAAC4B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;UACpF,MAAI,CAACvD,WAAL,GAAmByB,KAAK,CAAC4B,aAAN,CAAoBG,OAApB,GAA8B,MAAI,CAACzD,WAAtD,CAAA;EACD,OAAA;;EAED,MAAA,MAAI,CAAC8C,YAAL,EAAA,CAAA;;EACA,MAAA,IAAI,MAAI,CAAC5C,OAAL,CAAalB,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL,EAAA,CAAA;;UACA,IAAI,MAAI,CAACe,YAAT,EAAuB;EACrB8D,UAAAA,YAAY,CAAC,MAAI,CAAC9D,YAAN,CAAZ,CAAA;EACD,SAAA;;EAED,QAAA,MAAI,CAACA,YAAL,GAAoB+D,UAAU,CAAC,UAAApC,KAAK,EAAA;EAAA,UAAA,OAAI,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAJ,CAAA;WAAN,EAA6BjF,sBAAsB,GAAG,MAAI,CAACyD,OAAL,CAAarB,QAAnE,CAA9B,CAAA;EACD,OAAA;OArBH,CAAA;;EAwBAxC,IAAAA,qBAAC,CAAC,IAAA,CAAK+D,QAAL,CAAc2D,gBAAd,CAA+BxF,iBAA/B,CAAD,CAAD,CACG2E,EADH,CACMjF,gBADN,EACwB,UAAA+F,CAAC,EAAA;QAAA,OAAIA,CAAC,CAACC,cAAF,EAAJ,CAAA;OADzB,CAAA,CAAA;;MAGA,IAAI,IAAA,CAAKrD,aAAT,EAAwB;QACtBvE,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBnF,iBAApB,EAAuC,UAAA2D,KAAK,EAAA;UAAA,OAAI2B,KAAK,CAAC3B,KAAD,CAAT,CAAA;SAA5C,CAAA,CAAA;QACArF,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBlF,eAApB,EAAqC,UAAA0D,KAAK,EAAA;UAAA,OAAIkC,GAAG,CAAClC,KAAD,CAAP,CAAA;SAA1C,CAAA,CAAA;;EAEA,MAAA,IAAA,CAAKtB,QAAL,CAAc8D,SAAd,CAAwBC,GAAxB,CAA4BjH,wBAA5B,CAAA,CAAA;EACD,KALD,MAKO;QACLb,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBtF,gBAApB,EAAsC,UAAA8D,KAAK,EAAA;UAAA,OAAI2B,KAAK,CAAC3B,KAAD,CAAT,CAAA;SAA3C,CAAA,CAAA;QACArF,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBrF,eAApB,EAAqC,UAAA6D,KAAK,EAAA;UAAA,OAAIiC,IAAI,CAACjC,KAAD,CAAR,CAAA;SAA1C,CAAA,CAAA;QACArF,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiB8C,EAAjB,CAAoBpF,cAApB,EAAoC,UAAA4D,KAAK,EAAA;UAAA,OAAIkC,GAAG,CAAClC,KAAD,CAAP,CAAA;SAAzC,CAAA,CAAA;EACD,KAAA;;;WAGHyB,WAAA,SAASzB,QAAAA,CAAAA,KAAT,EAAgB;MACd,IAAI,iBAAA,CAAkB0C,IAAlB,CAAuB1C,KAAK,CAAC2C,MAAN,CAAaC,OAApC,CAAJ,EAAkD;EAChD,MAAA,OAAA;EACD,KAAA;;MAED,QAAQ5C,KAAK,CAAC6C,KAAd;EACE,MAAA,KAAKhI,kBAAL;EACEmF,QAAAA,KAAK,CAACuC,cAAN,EAAA,CAAA;EACA,QAAA,IAAA,CAAKxC,IAAL,EAAA,CAAA;EACA,QAAA,MAAA;;EACF,MAAA,KAAKjF,mBAAL;EACEkF,QAAAA,KAAK,CAACuC,cAAN,EAAA,CAAA;EACA,QAAA,IAAA,CAAK/C,IAAL,EAAA,CAAA;EACA,QAAA,MAAA;EARJ,KAAA;;;WAaFoB,gBAAA,SAAc9C,aAAAA,CAAAA,OAAd,EAAuB;MACrB,IAAKE,CAAAA,MAAL,GAAcF,OAAO,IAAIA,OAAO,CAACgF,UAAnB,GACZ,EAAA,CAAGC,KAAH,CAASC,IAAT,CAAclF,OAAO,CAACgF,UAAR,CAAmBT,gBAAnB,CAAoCzF,aAApC,CAAd,CADY,GAEZ,EAFF,CAAA;EAGA,IAAA,OAAO,KAAKoB,MAAL,CAAYiF,OAAZ,CAAoBnF,OAApB,CAAP,CAAA;;;EAGFoF,EAAAA,MAAAA,CAAAA,sBAAA,SAAA,mBAAA,CAAoBnC,SAApB,EAA+BoC,aAA/B,EAA8C;EAC5C,IAAA,IAAMC,eAAe,GAAGrC,SAAS,KAAKtF,cAAtC,CAAA;EACA,IAAA,IAAM4H,eAAe,GAAGtC,SAAS,KAAKrF,cAAtC,CAAA;;EACA,IAAA,IAAMiF,WAAW,GAAG,IAAA,CAAKC,aAAL,CAAmBuC,aAAnB,CAApB,CAAA;;EACA,IAAA,IAAMG,aAAa,GAAG,IAAA,CAAKtF,MAAL,CAAY6C,MAAZ,GAAqB,CAA3C,CAAA;EACA,IAAA,IAAM0C,aAAa,GAAGF,eAAe,IAAI1C,WAAW,KAAK,CAAnC,IACEyC,eAAe,IAAIzC,WAAW,KAAK2C,aAD3D,CAAA;;EAGA,IAAA,IAAIC,aAAa,IAAI,CAAC,KAAK/E,OAAL,CAAajB,IAAnC,EAAyC;EACvC,MAAA,OAAO4F,aAAP,CAAA;EACD,KAAA;;MAED,IAAMK,KAAK,GAAGzC,SAAS,KAAKrF,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD,CAAA;MACA,IAAM+H,SAAS,GAAG,CAAC9C,WAAW,GAAG6C,KAAf,IAAwB,IAAA,CAAKxF,MAAL,CAAY6C,MAAtD,CAAA;MAEA,OAAO4C,SAAS,KAAK,CAAC,CAAf,GACL,IAAKzF,CAAAA,MAAL,CAAY,IAAKA,CAAAA,MAAL,CAAY6C,MAAZ,GAAqB,CAAjC,CADK,GACiC,KAAK7C,MAAL,CAAYyF,SAAZ,CADxC,CAAA;;;EAIFC,EAAAA,MAAAA,CAAAA,qBAAA,SAAA,kBAAA,CAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,IAAA,IAAMC,WAAW,GAAG,IAAA,CAAKjD,aAAL,CAAmB+C,aAAnB,CAApB,CAAA;;EACA,IAAA,IAAMG,SAAS,GAAG,IAAKlD,CAAAA,aAAL,CAAmB,IAAA,CAAKlC,QAAL,CAAcE,aAAd,CAA4BjC,oBAA5B,CAAnB,CAAlB,CAAA;;EACA,IAAA,IAAMoH,UAAU,GAAGpJ,qBAAC,CAACqJ,KAAF,CAAQnI,WAAR,EAAqB;EACtC8H,MAAAA,aAAa,EAAbA,aADsC;EAEtC5C,MAAAA,SAAS,EAAE6C,kBAF2B;EAGtCK,MAAAA,IAAI,EAAEH,SAHgC;EAItCrD,MAAAA,EAAE,EAAEoD,WAAAA;EAJkC,KAArB,CAAnB,CAAA;EAOAlJ,IAAAA,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBH,UAAzB,CAAA,CAAA;EAEA,IAAA,OAAOA,UAAP,CAAA;;;WAGFI,6BAAA,SAA2BrG,0BAAAA,CAAAA,OAA3B,EAAoC;MAClC,IAAI,IAAA,CAAKa,kBAAT,EAA6B;EAC3B,MAAA,IAAMyF,UAAU,GAAG,EAAGrB,CAAAA,KAAH,CAASC,IAAT,CAAc,IAAKrE,CAAAA,kBAAL,CAAwB0D,gBAAxB,CAAyC3F,eAAzC,CAAd,CAAnB,CAAA;EACA/B,MAAAA,qBAAC,CAACyJ,UAAD,CAAD,CAAcC,WAAd,CAA0BnJ,iBAA1B,CAAA,CAAA;;EAEA,MAAA,IAAMoJ,aAAa,GAAG,IAAK3F,CAAAA,kBAAL,CAAwB4F,QAAxB,CACpB,IAAA,CAAK3D,aAAL,CAAmB9C,OAAnB,CADoB,CAAtB,CAAA;;EAIA,MAAA,IAAIwG,aAAJ,EAAmB;EACjB3J,QAAAA,qBAAC,CAAC2J,aAAD,CAAD,CAAiBE,QAAjB,CAA0BtJ,iBAA1B,CAAA,CAAA;EACD,OAAA;EACF,KAAA;;;EAGHmF,EAAAA,MAAAA,CAAAA,kBAAA,SAAkB,eAAA,GAAA;MAChB,IAAMvC,OAAO,GAAG,IAAA,CAAKI,cAAL,IAAuB,IAAKQ,CAAAA,QAAL,CAAcE,aAAd,CAA4BjC,oBAA5B,CAAvC,CAAA;;MAEA,IAAI,CAACmB,OAAL,EAAc;EACZ,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAM2G,eAAe,GAAGC,QAAQ,CAAC5G,OAAO,CAAC6G,YAAR,CAAqB,eAArB,CAAD,EAAwC,EAAxC,CAAhC,CAAA;;EAEA,IAAA,IAAIF,eAAJ,EAAqB;EACnB,MAAA,IAAA,CAAKjG,OAAL,CAAaoG,eAAb,GAA+B,IAAKpG,CAAAA,OAAL,CAAaoG,eAAb,IAAgC,IAAA,CAAKpG,OAAL,CAAarB,QAA5E,CAAA;EACA,MAAA,IAAA,CAAKqB,OAAL,CAAarB,QAAb,GAAwBsH,eAAxB,CAAA;EACD,KAHD,MAGO;EACL,MAAA,IAAA,CAAKjG,OAAL,CAAarB,QAAb,GAAwB,IAAKqB,CAAAA,OAAL,CAAaoG,eAAb,IAAgC,IAAA,CAAKpG,OAAL,CAAarB,QAArE,CAAA;EACD,KAAA;;;EAGHsC,EAAAA,MAAAA,CAAAA,SAAA,SAAA,MAAA,CAAOsB,SAAP,EAAkBjD,OAAlB,EAA2B;EAAA,IAAA,IAAA,MAAA,GAAA,IAAA,CAAA;;MACzB,IAAMqF,aAAa,GAAG,IAAKzE,CAAAA,QAAL,CAAcE,aAAd,CAA4BjC,oBAA5B,CAAtB,CAAA;;EACA,IAAA,IAAMkI,kBAAkB,GAAG,IAAA,CAAKjE,aAAL,CAAmBuC,aAAnB,CAA3B,CAAA;;EACA,IAAA,IAAM2B,WAAW,GAAGhH,OAAO,IAAIqF,aAAa,IAC1C,IAAKD,CAAAA,mBAAL,CAAyBnC,SAAzB,EAAoCoC,aAApC,CADF,CAAA;;EAEA,IAAA,IAAM4B,gBAAgB,GAAG,IAAA,CAAKnE,aAAL,CAAmBkE,WAAnB,CAAzB,CAAA;;EACA,IAAA,IAAME,SAAS,GAAG7F,OAAO,CAAC,IAAA,CAAKlB,SAAN,CAAzB,CAAA;EAEA,IAAA,IAAIgH,oBAAJ,CAAA;EACA,IAAA,IAAIC,cAAJ,CAAA;EACA,IAAA,IAAItB,kBAAJ,CAAA;;MAEA,IAAI7C,SAAS,KAAKtF,cAAlB,EAAkC;EAChCwJ,MAAAA,oBAAoB,GAAG5J,eAAvB,CAAA;EACA6J,MAAAA,cAAc,GAAG5J,eAAjB,CAAA;EACAsI,MAAAA,kBAAkB,GAAGjI,cAArB,CAAA;EACD,KAJD,MAIO;EACLsJ,MAAAA,oBAAoB,GAAG7J,gBAAvB,CAAA;EACA8J,MAAAA,cAAc,GAAG3J,eAAjB,CAAA;EACAqI,MAAAA,kBAAkB,GAAGhI,eAArB,CAAA;EACD,KAAA;;MAED,IAAIkJ,WAAW,IAAInK,qBAAC,CAACmK,WAAD,CAAD,CAAeK,QAAf,CAAwBjK,iBAAxB,CAAnB,EAA+D;QAC7D,IAAKkD,CAAAA,UAAL,GAAkB,KAAlB,CAAA;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAM2F,UAAU,GAAG,IAAKL,CAAAA,kBAAL,CAAwBoB,WAAxB,EAAqClB,kBAArC,CAAnB,CAAA;;EACA,IAAA,IAAIG,UAAU,CAACqB,kBAAX,EAAJ,EAAqC;EACnC,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAI,CAACjC,aAAD,IAAkB,CAAC2B,WAAvB,EAAoC;EAClC;EACA,MAAA,OAAA;EACD,KAAA;;MAED,IAAK1G,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;EAEA,IAAA,IAAI4G,SAAJ,EAAe;EACb,MAAA,IAAA,CAAK1H,KAAL,EAAA,CAAA;EACD,KAAA;;MAED,IAAK6G,CAAAA,0BAAL,CAAgCW,WAAhC,CAAA,CAAA;;MACA,IAAK5G,CAAAA,cAAL,GAAsB4G,WAAtB,CAAA;EAEA,IAAA,IAAMO,SAAS,GAAG1K,qBAAC,CAACqJ,KAAF,CAAQlI,UAAR,EAAoB;EACpC6H,MAAAA,aAAa,EAAEmB,WADqB;EAEpC/D,MAAAA,SAAS,EAAE6C,kBAFyB;EAGpCK,MAAAA,IAAI,EAAEY,kBAH8B;EAIpCpE,MAAAA,EAAE,EAAEsE,gBAAAA;EAJgC,KAApB,CAAlB,CAAA;;MAOA,IAAIpK,qBAAC,CAAC,IAAA,CAAK+D,QAAN,CAAD,CAAiByG,QAAjB,CAA0BhK,gBAA1B,CAAJ,EAAiD;EAC/CR,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwBU,cAAxB,CAAA,CAAA;QAEAjF,wBAAI,CAACqF,MAAL,CAAYR,WAAZ,CAAA,CAAA;EAEAnK,MAAAA,qBAAC,CAACwI,aAAD,CAAD,CAAiBqB,QAAjB,CAA0BS,oBAA1B,CAAA,CAAA;EACAtK,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwBS,oBAAxB,CAAA,CAAA;EAEA,MAAA,IAAMM,kBAAkB,GAAGtF,wBAAI,CAACuF,gCAAL,CAAsCrC,aAAtC,CAA3B,CAAA;QAEAxI,qBAAC,CAACwI,aAAD,CAAD,CACGrC,GADH,CACOb,wBAAI,CAACwF,cADZ,EAC4B,YAAM;EAC9B9K,QAAAA,qBAAC,CAACmK,WAAD,CAAD,CACGT,WADH,CACkBY,oBADlB,GAAA,GAAA,GAC0CC,cAD1C,CAAA,CAEGV,QAFH,CAEYtJ,iBAFZ,CAAA,CAAA;UAIAP,qBAAC,CAACwI,aAAD,CAAD,CAAiBkB,WAAjB,CAAgCnJ,iBAAhC,GAAA,GAAA,GAAqDgK,cAArD,GAAA,GAAA,GAAuED,oBAAvE,CAAA,CAAA;UAEA,MAAI,CAAC7G,UAAL,GAAkB,KAAlB,CAAA;EAEAgE,QAAAA,UAAU,CAAC,YAAA;YAAA,OAAMzH,qBAAC,CAAC,MAAI,CAAC+D,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBmB,SAAzB,CAAN,CAAA;WAAD,EAA4C,CAA5C,CAAV,CAAA;SAVJ,CAAA,CAYGK,oBAZH,CAYwBH,kBAZxB,CAAA,CAAA;EAaD,KAvBD,MAuBO;EACL5K,MAAAA,qBAAC,CAACwI,aAAD,CAAD,CAAiBkB,WAAjB,CAA6BnJ,iBAA7B,CAAA,CAAA;EACAP,MAAAA,qBAAC,CAACmK,WAAD,CAAD,CAAeN,QAAf,CAAwBtJ,iBAAxB,CAAA,CAAA;QAEA,IAAKkD,CAAAA,UAAL,GAAkB,KAAlB,CAAA;EACAzD,MAAAA,qBAAC,CAAC,IAAK+D,CAAAA,QAAN,CAAD,CAAiBwF,OAAjB,CAAyBmB,SAAzB,CAAA,CAAA;EACD,KAAA;;EAED,IAAA,IAAIL,SAAJ,EAAe;EACb,MAAA,IAAA,CAAK7E,KAAL,EAAA,CAAA;EACD,KAAA;EACF;;;aAGMwF,mBAAP,SAAwB5H,gBAAAA,CAAAA,MAAxB,EAAgC;MAC9B,OAAO,IAAA,CAAK6H,IAAL,CAAU,YAAY;QAC3B,IAAIC,IAAI,GAAGlL,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,CAAatL,QAAb,CAAX,CAAA;;QACA,IAAIiE,OAAO,GACNtB,QAAAA,CAAAA,EAAAA,EAAAA,OADM,EAENvC,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,EAFM,CAAX,CAAA;;EAKA,MAAA,IAAI,OAAO9H,MAAP,KAAkB,QAAtB,EAAgC;EAC9BS,QAAAA,OAAO,GACFA,QAAAA,CAAAA,EAAAA,EAAAA,OADE,EAEFT,MAFE,CAAP,CAAA;EAID,OAAA;;QAED,IAAM+H,MAAM,GAAG,OAAO/H,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCS,OAAO,CAACnB,KAA7D,CAAA;;QAEA,IAAI,CAACwI,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIhI,QAAJ,CAAa,IAAb,EAAmBW,OAAnB,CAAP,CAAA;UACA7D,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,CAAatL,QAAb,EAAuBsL,IAAvB,CAAA,CAAA;EACD,OAAA;;EAED,MAAA,IAAI,OAAO9H,MAAP,KAAkB,QAAtB,EAAgC;UAC9B8H,IAAI,CAACpF,EAAL,CAAQ1C,MAAR,CAAA,CAAA;EACD,OAFD,MAEO,IAAI,OAAO+H,MAAP,KAAkB,QAAtB,EAAgC;EACrC,QAAA,IAAI,OAAOD,IAAI,CAACC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,UAAA,MAAM,IAAIC,SAAJ,CAAkCD,oBAAAA,GAAAA,MAAlC,GAAN,IAAA,CAAA,CAAA;EACD,SAAA;;UAEDD,IAAI,CAACC,MAAD,CAAJ,EAAA,CAAA;SALK,MAMA,IAAItH,OAAO,CAACrB,QAAR,IAAoBqB,OAAO,CAACwH,IAAhC,EAAsC;EAC3CH,QAAAA,IAAI,CAACvI,KAAL,EAAA,CAAA;EACAuI,QAAAA,IAAI,CAAC1F,KAAL,EAAA,CAAA;EACD,OAAA;EACF,KAjCM,CAAP,CAAA;;;aAoCK8F,uBAAP,SAA4BjG,oBAAAA,CAAAA,KAA5B,EAAmC;EACjC,IAAA,IAAMkG,QAAQ,GAAGjG,wBAAI,CAACkG,sBAAL,CAA4B,IAA5B,CAAjB,CAAA;;MAEA,IAAI,CAACD,QAAL,EAAe;EACb,MAAA,OAAA;EACD,KAAA;;MAED,IAAMvD,MAAM,GAAGhI,qBAAC,CAACuL,QAAD,CAAD,CAAY,CAAZ,CAAf,CAAA;;EAEA,IAAA,IAAI,CAACvD,MAAD,IAAW,CAAChI,qBAAC,CAACgI,MAAD,CAAD,CAAUwC,QAAV,CAAmBlK,mBAAnB,CAAhB,EAAyD;EACvD,MAAA,OAAA;EACD,KAAA;;EAED,IAAA,IAAM8C,MAAM,GACPpD,QAAAA,CAAAA,EAAAA,EAAAA,qBAAC,CAACgI,MAAD,CAAD,CAAUkD,IAAV,EADO,EAEPlL,qBAAC,CAAC,IAAD,CAAD,CAAQkL,IAAR,EAFO,CAAZ,CAAA;;EAIA,IAAA,IAAMO,UAAU,GAAG,IAAA,CAAKzB,YAAL,CAAkB,eAAlB,CAAnB,CAAA;;EAEA,IAAA,IAAIyB,UAAJ,EAAgB;QACdrI,MAAM,CAACZ,QAAP,GAAkB,KAAlB,CAAA;EACD,KAAA;;MAEDU,QAAQ,CAAC8H,gBAAT,CAA0B3C,IAA1B,CAA+BrI,qBAAC,CAACgI,MAAD,CAAhC,EAA0C5E,MAA1C,CAAA,CAAA;;EAEA,IAAA,IAAIqI,UAAJ,EAAgB;QACdzL,qBAAC,CAACgI,MAAD,CAAD,CAAUkD,IAAV,CAAetL,QAAf,CAAA,CAAyBkG,EAAzB,CAA4B2F,UAA5B,CAAA,CAAA;EACD,KAAA;;EAEDpG,IAAAA,KAAK,CAACuC,cAAN,EAAA,CAAA;;;;;WA5cF,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAOjI,OAAP,CAAA;EACD,KAAA;;;WAED,SAAqB,GAAA,GAAA;EACnB,MAAA,OAAO4C,OAAP,CAAA;EACD,KAAA;;;;;EA0cH;EACA;EACA;;;AAEAvC,uBAAC,CAACmE,QAAD,CAAD,CAAY0C,EAAZ,CAAe/E,oBAAf,EAAqCO,mBAArC,EAA0Da,QAAQ,CAACoI,oBAAnE,CAAA,CAAA;AAEAtL,uBAAC,CAACyE,MAAD,CAAD,CAAUoC,EAAV,CAAahF,mBAAb,EAAkC,YAAM;EACtC,EAAA,IAAM6J,SAAS,GAAG,EAAGtD,CAAAA,KAAH,CAASC,IAAT,CAAclE,QAAQ,CAACuD,gBAAT,CAA0BpF,kBAA1B,CAAd,CAAlB,CAAA;;EACA,EAAA,KAAK,IAAIqJ,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,SAAS,CAACxF,MAAhC,EAAwCyF,CAAC,GAAGC,GAA5C,EAAiDD,CAAC,EAAlD,EAAsD;MACpD,IAAME,SAAS,GAAG7L,qBAAC,CAAC0L,SAAS,CAACC,CAAD,CAAV,CAAnB,CAAA;;MACAzI,QAAQ,CAAC8H,gBAAT,CAA0B3C,IAA1B,CAA+BwD,SAA/B,EAA0CA,SAAS,CAACX,IAAV,EAA1C,CAAA,CAAA;EACD,GAAA;EACF,CAND,CAAA,CAAA;EAQA;EACA;EACA;;AAEAlL,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAawD,GAAAA,QAAQ,CAAC8H,gBAAtB,CAAA;AACAhL,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWoM,CAAAA,WAAX,GAAyB5I,QAAzB,CAAA;;AACAlD,uBAAC,CAACC,EAAF,CAAKP,IAAL,CAAWqM,CAAAA,UAAX,GAAwB,YAAM;EAC5B/L,EAAAA,qBAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb,CAAA;IACA,OAAOmD,QAAQ,CAAC8H,gBAAhB,CAAA;EACD,CAHD;;;;;;;;"}
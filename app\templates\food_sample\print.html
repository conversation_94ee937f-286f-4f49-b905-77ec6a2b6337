<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>留样记录打印</title>
    <style>
        body {
            font-family: SimSun, "宋体", "Microsoft YaHei", "微软雅黑", sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 14pt;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 18pt;
            margin-bottom: 20px;
        }
        .info {
            margin-bottom: 20px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .row {
            display: flex;
            justify-content: space-between;
        }
        .col {
            width: 48%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            width: 30%;
            background-color: #f2f2f2;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 80%;
            max-height: 300px;
            border: 1px solid #ddd;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            margin-top: 50px;
        }
        @media print {
            body {
                padding: 0;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">{{ project_name|default('初中毕业生学生去向管理系统') }}</div>
            <div class="subtitle">食品留样记录单</div>
        </div>

        <div class="row">
            <div class="col">
                <table>
                    <tr>
                        <th>留样编号</th>
                        <td>{{ food_sample.sample_number }}</td>
                    </tr>
                    <tr>
                        <th>区域</th>
                        <td>{{ food_sample.area.name }}</td>
                    </tr>
                    <tr>
                        <th>食谱名称</th>
                        <td>{{ food_sample.recipe.name }}</td>
                    </tr>
                    <tr>
                        <th>用餐日期</th>
                        <td>{{ food_sample.meal_date }}</td>
                    </tr>
                    <tr>
                        <th>餐次</th>
                        <td>{{ food_sample.meal_type }}</td>
                    </tr>
                    <tr>
                        <th>留样数量</th>
                        <td>{{ food_sample.sample_quantity }} {{ food_sample.sample_unit }}</td>
                    </tr>
                </table>
            </div>
            <div class="col">
                <table>
                    <tr>
                        <th>存储位置</th>
                        <td>{{ food_sample.storage_location }}</td>
                    </tr>
                    <tr>
                        <th>存储温度</th>
                        <td>{{ food_sample.storage_temperature }}</td>
                    </tr>
                    <tr>
                        <th>留样时间</th>
                        <td>{{  food_sample.start_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                    </tr>
                    <tr>
                        <th>留样结束时间</th>
                        <td>{{  food_sample.end_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                    </tr>
                    <tr>
                        <th>状态</th>
                        <td>{{ food_sample.status }}</td>
                    </tr>
                    <tr>
                        <th>操作员</th>
                        <td>{{ food_sample.operator.real_name or food_sample.operator.username }}</td>
                    </tr>
                </table>
            </div>
        </div>

        {% if food_sample.status == '已销毁' %}
        <table>
            <tr>
                <th>销毁时间</th>
                <td>{{  food_sample.destruction_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
            </tr>
            <tr>
                <th>销毁操作员</th>
                <td>{{ food_sample.destruction_operator.real_name or food_sample.destruction_operator.username }}</td>
            </tr>
        </table>
        {% endif %}

        {% if food_sample.sample_image %}
        <div class="image-container">
            <h3>留样图片</h3>
            <img src="{{ url_for('static', filename=food_sample.sample_image, _external=True) }}" alt="留样图片">
        </div>
        {% endif %}

        <div class="footer">
            <div>
                <div><strong>留样人：</strong>{{ food_sample.operator.real_name or food_sample.operator.username }}</div>
                <div class="signature">签名：________________</div>
            </div>
            {% if food_sample.status == '已销毁' %}
            <div>
                <div><strong>销毁人：</strong>{{ food_sample.destruction_operator.real_name or food_sample.destruction_operator.username }}</div>
                <div class="signature">签名：________________</div>
            </div>
            {% endif %}
            <div>
                <div><strong>日期：</strong>{{  food_sample.created_at|format_datetime('%Y年%m月%d日')  }}</div>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
